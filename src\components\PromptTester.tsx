
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Play, Settings, Loader2, Plus, Trash2, AlertCircle, ChevronDown, ChevronRight } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { promptTestingService, PromptTestResult } from '@/lib/prompt-testing'; // Added PromptTestResult
import { aiService } from '@/lib/ai-service';
import { useLoading } from '@/lib/performance'; // Import useLoading

interface PromptTesterProps {
  onResults: (results: any) => void;
}

export const PromptTester = ({ onResults }: PromptTesterProps) => {
  const [prompt, setPrompt] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');
  // const [isLoading, setIsLoading] = useState(false); // Replaced by useLoading
  const { isLoading: isTestRunning, setLoading: setIsTestRunning } = useLoading('prompt-tester-run');
  const [selectedModels, setSelectedModels] = useState<Record<string, boolean>>({});
  const [temperature, setTemperature] = useState(0.7);
  const [maxTokens, setMaxTokens] = useState(1024);
  const [variables, setVariables] = useState<Array<{ key: string; value: string }>>([]);
  const [availableModels, setAvailableModels] = useState<Array<{ id: string; name: string; provider: string }>>([]);
  const [lastResults, setLastResults] = useState<any>(null); // This will hold PromptTestResult

  // New state for sandbox metadata
  const [testGoal, setTestGoal] = useState('');
  const [expectedOutputFormat, setExpectedOutputFormat] = useState('');
  const [configuredProviders, setConfiguredProviders] = useState<Set<string>>(new Set());
  const [expandedProviders, setExpandedProviders] = useState<Record<string, boolean>>({});
  const [expandedModelTypes, setExpandedModelTypes] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // Check which providers are configured
    const configured = new Set<string>();
    if (aiService.isConfigured('openai')) configured.add('OpenAI');
    if (aiService.isConfigured('anthropic')) configured.add('Anthropic');
    if (aiService.isConfigured('google')) configured.add('Google');
    if (aiService.isConfigured('mistral')) configured.add('Mistral');
    
    setConfiguredProviders(configured);
    console.log('🔧 Configured providers:', Array.from(configured));

    // Load available models from the service with error handling
    try {
      const models = promptTestingService.getAvailableModels();
      setAvailableModels(models);

      // Set default selections only for configured providers
      const defaultSelections: Record<string, boolean> = {};
      models.forEach(model => {
        // Only select models if their provider is configured
        defaultSelections[model.id] = configured.has(model.provider);
      });
      
      setSelectedModels(defaultSelections);
      console.log('🎯 Default model selections:', defaultSelections);
    } catch (error) {
      console.error('Failed to load available models:', error);
      // Fallback to basic models
      const fallbackModels = [
        { id: 'openai:gpt-4o', name: 'GPT-4o', provider: 'OpenAI' },
        { id: 'anthropic:claude-3-5-sonnet-latest', name: 'Claude 3.5 Sonnet', provider: 'Anthropic' },
        { id: 'google:gemini-2.5-flash', name: 'Gemini 2.5 Flash', provider: 'Google' }
      ];
      setAvailableModels(fallbackModels);
      
      // Only select configured providers
      const fallbackSelections: Record<string, boolean> = {};
      fallbackModels.forEach(model => {
        fallbackSelections[model.id] = configured.has(model.provider);
      });
      setSelectedModels(fallbackSelections);
    }
  }, []);

  const handleModelToggle = (modelId: string) => {
    setSelectedModels(prev => ({
      ...prev,
      [modelId]: !prev[modelId]
    }));
  };

  const toggleProvider = (provider: string) => {
    setExpandedProviders(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  const toggleModelType = (key: string) => {
    setExpandedModelTypes(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleAddVariable = () => {
    setVariables(prev => [...prev, { key: '', value: '' }]);
  };

  const handleRemoveVariable = (index: number) => {
    setVariables(prev => prev.filter((_, i) => i !== index));
  };

  const handleVariableChange = (index: number, field: 'key' | 'value', value: string) => {
    setVariables(prev => prev.map((variable, i) =>
      i === index ? { ...variable, [field]: value } : variable
    ));
  };

  const handleRunTest = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt to test",
        variant: "destructive"
      });
      return;
    }

    const activeModels = Object.entries(selectedModels)
      .filter(([_, selected]) => selected)
      .map(([id]) => id);

    if (activeModels.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one model",
        variant: "destructive"
      });
      return;
    }

    // Check if selected models have configured providers
    const selectedModelObjects = availableModels.filter(model => 
      activeModels.includes(model.id)
    );
    
    const unconfiguredModels = selectedModelObjects.filter(model => 
      !configuredProviders.has(model.provider)
    );

    if (unconfiguredModels.length > 0) {
      toast({
        title: "Configuration Error",
        description: `Please configure API keys for: ${unconfiguredModels.map(m => m.provider).join(', ')}`,
        variant: "destructive"
      });
      return;
    }

    setIsTestRunning(true); // Use setLoading from useLoading

    try {
      // Convert variables array to object
      const variablesObject = variables.reduce((acc, { key, value }) => {
        if (key.trim()) {
          acc[key.trim()] = value;
        }
        return acc;
      }, {} as Record<string, string>);

      const testResult = await promptTestingService.runPromptTest({
        prompt,
        systemPrompt: systemPrompt || undefined,
        models: activeModels,
        temperature,
        maxTokens,
        variables: Object.keys(variablesObject).length > 0 ? variablesObject : undefined,
        testGoal: testGoal || undefined, // Pass new metadata
        expectedOutputFormat: expectedOutputFormat || undefined // Pass new metadata
      });

      // The testResult object (PromptTestResult) now contains testGoal and expectedOutputFormat.
      // This entire object is passed to onResults.
      // The mapping to TestResult's runMetadata will happen in Index.tsx's handleResults or promptStore.

      // Store results locally for display
      const storeResult = {
        id: testResult.id,
        timestamp: testResult.timestamp,
        prompt: testResult.prompt,
        systemPrompt: testResult.systemPrompt,
        variables: testResult.variables,
        results: testResult.results.map(result => ({
          model: result.model,
          provider: result.provider,
          response: result.response,
          timeToCompletion: result.timeToCompletion,
          tokens: result.tokens.total,
          score: result.score,
          error: result.error
        })),
        summary: testResult.summary,
        metadata: testResult.metadata
      };

      // Store results locally for display
      setLastResults(storeResult);

      onResults(storeResult);

      console.log('🎯 PromptTester: Test completed, results:', storeResult);
      console.log('🎯 PromptTester: Calling onResults with:', storeResult);

      toast({
        title: "Test Complete",
        description: `Successfully tested across ${testResult.metadata.successfulTests}/${testResult.metadata.totalModels} models`,
      });
    } catch (error) {
      console.error('Prompt test failed:', error);
      toast({
        title: "Test Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsTestRunning(false); // Use setLoading from useLoading
    }
  };

  // Group and organize models by provider and type
  const organizeModelsByProvider = () => {
    const providerGroups: Record<string, Record<string, Array<{ id: string; name: string; provider: string }>>> = {};
    
    availableModels.forEach(model => {
      if (!providerGroups[model.provider]) {
        providerGroups[model.provider] = {};
      }
      
      // Group models by type within each provider
      let modelType = 'Other';
      const modelName = model.name.toLowerCase();
      
      if (model.provider === 'OpenAI') {
        if (modelName.includes('o1') || modelName.includes('o3')) {
          modelType = 'Reasoning Models (O-series)';
        } else if (modelName.includes('gpt-4o')) {
          modelType = 'GPT-4o Series';
        } else if (modelName.includes('gpt-4')) {
          modelType = 'GPT-4 Series';
        } else if (modelName.includes('gpt-3.5')) {
          modelType = 'GPT-3.5 Series';
        }
      } else if (model.provider === 'Anthropic') {
        if (modelName.includes('claude-4') || modelName.includes('opus-4') || modelName.includes('sonnet-4')) {
          modelType = 'Claude 4 Series';
        } else if (modelName.includes('claude-3.7')) {
          modelType = 'Claude 3.7 Series';
        } else if (modelName.includes('claude-3.5')) {
          modelType = 'Claude 3.5 Series';
        } else if (modelName.includes('claude-3')) {
          modelType = 'Claude 3 Series';
        }
      } else if (model.provider === 'Google') {
        if (modelName.includes('gemini-2.5')) {
          modelType = 'Gemini 2.5 Series';
        } else if (modelName.includes('gemini-2.0')) {
          modelType = 'Gemini 2.0 Series';
        } else if (modelName.includes('gemini-1.5')) {
          modelType = 'Gemini 1.5 Series';
        }
      } else if (model.provider === 'Mistral') {
        if (modelName.includes('magistral')) {
          modelType = 'Magistral Series';
        } else if (modelName.includes('codestral')) {
          modelType = 'Codestral Series';
        } else if (modelName.includes('devstral')) {
          modelType = 'Devstral Series';
        } else if (modelName.includes('ministral')) {
          modelType = 'Ministral Series';
        } else if (modelName.includes('mistral-large')) {
          modelType = 'Mistral Large';
        } else if (modelName.includes('mistral-medium')) {
          modelType = 'Mistral Medium';
        } else if (modelName.includes('mistral-small')) {
          modelType = 'Mistral Small';
        }
      }
      
      if (!providerGroups[model.provider][modelType]) {
        providerGroups[model.provider][modelType] = [];
      }
      
      providerGroups[model.provider][modelType].push(model);
    });
    
    return providerGroups;
  };

  const organizedModels = organizeModelsByProvider();
  const selectedCount = Object.values(selectedModels).filter(Boolean).length;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Prompt Input */}
        <div className="lg:col-span-2 space-y-4">
          <Tabs defaultValue="prompt" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="prompt">Prompt</TabsTrigger>
              <TabsTrigger value="variables">Variables</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="prompt" className="space-y-4">
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="system-prompt" className="text-slate-200">System Prompt (Optional)</Label>
                    <Textarea
                      id="system-prompt"
                      placeholder="Enter system instructions here..."
                      value={systemPrompt}
                      onChange={(e) => setSystemPrompt(e.target.value)}
                      className="mt-2 bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 min-h-[100px]"
                    />
                  </div>

                  <div>
                    <Label htmlFor="user-prompt" className="text-slate-200">User Prompt *</Label>
                    <Textarea
                      id="user-prompt"
                      placeholder="Enter your prompt to test across multiple models... Use {{variable}} for dynamic content."
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="mt-2 bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 min-h-[200px]"
                    />
                  </div>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="variables" className="space-y-4">
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-slate-200">Variables</h3>
                  <Button onClick={handleAddVariable} size="sm" variant="outline">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Variable
                  </Button>
                </div>

                <div className="space-y-3">
                  {variables.map((variable, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        placeholder="Variable name"
                        value={variable.key}
                        onChange={(e) => handleVariableChange(index, 'key', e.target.value)}
                        className="bg-slate-900/50 border-slate-600 text-white"
                      />
                      <Input
                        placeholder="Value"
                        value={variable.value}
                        onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                        className="bg-slate-900/50 border-slate-600 text-white"
                      />
                      <Button
                        onClick={() => handleRemoveVariable(index)}
                        size="sm"
                        variant="ghost"
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}

                  {variables.length === 0 && (
                    <p className="text-slate-400 text-sm">
                      No variables defined. Use {"{{variableName}}"} in your prompt and define values here.
                    </p>
                  )}
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <div className="space-y-6">
                  <div>
                    <Label className="text-slate-200">Temperature: {temperature}</Label>
                    <Slider
                      value={[temperature]}
                      onValueChange={([value]) => setTemperature(value)}
                      max={2}
                      min={0}
                      step={0.1}
                      className="mt-2"
                    />
                    <p className="text-xs text-slate-400 mt-1">
                      Controls randomness. Lower values make responses more focused.
                    </p>
                  </div>

                  <div>
                    <Label className="text-slate-200">Max Tokens: {maxTokens}</Label>
                    <Slider
                      value={[maxTokens]}
                      onValueChange={([value]) => setMaxTokens(value)}
                      max={4096}
                      min={100}
                      step={100}
                      className="mt-2"
                    />
                    <p className="text-xs text-slate-400 mt-1">
                      Maximum number of tokens in the response.
                    </p>
                  </div>

                  <div className="border-t border-slate-700 pt-6 space-y-6">
                    <div>
                      <Label htmlFor="test-goal" className="text-slate-200">Test Goal (Optional)</Label>
                      <Input
                        id="test-goal"
                        value={testGoal}
                        onChange={(e) => setTestGoal(e.target.value)}
                        placeholder="e.g., Evaluate response clarity for a non-technical audience"
                        className="mt-2 bg-slate-900/50 border-slate-600 text-white placeholder-slate-400"
                      />
                      <p className="text-xs text-slate-400 mt-1">
                        Describe the primary objective of this test run.
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="expected-output-format" className="text-slate-200">Expected Output Format (Optional)</Label>
                      <Input
                        id="expected-output-format"
                        value={expectedOutputFormat}
                        onChange={(e) => setExpectedOutputFormat(e.target.value)}
                        placeholder="e.g., JSON, Markdown list, Number"
                        className="mt-2 bg-slate-900/50 border-slate-600 text-white placeholder-slate-400"
                      />
                      <p className="text-xs text-slate-400 mt-1">
                        Specify the expected format of the model's response for this test.
                      </p>
                    </div>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Model Selection */}
        <div className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-200">Select Models</h3>
              <Badge variant="outline" className="text-slate-300 border-slate-600">
                {selectedCount} selected
              </Badge>
            </div>
            
            {configuredProviders.size === 0 && (
              <div className="mb-4 p-3 bg-amber-900/20 border border-amber-700 rounded-md">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-amber-500" />
                  <p className="text-sm text-amber-200">
                    No API keys configured. Go to Settings → AI Configuration to add your API keys.
                  </p>
                </div>
              </div>
            )}

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {Object.entries(organizedModels).map(([provider, modelGroups]) => (
                <Collapsible 
                  key={provider} 
                  open={expandedProviders[provider] || false}
                  onOpenChange={() => toggleProvider(provider)}
                >
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-2 rounded-md hover:bg-slate-700/50 transition-colors">
                    <div className="flex items-center gap-2">
                      {expandedProviders[provider] ? 
                        <ChevronDown className="h-4 w-4 text-slate-400" /> : 
                        <ChevronRight className="h-4 w-4 text-slate-400" />
                      }
                      <h4 className="font-medium text-slate-300">{provider}</h4>
                      <Badge 
                        variant={configuredProviders.has(provider) ? "default" : "secondary"}
                        className={configuredProviders.has(provider) ? "bg-green-600" : "bg-slate-600"}
                      >
                        {configuredProviders.has(provider) ? "Configured" : "Not Configured"}
                      </Badge>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="ml-6 mt-2 space-y-2">
                    {Object.entries(modelGroups).map(([modelType, models]) => {
                      const modelTypeKey = `${provider}-${modelType}`;
                      return (
                        <Collapsible 
                          key={modelType}
                          open={expandedModelTypes[modelTypeKey] || false}
                          onOpenChange={() => toggleModelType(modelTypeKey)}
                        >
                          <CollapsibleTrigger className="flex items-center gap-2 w-full p-1 rounded-md hover:bg-slate-700/30 transition-colors text-left">
                            {expandedModelTypes[modelTypeKey] ? 
                              <ChevronDown className="h-3 w-3 text-slate-500" /> : 
                              <ChevronRight className="h-3 w-3 text-slate-500" />
                            }
                            <h5 className="text-sm font-medium text-slate-400">{modelType}</h5>
                            <Badge variant="outline" className="text-xs text-slate-500 border-slate-600">
                              {models.length}
                            </Badge>
                          </CollapsibleTrigger>
                          
                          <CollapsibleContent className="ml-5 mt-1 space-y-2">
                            {models.map((model) => (
                              <div key={model.id} className="flex items-center space-x-3 p-1">
                                <Checkbox
                                  id={model.id}
                                  checked={selectedModels[model.id] || false}
                                  onCheckedChange={() => handleModelToggle(model.id)}
                                  disabled={!configuredProviders.has(model.provider)}
                                  className="border-slate-500"
                                />
                                <div className="flex-1">
                                  <Label 
                                    htmlFor={model.id} 
                                    className={`cursor-pointer text-sm ${
                                      configuredProviders.has(model.provider) ? 'text-slate-200' : 'text-slate-500'
                                    }`}
                                  >
                                    {model.name}
                                  </Label>
                                </div>
                              </div>
                            ))}
                          </CollapsibleContent>
                        </Collapsible>
                      );
                    })}
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
          </Card>

          <Button
            onClick={handleRunTest}
            disabled={isTestRunning || configuredProviders.size === 0}
            className="w-full bg-gradient-to-r from-brand-blue to-brand-purple hover:from-brand-blue/90 hover:to-brand-purple/90 text-white"
            size="lg"
          >
            {isTestRunning ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Running Test...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Run Prompt Test
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Results Display */}
      {lastResults && (
        <Card className="bg-slate-800/50 border-slate-700 p-6 mt-6">
          <h3 className="text-xl font-semibold mb-4 text-slate-200">Test Results</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {lastResults.results?.map((result: any, index: number) => (
                <Card key={index} className="bg-slate-900/50 border-slate-600 p-4">
                  <div className="mb-3">
                    <h4 className="font-semibold text-slate-200">{result.model}</h4>
                    <p className="text-xs text-slate-400">{result.provider}</p>
                  </div>

                  {result.error ? (
                    <div className="text-red-400 text-sm">
                      Error: {result.error}
                    </div>
                  ) : (
                    <>
                      <div className="mb-3 p-3 bg-slate-800 rounded text-sm text-slate-300 max-h-32 overflow-y-auto">
                        {result.response || 'No response'}
                      </div>

                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-slate-400">Tokens:</span>
                          <span className="text-slate-300">{result.tokens || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">Time:</span>
                          <span className="text-slate-300">{result.timeToCompletion || 0}ms</span>
                        </div>
                        {result.score && (
                          <div className="flex justify-between">
                            <span className="text-slate-400">Score:</span>
                            <span className="text-slate-300">{Math.round(result.score.fidelity || 0)}%</span>
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </Card>
              )) || <div className="text-slate-400">No results to display</div>}
            </div>

            {lastResults.summary && (
              <div className="mt-4 p-4 bg-slate-900/50 rounded border border-slate-600">
                <h4 className="font-semibold text-slate-200 mb-2">Summary</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-slate-400">Total Tokens:</span>
                    <span className="text-slate-300 ml-2">{lastResults.summary.totalTokens || 0}</span>
                  </div>
                  <div>
                    <span className="text-slate-400">Avg Score:</span>
                    <span className="text-slate-300 ml-2">{Math.round(lastResults.summary.averageScore || 0)}%</span>
                  </div>
                  <div>
                    <span className="text-slate-400">Duration:</span>
                    <span className="text-slate-300 ml-2">{lastResults.metadata?.duration || 0}ms</span>
                  </div>
                  <div>
                    <span className="text-slate-400">Success Rate:</span>
                    <span className="text-slate-300 ml-2">
                      {lastResults.metadata?.successfulTests || 0}/{lastResults.metadata?.totalModels || 0}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};
