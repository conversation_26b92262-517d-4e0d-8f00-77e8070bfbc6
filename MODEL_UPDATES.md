# AI Model Updates - June 2025

## Overview
Updated all AI provider model listings to reflect the latest available models as of June 2025. This addresses the Gemini 2.5 Flash error and ensures all providers have current, working model names.

## Issue Fixed
**Problem**: `gemini-2.5-pro-exp-1219` was returning a 404 error:
```
"Connection failed: Google API error (404): models/gemini-2.5-pro-exp-1219 is not found for API version v1beta, or is not supported for generateContent."
```

**Root Cause**: The experimental model `gemini-2.5-pro-exp-1219` was deprecated and replaced with stable versions.

**Solution**: Updated to use current stable Gemini models like `gemini-2.5-pro` and `gemini-2.5-flash`.

## Updated Model Listings

### OpenAI Models
- **Latest O-series**: `o3-mini-2025-01-31`, `o1`, `o1-2024-12-17`, `o1-mini`, `o1-preview`
- **Latest GPT-4o**: `gpt-4o`, `gpt-4o-2024-11-20`, `gpt-4o-mini`, `chatgpt-4o-latest`
- **Legacy**: `gpt-4-turbo`, `gpt-3.5-turbo`

### Anthropic Models
- **<PERSON> 4**: `claude-opus-4-20250514`, `claude-sonnet-4-20250514`
- **Claude 3.7**: `claude-3-7-sonnet-20250219` (with extended thinking)
- **Claude 3.5**: `claude-3-5-sonnet-20241022`, `claude-3-5-haiku-20241022`
- **Claude 3**: `claude-3-opus-20240229`, `claude-3-sonnet-20240229`, `claude-3-haiku-20240307`

### Google Models
- **Gemini 2.5**: `gemini-2.5-pro`, `gemini-2.5-flash`, `gemini-2.5-flash-lite-preview-06-17`
- **Gemini 2.0**: `gemini-2.0-flash`, `gemini-2.0-flash-001`, `gemini-2.0-flash-lite`
- **Gemini 1.5**: `gemini-1.5-pro`, `gemini-1.5-flash`, `gemini-1.5-flash-8b`

### Mistral Models
- **Magistral**: `magistral-medium-2506`, `magistral-small-2506`
- **Mistral**: `mistral-medium-2505`, `mistral-large-2411`, `mistral-small-2503`
- **Specialized**: `codestral-2501`, `devstral-small-2505`
- **Edge**: `ministral-8b-2410`, `ministral-3b-2410`

## Files Updated
1. `src/lib/ai-service.ts` - Main model configuration
2. `src/components/AIConfiguration.tsx` - Default model settings
3. `src/lib/prompt-testing.ts` - Model mappings
4. `src/pages/Index.tsx` - Default test models
5. `src/components/PromptTester.tsx` - Fallback models
6. `.env.example` - Default environment settings
7. Test files - Updated model references

## Default Model Changes
- **Old Default**: `gpt-4.1-2025-04-14` (non-existent)
- **New Default**: `gpt-4o` (current stable)

## Recommendations
1. **Use stable models** in production (avoid experimental/preview models)
2. **Test with multiple providers** to ensure compatibility
3. **Monitor provider documentation** for model deprecations
4. **Use model aliases** (like `latest`) for development, specific versions for production

## Testing
After these updates:
- Gemini 2.5 Flash should work correctly
- All providers should have valid, current models
- Default configurations should work out of the box
- Fallback models are all current and stable

## Next Steps
1. Test the updated Gemini models in the application
2. Verify all providers work with their respective API keys
3. Monitor for any new model releases or deprecations
4. Consider adding automatic model validation in the future
