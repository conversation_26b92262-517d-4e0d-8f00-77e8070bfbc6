import { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Download, 
  FileText, 
  Settings, 
  Filter,
  Calendar,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { 
  exportToolsService, 
  ExportConfiguration, 
  ExportData, 
  EXPORT_FORMATS,
  PROMPT_STUDIO_APP_NAME,
  PROMPT_STUDIO_APP_VERSION,
  type StandaloneExportData, // Import type
  exportToPromptXArchive, // Import the correct archive function
  downloadBlobUtil       // Import the download utility
} from '@/lib/export-tools';
import { usePromptStore } from '@/hooks/usePromptStore'; // promptStore will need getFlows()
import { toast } from '@/hooks/use-toast';
// Assuming ChainFlow would be available from store or a specific context if we were exporting saved flows
// import { ChainFlow } from '@/lib/chain-linker';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ExportDialog = ({ open, onOpenChange }: ExportDialogProps) => {
  const { prompts, agents, results } = usePromptStore();
  
  const [configuration, setConfiguration] = useState<ExportConfiguration>({
    format: 'json',
    includeMetadata: true,
    includeResults: true,
    includeAgents: true,
    includeVariables: true
  });

  const [isExporting, setIsExporting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [exportStats, setExportStats] = useState({
    prompts: 0,
    agents: 0,
    results: 0,
    flows: 0
  });
  const [publishToVault, setPublishToVault] = useState(false); // New state for vault publishing

  // State for .promptx pack metadata
  const [packMetadata, setPackMetadata] = useState({
    packTitle: '',
    packVersion: '1.0.0',
    packAuthors: <AUTHORS>
    packDescription: '',
    packLicense: '',
    packTags: '', // Comma-separated string
    packTone: '',
    packOutputType: '',
    packPreviewSnippet: '',
    packPreferredModels: '', // New: Comma-separated string
    ethicsPledgeAdherence: false, // New: Boolean
  });

  useEffect(() => {
    // Calculate export statistics
    const stats = {
      prompts: prompts.length,
      agents: configuration.includeAgents ? agents.length : 0,
      results: configuration.includeResults ? results.length : 0
    };
    setExportStats(stats);
  }, [prompts, agents, results, configuration]);

  const handleExport = async () => {
    // Validate configuration
    const validation = exportToolsService.validateConfiguration(configuration);
    if (!validation.valid) {
      setValidationErrors(validation.errors);
      return;
    }

    setIsExporting(true);
    setValidationErrors([]);

    try {
      if (configuration.format === 'promptx') {
        // Handle .promptx export using exportToPromptXArchive
        // const flowsToExport = promptStore.getFlows ? promptStore.getFlows() : []; // Placeholder
        // For now, as flows are not in promptStore, this will be an empty array or undefined.
        // This part will be fully active when flow persistence (II.3) is implemented.
        const flowsToExport = []; // Actual flows would come from promptStore if they were saved

        // const flowsToExport = promptStore.getFlows ? promptStore.getFlows() : []; // Placeholder
        // For now, as flows are not in promptStore, this will be an empty array or undefined.
        // This part will be fully active when flow persistence (II.3) is implemented.
        const flowsToExport = []; // Actual flows would come from promptStore if they were saved

        const exportMetadataForPromptX: StandaloneExportData['metadata'] = configuration.includeMetadata ? {
          appName: PROMPT_STUDIO_APP_NAME,
          appVersion: PROMPT_STUDIO_APP_VERSION,
          exportDate: new Date().toISOString(),
          // Pack specific metadata from UI state
          packTitle: packMetadata.packTitle,
          packVersion: packMetadata.packVersion,
          packDescription: packMetadata.packDescription,
          authors: packMetadata.packAuthors.split(',').map(a => a.trim()).filter(a => a),
          license: packMetadata.packLicense,
          packTags: packMetadata.packTags.split(',').map(t => t.trim()).filter(t => t),
          packTone: packMetadata.packTone,
          packOutputType: packMetadata.packOutputType,
          packPreviewSnippet: packMetadata.packPreviewSnippet,
          packPreferredModels: packMetadata.packPreferredModels.split(',').map(m => m.trim()).filter(m => m), // Added
          ethicsPledgeAdherence: packMetadata.ethicsPledgeAdherence, // Added

          exportSettings: {
            includeAgents: configuration.includeAgents,
            includeResults: configuration.includeResults,
            includeVariables: configuration.includeVariables,
            filtersApplied: configuration.filters
          }
        } : {
          exportDate: new Date().toISOString(),
          // Still include essential pack title even if general metadata is off
          packTitle: packMetadata.packTitle || "Untitled Exported Pack",
        };

        const standaloneExportData: StandaloneExportData = {
          prompts: prompts.filter(p => p),
          agents: configuration.includeAgents ? agents.filter(a => a) : [],
          results: configuration.includeResults ? results.filter(r => r) : [],
          flows: flowsToExport,
          metadata: exportMetadataForPromptX
        };

        const blob = await exportToPromptXArchive(standaloneExportData);
        const timestamp = new Date().toISOString().split('T')[0];
        // Simplified filename generation for .promptx
        const filename = `prompt-studio-archive-${timestamp}.promptx`;
        downloadBlobUtil(blob, filename);

        toast({
          title: "Export Successful",
          description: `Data exported as ${filename}`,
        });

        if (publishToVault) {
          try {
            const {publishItemToVault} = usePromptStore.getState(); // Get latest instance of publish method
            await publishItemToVault({
              id: filename, // Use filename or a generated UUID for the pack instance ID
              type: 'prompt_pack',
              name: exportMetadataForPromptX?.packTitle || 'Untitled Pack',
              description: exportMetadataForPromptX?.packDescription,
              author: (exportMetadataForPromptX?.authors || []).join(', ') || undefined,
              tags: exportMetadataForPromptX?.packTags,
              previewSnippet: exportMetadataForPromptX?.packPreviewSnippet,
              itemCounts: {
                prompts: standaloneExportData.prompts.length,
                agents: standaloneExportData.agents?.length,
                results: standaloneExportData.results?.length,
                flows: standaloneExportData.flows?.length,
              }
              // upvotes & forksCount will be default 0
            });
            toast({ title: "Published to Vault", description: `${exportMetadataForPromptX?.packTitle || 'Pack'} (mock) published.`});
          } catch (vaultError) {
            console.error("Failed to publish to vault:", vaultError);
            toast({ title: "Vault Publish Failed", description: vaultError instanceof Error ? vaultError.message : "Unknown error", variant: "destructive"});
          }
        }

      } else {
        // Handle other formats using the existing service method
        const exportData: ExportData = {
          prompts: prompts,
          agents: configuration.includeAgents ? agents : undefined,
          results: configuration.includeResults ? results : undefined,
          metadata: configuration.includeMetadata ? {
            exportedAt: new Date(),
            version: '1.0', // This could be PROMPT_STUDIO_APP_VERSION
            source: PROMPT_STUDIO_APP_NAME,
            configuration
          } : undefined
        };

        const blob = await exportToolsService.exportData(exportData, configuration);
        // Use existing filename generation for other formats
        const filename = exportToolsService.generateFilename(configuration, exportData);
        const formatInfo = EXPORT_FORMATS[configuration.format];
        exportToolsService.downloadExport(blob, filename, formatInfo); // Pass formatInfo

        toast({
          title: "Export Successful",
          description: `Data exported as ${filename}`,
        });
      }

      onOpenChange(false);
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: "Export Failed",
        description: `Data exported as ${filename}`,
      });

      onOpenChange(false);
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleExternalExport = async (service: 'catalyst' | 'notion') => {
    setIsExporting(true);

    try {
      const exportData: ExportData = {
        prompts: prompts,
        agents: configuration.includeAgents ? agents : undefined,
        results: configuration.includeResults ? results : undefined,
        metadata: {
          exportedAt: new Date(),
          version: '1.0',
          source: 'Prompt Studio',
          configuration
        }
      };

      let result;
      if (service === 'catalyst') {
        result = await exportToolsService.exportToCatalyst(exportData, {});
      } else {
        result = await exportToolsService.exportToNotion(exportData, {});
      }

      if (result.success) {
        toast({
          title: "Export Successful",
          description: result.message,
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export to external service",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5 text-blue-400" />
            Export Data
          </DialogTitle>
          <DialogDescription>
            Export your prompts, agents, and results in various formats or to external services.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Statistics */}
          <div className="bg-slate-900/50 p-4 rounded-lg border border-slate-700">
            <h3 className="font-medium text-slate-200 mb-3">Export Preview</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">{exportStats.prompts}</div>
                <div className="text-sm text-slate-400">Prompts</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">{exportStats.agents}</div>
                <div className="text-sm text-slate-400">Agents</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">{exportStats.results}</div>
                <div className="text-sm text-slate-400">Results</div>
              </div>
            </div>
          </div>

          {/* Pack Metadata Form - Conditional for .promptx */}
          {configuration.format === 'promptx' && (
            <Card className="bg-slate-800/70 border-slate-700 p-4 md:p-6">
              <h3 className="text-lg font-semibold text-slate-100 mb-4">Pack Metadata (.promptx)</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3">
                <div>
                  <Label htmlFor="packTitle" className="text-sm text-slate-300">Pack Title *</Label>
                  <Input id="packTitle" value={packMetadata.packTitle} onChange={e => setPackMetadata(p => ({...p, packTitle: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="My Awesome Prompt Pack"/>
                  {!packMetadata.packTitle && <p className="text-xs text-red-400 mt-1">Title is required for .promptx export.</p>}
                </div>
                <div>
                  <Label htmlFor="packVersion" className="text-sm text-slate-300">Pack Version</Label>
                  <Input id="packVersion" value={packMetadata.packVersion} onChange={e => setPackMetadata(p => ({...p, packVersion: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="1.0.0"/>
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="packDescription" className="text-sm text-slate-300">Description</Label>
                  <Textarea id="packDescription" value={packMetadata.packDescription} onChange={e => setPackMetadata(p => ({...p, packDescription: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="A brief description of this prompt pack." rows={2}/>
                </div>
                <div>
                  <Label htmlFor="packAuthors" className="text-sm text-slate-300">Author(s) (comma-sep)</Label>
                  <Input id="packAuthors" value={packMetadata.packAuthors} onChange={e => setPackMetadata(p => ({...p, packAuthors: <AUTHORS>
                </div>
                <div>
                  <Label htmlFor="packLicense" className="text-sm text-slate-300">License</Label>
                  <Input id="packLicense" value={packMetadata.packLicense} onChange={e => setPackMetadata(p => ({...p, packLicense: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="MIT, CC-BY-NC-4.0, etc."/>
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="packTags" className="text-sm text-slate-300">Pack Tags (comma-sep)</Label>
                  <Input id="packTags" value={packMetadata.packTags} onChange={e => setPackMetadata(p => ({...p, packTags: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="creative, code, marketing"/>
                </div>
                 <div>
                  <Label htmlFor="packTone" className="text-sm text-slate-300">Overall Tone</Label>
                  <Input id="packTone" value={packMetadata.packTone} onChange={e => setPackMetadata(p => ({...p, packTone: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="e.g., formal, friendly"/>
                </div>
                <div>
                  <Label htmlFor="packOutputType" className="text-sm text-slate-300">Predominant Output Type</Label>
                  <Input id="packOutputType" value={packMetadata.packOutputType} onChange={e => setPackMetadata(p => ({...p, packOutputType: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="e.g., JSON, Markdown"/>
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="packPreviewSnippet" className="text-sm text-slate-300">Preview Snippet (short example)</Label>
                  <Textarea id="packPreviewSnippet" value={packMetadata.packPreviewSnippet} onChange={e => setPackMetadata(p => ({...p, packPreviewSnippet: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="A very short example prompt or output..." rows={2}/>
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="packPreferredModels" className="text-sm text-slate-300">Preferred Models (comma-sep)</Label>
                  <Input id="packPreferredModels" value={packMetadata.packPreferredModels} onChange={e => setPackMetadata(p => ({...p, packPreferredModels: e.target.value}))} className="mt-1 bg-slate-900/50 border-slate-600 text-white" placeholder="e.g., gpt-4o, claude-3-opus"/>
                </div>
                <div className="md:col-span-2 flex items-center space-x-2 mt-2">
                  <Checkbox
                    id="ethicsPledgeAdherence"
                    checked={packMetadata.ethicsPledgeAdherence}
                    onCheckedChange={checked => setPackMetadata(p => ({...p, ethicsPledgeAdherence: Boolean(checked)}))}
                    className="border-slate-500"
                  />
                  <Label htmlFor="ethicsPledgeAdherence" className="text-sm text-slate-300 cursor-pointer">
                    This pack adheres to the <a href="#" className="text-blue-400 hover:underline" title="View AI Prompt Ethics Pledge (📄20) - Link not implemented">AI Prompt Ethics Pledge (📄20)</a>.
                  </Label>
                </div>
                {/* Publish to Vault Checkbox */}
                <div className="md:col-span-2 flex items-center space-x-2 mt-3 pt-3 border-t border-slate-700/50">
                  <Checkbox
                    id="publishToVault"
                    checked={publishToVault}
                    onCheckedChange={checked => setPublishToVault(Boolean(checked))}
                    className="border-slate-500"
                  />
                  <Label htmlFor="publishToVault" className="text-sm text-slate-300 cursor-pointer">
                    Publish this pack to Community Vault (local mock)
                  </Label>
                </div>
              </div>
            </Card>
          )}

          <Tabs defaultValue="format" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="format">Format</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="filters">Filters</TabsTrigger>
              <TabsTrigger value="external">External</TabsTrigger>
            </TabsList>

            <TabsContent value="format" className="space-y-4">
              <div className="space-y-2">
                <Label>Export Format</Label>
                <Select 
                  value={configuration.format} 
                  onValueChange={(value) => setConfiguration(prev => ({ ...prev, format: value }))}
                >
                  <SelectTrigger className="bg-slate-900/50 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(EXPORT_FORMATS)
                      .filter(f => f.id !== 'promptx_single_json') // Filter out the single JSON PromptX from UI
                      .map(format => (
                      <SelectItem key={format.id} value={format.id}>
                        <div className="flex flex-col">
                          <span>{format.name}</span>
                          <span className="text-xs text-slate-400">{format.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="bg-slate-900/30 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="h-4 w-4 text-blue-400" />
                  <span className="text-sm font-medium text-slate-200">Format Details</span>
                </div>
                <p className="text-xs text-slate-400">
                  {EXPORT_FORMATS[configuration.format].description}
                </p>
                <div className="flex gap-2 mt-2">
                  <Badge variant="secondary" className="text-xs">
                    {EXPORT_FORMATS[configuration.format].extension}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {EXPORT_FORMATS[configuration.format].mimeType}
                  </Badge>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-200">Include Metadata</Label>
                    <p className="text-sm text-slate-400">Export timestamps, versions, and configuration</p>
                  </div>
                  <Switch
                    checked={configuration.includeMetadata}
                    onCheckedChange={(checked) => setConfiguration(prev => ({ ...prev, includeMetadata: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-200">Include Test Results</Label>
                    <p className="text-sm text-slate-400">Export all test results and scores</p>
                  </div>
                  <Switch
                    checked={configuration.includeResults}
                    onCheckedChange={(checked) => setConfiguration(prev => ({ ...prev, includeResults: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-200">Include Agents</Label>
                    <p className="text-sm text-slate-400">Export agent configurations and personalities</p>
                  </div>
                  <Switch
                    checked={configuration.includeAgents}
                    onCheckedChange={(checked) => setConfiguration(prev => ({ ...prev, includeAgents: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-200">Include Variables</Label>
                    <p className="text-sm text-slate-400">Export prompt variables and values</p>
                  </div>
                  <Switch
                    checked={configuration.includeVariables}
                    onCheckedChange={(checked) => setConfiguration(prev => ({ ...prev, includeVariables: checked }))}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="filters" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Minimum Score Threshold</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      value={[configuration.filters?.minScore || 0]}
                      onValueChange={([value]) => setConfiguration(prev => ({
                        ...prev,
                        filters: { ...prev.filters, minScore: value }
                      }))}
                      max={100}
                      min={0}
                      step={5}
                      className="flex-1"
                    />
                    <span className="text-sm text-slate-300 w-12">
                      {configuration.filters?.minScore || 0}%
                    </span>
                  </div>
                  <p className="text-xs text-slate-400">
                    Only export results with scores above this threshold
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Result Types</Label>
                  <div className="space-y-2">
                    {['prompt_test', 'agent_simulation', 'variation_testing', 'project_simulation'].map(type => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={type}
                          checked={configuration.filters?.types?.includes(type) ?? true}
                          onCheckedChange={(checked) => {
                            const currentTypes = configuration.filters?.types || [];
                            const newTypes = checked 
                              ? [...currentTypes, type]
                              : currentTypes.filter(t => t !== type);
                            
                            setConfiguration(prev => ({
                              ...prev,
                              filters: { ...prev.filters, types: newTypes }
                            }));
                          }}
                        />
                        <Label htmlFor={type} className="text-slate-200 capitalize">
                          {type.replace('_', ' ')}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="external" className="space-y-4">
              <div className="space-y-4">
                <Alert className="border-blue-600/30 bg-blue-900/20">
                  <ExternalLink className="h-4 w-4 text-blue-400" />
                  <AlertDescription>
                    <p className="text-blue-300 font-medium mb-2">External Service Integration</p>
                    <p className="text-blue-200 text-sm">
                      Export directly to external services like Catalyst vaults or Notion pages.
                      These integrations require proper API configuration.
                    </p>
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={() => handleExternalExport('catalyst')}
                    disabled={isExporting}
                    variant="outline"
                    className="h-20 flex flex-col items-center justify-center gap-2"
                  >
                    <ExternalLink className="h-5 w-5" />
                    <span>Export to Catalyst</span>
                    <span className="text-xs text-slate-400">Vault integration</span>
                  </Button>

                  <Button
                    onClick={() => handleExternalExport('notion')}
                    disabled={isExporting}
                    variant="outline"
                    className="h-20 flex flex-col items-center justify-center gap-2"
                  >
                    <ExternalLink className="h-5 w-5" />
                    <span>Export to Notion</span>
                    <span className="text-xs text-slate-400">Page integration</span>
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <Alert className="border-red-600/30 bg-red-900/20">
              <AlertCircle className="h-4 w-4 text-red-400" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="text-red-300 font-medium">Please fix the following errors:</p>
                  {validationErrors.map((error, index) => (
                    <p key={index} className="text-sm text-red-200">• {error}</p>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleExport} 
            disabled={
              isExporting ||
              validationErrors.length > 0 ||
              (configuration.format === 'promptx' && !packMetadata.packTitle.trim())
            }
            className="bg-gradient-to-r from-brand-blue to-brand-purple hover:from-brand-blue/90 hover:to-brand-purple/90 text-white"
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
