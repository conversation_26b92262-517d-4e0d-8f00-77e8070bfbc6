
import { useState, useRef, useCallback, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Save, 
  Trash2, 
  Link, 
  Unlink,
  Download,
  Upload,
  AlertCircle,
  CheckCircle,
  Zap, // Kept for main execute button, specific node icons come from NodePalette
  Settings as SettingsIcon, // Renamed to avoid conflict if a Settings component is imported
  X, Loader2 // For close button and loading state
} from 'lucide-react';
import { NodePalette, nodeTypeConfigs } from './NodePalette'; // Import NodePalette and its config
import { NodePropertiesPanel } from './NodePropertiesPanel'; // Import NodePropertiesPanel
import { 
  ChainFlow, 
  ChainNode, 
  ChainConnection, 
  chainLinkerService 
} from '@/lib/chain-linker';
import { toast } from '@/hooks/use-toast';

interface CanvasPosition {
  x: number;
  y: number;
}

interface DragState {
  isDragging: boolean;
  dragType: 'node' | 'canvas'; // Removed 'connection' here, will use specific state for that
  nodeId?: string; // For node dragging
  startPosition?: CanvasPosition; // For node or canvas dragging
  currentPosition?: CanvasPosition; // For node or canvas dragging
  hasMoved?: boolean; // Track if the mouse has moved significantly during drag
}

interface ConnectionDragState {
  isConnecting: boolean;
  sourceNodeId: string | null;
  sourceOutputId: string | null;
  previewPosition: CanvasPosition | null; // Mouse position for preview line end
  targetNodeId: string | null; // For highlighting target node
  targetInputId: string | null; // For highlighting target input
}

export const ChainLinkerCanvas = () => {
  const [flow, setFlow] = useState<ChainFlow>(
    chainLinkerService.createFlow('New Flow', 'Drag and drop prompt flow')
  );
  const [selectedNode, setSelectedNode] = useState<ChainNode | null>(null);
  // Node and Canvas dragging state
  const [dragState, setDragState] = useState<DragState>({ isDragging: false, dragType: 'canvas', hasMoved: false });
  // Connection dragging state
  const [connectionDragState, setConnectionDragState] = useState<ConnectionDragState>({
    isConnecting: false,
    sourceNodeId: null,
    sourceOutputId: null,
    previewPosition: null,
    targetNodeId: null,
    targetInputId: null,
  });

  const [canvasOffset, setCanvasOffset] = useState<CanvasPosition>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<any>(null);
  
  const canvasRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const portRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // Drag threshold to distinguish between click and drag
  const DRAG_THRESHOLD = 5;

  const getPortElementId = (nodeId: string, portId: string, type: 'input' | 'output') => `${type}-${nodeId}-${portId}`;

  // Helper to calculate absolute port position on canvas
  const getPortPosition = useCallback((nodeId: string, portId: string, type: 'input' | 'output') => {
    const portElementId = getPortElementId(nodeId, portId, type);
    const portElement = portRefs.current[portElementId];
    const node = flow.nodes.find(n => n.id === nodeId);

    if (portElement && node && canvasRef.current) {
      const portRect = portElement.getBoundingClientRect();
      const canvasRect = canvasRef.current.getBoundingClientRect();

      // Position of port center relative to node's top-left in unzoomed coordinates
      const portCenterXRelNode = (portRect.left + portRect.width / 2 - (node.position.x * zoom + canvasOffset.x + canvasRect.left)) / zoom;
      const portCenterYRelNode = (portRect.top + portRect.height / 2 - (node.position.y * zoom + canvasOffset.y + canvasRect.top)) / zoom;

      // Absolute position of port center on canvas in unzoomed coordinates
      return {
        x: node.position.x + portCenterXRelNode,
        y: node.position.y + portCenterYRelNode,
      };
    }
    return null;
  }, [flow.nodes, zoom, canvasOffset]);

  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {
    // If clicking on an input port while connecting, it's handled by handleInputMouseUp
    if (connectionDragState.isConnecting) {
       // Check if the click is on an input port, otherwise cancel connection
      const targetElement = e.target as HTMLElement;
      const inputPortElement = targetElement.closest('[data-input-port-node-id]');
      if (!inputPortElement) {
        setConnectionDragState({ isConnecting: false, sourceNodeId: null, sourceOutputId: null, previewPosition: null, targetNodeId: null, targetInputId: null });
      }
      return;
    }

    // If not clicking on a node or specific interactive element, assume canvas panning
    const targetIsNode = (e.target as HTMLElement).closest('.chain-node');
    if (!targetIsNode && e.target === canvasRef.current) {
      setDragState({
        isDragging: true,
        dragType: 'canvas',
        startPosition: { x: e.clientX, y: e.clientY }, // Store initial mouse position
        hasMoved: false
      });
    }
  }, [canvasOffset, connectionDragState.isConnecting]);

  const handleCanvasMouseMove = useCallback((e: React.MouseEvent) => {
    if (dragState.isDragging && dragState.dragType === 'canvas' && dragState.startPosition) {
      const deltaX = e.clientX - dragState.startPosition.x;
      const deltaY = e.clientY - dragState.startPosition.y;
      
      // Check if movement exceeds threshold
      if (!dragState.hasMoved && (Math.abs(deltaX) > DRAG_THRESHOLD || Math.abs(deltaY) > DRAG_THRESHOLD)) {
        setDragState(prev => ({ ...prev, hasMoved: true }));
      }
      
      setCanvasOffset(prevOffset => ({
        x: prevOffset.x + deltaX,
        y: prevOffset.y + deltaY
      }));
      // Update start position for continuous dragging
      setDragState(prev => ({ ...prev, startPosition: { x: e.clientX, y: e.clientY } }));
    } else if (connectionDragState.isConnecting && canvasRef.current) {
      const canvasRect = canvasRef.current.getBoundingClientRect();
      const previewX = (e.clientX - canvasRect.left - canvasOffset.x) / zoom;
      const previewY = (e.clientY - canvasRect.top - canvasOffset.y) / zoom;

      // Check for hover over input ports
      let foundTarget = false;
      for (const node of flow.nodes) {
        if (node.id === connectionDragState.sourceNodeId) continue; // Cannot connect to self

        for (const input of node.inputs) {
          const portElementId = getPortElementId(node.id, input.id, 'input');
          const portElement = portRefs.current[portElementId];
          if (portElement) {
            const portRect = portElement.getBoundingClientRect();
            if (e.clientX >= portRect.left && e.clientX <= portRect.right &&
                e.clientY >= portRect.top && e.clientY <= portRect.bottom) {
              setConnectionDragState(prev => ({ ...prev, previewPosition: { x: previewX, y: previewY }, targetNodeId: node.id, targetInputId: input.id }));
              foundTarget = true;
              break;
            }
          }
        }
        if (foundTarget) break;
      }
      if (!foundTarget) {
         setConnectionDragState(prev => ({ ...prev, previewPosition: { x: previewX, y: previewY }, targetNodeId: null, targetInputId: null }));
      }
    } else if (dragState.isDragging && dragState.dragType === 'node' && dragState.nodeId && dragState.startPosition) {
        const deltaX = e.clientX - dragState.startPosition.x;
        const deltaY = e.clientY - dragState.startPosition.y;
        
        // Check if movement exceeds threshold
        if (!dragState.hasMoved && (Math.abs(deltaX) > DRAG_THRESHOLD || Math.abs(deltaY) > DRAG_THRESHOLD)) {
          setDragState(prev => ({ ...prev, hasMoved: true }));
        }

        if (dragState.hasMoved) {
          const newX = (e.clientX - dragState.startPosition.x) / zoom;
          const newY = (e.clientY - dragState.startPosition.y) / zoom;

          const updatedNodes = flow.nodes.map(n =>
            n.id === dragState.nodeId
              ? { ...n, position: { x: newX , y: newY } }
              : n
          );
          setFlow(prevFlow => ({ ...prevFlow, nodes: updatedNodes }));
        }
    }
  }, [dragState, connectionDragState.isConnecting, canvasOffset, zoom, flow.nodes, getPortPosition]);

  const handleCanvasMouseUp = useCallback(() => {
    if (dragState.isDragging && dragState.dragType === 'node') {
      // Node dragging finished - only select node if it wasn't dragged
      if (!dragState.hasMoved && dragState.nodeId) {
        const node = flow.nodes.find(n => n.id === dragState.nodeId);
        if (node) {
          setSelectedNode(node);
        }
      }
    } else if (connectionDragState.isConnecting) {
      // Connection dragging finished, but not on a valid port (handled by handleInputMouseUp)
      // This case handles dropping the connection on the canvas itself
      toast({ title: "Connection Canceled", description: "Connection must end on an input port.", variant: "destructive" });
    }
    setDragState({ isDragging: false, dragType: 'canvas', hasMoved: false });
    setConnectionDragState(prev => ({ ...prev, isConnecting: false, sourceNodeId: null, sourceOutputId: null, previewPosition: null, targetNodeId: null, targetInputId: null }));
  }, [dragState, connectionDragState.isConnecting, flow.nodes]);

  const handleNodeMouseDown = useCallback((nodeId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent canvas drag
    const node = flow.nodes.find(n => n.id === nodeId);
    if (node) {
      setDragState({
        isDragging: true,
        dragType: 'node',
        nodeId: nodeId,
        // Store initial click position relative to the node's current position
        startPosition: {
          x: e.clientX - node.position.x * zoom,
          y: e.clientY - node.position.y * zoom
        },
        hasMoved: false
      });
    }
  }, [flow.nodes, zoom]);

  const handleOutputMouseDown = useCallback((nodeId: string, outputId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent node drag or canvas drag
    setConnectionDragState({
      isConnecting: true,
      sourceNodeId: nodeId,
      sourceOutputId: outputId,
      previewPosition: null, // Will be set on mouse move
      targetNodeId: null,
      targetInputId: null,
    });
  }, []);

  const handleInputMouseUp = useCallback((targetNodeId: string, targetInputId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (connectionDragState.isConnecting && connectionDragState.sourceNodeId && connectionDragState.sourceOutputId) {
      if (connectionDragState.sourceNodeId === targetNodeId) {
        toast({ title: "Connection Error", description: "Cannot connect a node to itself.", variant: "destructive" });
      } else {
        try {
          chainLinkerService.addConnection(
            flow,
            connectionDragState.sourceNodeId,
            connectionDragState.sourceOutputId,
            targetNodeId,
            targetInputId
          );
          setFlow({ ...flow });
          toast({ title: "Connection Created", description: "Successfully linked nodes." });
        } catch (error: any) {
          toast({ title: "Connection Error", description: error.message, variant: "destructive" });
        }
      }
    }
    setConnectionDragState({ isConnecting: false, sourceNodeId: null, sourceOutputId: null, previewPosition: null, targetNodeId: null, targetInputId: null });
  }, [connectionDragState, flow]);


  const addNode = (nodeType: ChainNode['type'], position?: CanvasPosition) => {
    const canvasRect = canvasRef.current?.getBoundingClientRect();
    const defaultPosition = canvasRect ? {
      x: (canvasRect.width / 2 - canvasOffset.x) / zoom,
      y: (canvasRect.height / 2 - canvasOffset.y) / zoom
    } : { x: 200, y: 200 };

    const newNode = chainLinkerService.addNode(flow, nodeType, position || defaultPosition);
    setFlow({ ...flow });
    setSelectedNode(newNode);
    
    toast({
      title: "Node Added",
      description: `Added ${nodeType} node to the flow`,
    });
  };

  const removeNode = (nodeId: string) => {
    chainLinkerService.removeNode(flow, nodeId);
    setFlow({ ...flow });
    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }
    
    toast({
      title: "Node Removed",
      description: "Node and its connections have been removed",
    });
  };

  const updateNodeData = (nodeId: string, data: Partial<ChainNode['data']>) => {
    const node = flow.nodes.find(n => n.id === nodeId);
    if (node) {
      node.data = { ...node.data, ...data };
      setFlow({ ...flow });
    }
  };

  const executeFlow = async () => {
    setIsExecuting(true);
    setExecutionResult(null);

    try {
      const validation = chainLinkerService.validateFlow(flow);
      if (!validation.valid) {
        toast({
          title: "Validation Failed",
          description: validation.errors.join(', '),
          variant: "destructive"
        });
        return;
      }

      const result = await chainLinkerService.executeFlow(flow, {});
      setExecutionResult(result);
      
      if (result.success) {
        toast({
          title: "Execution Complete",
          description: `Flow executed successfully in ${result.duration}ms`,
        });
      } else {
        toast({
          title: "Execution Failed",
          description: result.errors?.[0]?.message || "Unknown error",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Execution Error",
        description: error instanceof Error ? error.message : "Failed to execute flow",
        variant: "destructive"
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const saveFlow = () => {
    // In a real app, this would save to a backend
    const flowData = JSON.stringify(flow, null, 2);
    const blob = new Blob([flowData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${flow.name.replace(/\s+/g, '_')}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Flow Saved",
      description: "Flow has been downloaded as JSON",
    });
  };

  // Helper function to get node configuration (icon, color)
  // This remains in ChainLinkerCanvas as it's used for rendering nodes on the canvas itself.
  const getNodeDisplayConfig = (nodeType: string) => {
    return nodeTypeConfigs.find(nt => nt.type === nodeType) ||
           { type: 'prompt', icon: Zap, label: 'Default', color: 'bg-gray-600' }; // Default fallback
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-700 bg-slate-800/50">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-bold text-slate-200">Chain Flow Editor</h2>
          <Badge variant="outline" className="border-slate-500 text-slate-300">
            {flow.nodes.length} Nodes | {flow.connections.length} Connections
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button onClick={executeFlow} disabled={isExecuting} className="bg-green-600 hover:bg-green-700">
            {isExecuting ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Play className="w-4 h-4 mr-2" />
            )}
            Execute Flow
          </Button>
          <Button onClick={saveFlow} variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white">
            <Save className="w-4 h-4 mr-2" />
            Save Flow
          </Button>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden"> {/* Added overflow-hidden to parent */}
        {/* Node Palette & Flow Info Section */}
        <div className="w-72 flex flex-col border-r border-slate-700 bg-slate-800/30 shrink-0"> {/* Added shrink-0 */}
          <NodePalette onAddNode={(nodeType) => addNode(nodeType)} />

          {/* Flow Info - Moved under NodePalette */}
          <div className="p-4 space-y-3 border-t border-slate-700 flex-shrink-0">
            <h3 className="font-semibold text-slate-200 mb-2">Flow Details</h3>
            <div>
              <Label htmlFor="flow-name" className="text-slate-300 text-sm">Name</Label>
              <Input
                id="flow-name"
                value={flow.name}
                onChange={(e) => setFlow({ ...flow, name: e.target.value, metadata: {...flow.metadata, updatedAt: new Date()} })}
                className="bg-slate-900/50 border-slate-600 text-white mt-1"
              />
            </div>
            <div>
              <Label htmlFor="flow-description" className="text-slate-300 text-sm">Description</Label>
              <Textarea
                id="flow-description"
                value={flow.description}
                onChange={(e) => setFlow({ ...flow, description: e.target.value, metadata: {...flow.metadata, updatedAt: new Date()} })}
                className="bg-slate-900/50 border-slate-600 text-white mt-1 min-h-[60px]"
                rows={3}
              />
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div className="flex-1 relative overflow-hidden bg-slate-900"> {/* Ensure this takes remaining space */}
          <div
            ref={canvasRef}
            className="w-full h-full relative cursor-move"
            onMouseDown={handleCanvasMouseDown}
            onMouseMove={handleCanvasMouseMove}
            onMouseUp={handleCanvasMouseUp}
            style={{
              backgroundImage: `radial-gradient(circle, #374151 1px, transparent 1px)`,
              backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
              backgroundPosition: `${canvasOffset.x}px ${canvasOffset.y}px`
            }}
          >
            {/* Nodes */}
            {flow.nodes.map(node => {
              const displayConfig = getNodeDisplayConfig(node.type);
              const Icon = displayConfig.icon; // This will be the LucideIcon component
              const nodeColor = displayConfig.color;
              const isSelected = selectedNode?.id === node.id;
              
              return (
                <div
                  key={node.id}
                  style={{
                    left: node.position.x * zoom + canvasOffset.x,
                    top: node.position.y * zoom + canvasOffset.y,
                    transformOrigin: 'top left',
                    transform: `scale(${zoom})`,
                  }}
                  onClick={() => setSelectedNode(node)}
                  onMouseDown={(e) => handleNodeMouseDown(node.id, e)}
                  className={`chain-node absolute cursor-grab transition-transform hover:shadow-lg ${
                    isSelected ? 'ring-2 ring-blue-400 shadow-xl' : 'shadow-md'
                  } bg-slate-800 border-slate-700 rounded-lg w-56`} // Card styles moved here
                >
                  <div className="p-3 border-b border-slate-700"> {/* Header equivalent */}
                    <div className="text-sm flex items-center gap-2 text-slate-100">
                      <div className={`w-3 h-3 rounded-full ${nodeColor} flex-shrink-0`} />
                      <Icon className="w-4 h-4 flex-shrink-0" />
                      <span className="truncate flex-grow font-medium">{node.data.title}</span>
                      <Button
                        onClick={(e) => { e.stopPropagation(); removeNode(node.id); }}
                        size="icon" variant="ghost"
                        className="ml-auto h-5 w-5 p-0 text-red-400 hover:text-red-300 flex-shrink-0"
                        title="Delete Node"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="p-3 pt-1"> {/* Content equivalent */}
                    <p className="text-xs text-slate-400 mb-2 truncate" title={node.data.description || undefined}>
                      {node.data.description || 'No description'}
                    </p>

                    {/* Input/Output ports */}
                      <div className="flex justify-between text-xs mt-1">
                        {/* Inputs */}
                        <div className="space-y-1.5">
                          {node.inputs.map(input => (
                            <div
                              key={input.id}
                              className="flex items-center gap-1.5 relative"
                              onMouseUp={(e) => handleInputMouseUp(node.id, input.id, e)}
                              data-input-port-node-id={node.id} // For easier identification in event handlers
                              data-input-port-id={input.id}
                            >
                              <div
                                ref={el => portRefs.current[getPortElementId(node.id, input.id, 'input')] = el}
                                className={`w-3 h-3 rounded-full border-2 ${
                                  connectionDragState.isConnecting && connectionDragState.targetNodeId === node.id && connectionDragState.targetInputId === input.id
                                  ? 'bg-green-500 border-green-300 ring-2 ring-green-300'
                                  : 'bg-blue-500 border-blue-300 hover:bg-blue-400'
                                } cursor-crosshair`}
                              />
                              <span className="text-slate-300">{input.label}</span>
                            </div>
                          ))}
                        </div>
                        {/* Outputs */}
                        <div className="space-y-1.5 text-right">
                          {node.outputs.map(output => (
                            <div
                              key={output.id}
                              className="flex items-center gap-1.5 justify-end relative"
                              onMouseDown={(e) => handleOutputMouseDown(node.id, output.id, e)}
                            >
                              <span className="text-slate-300">{output.label}</span>
                              <div
                                ref={el => portRefs.current[getPortElementId(node.id, output.id, 'output')] = el}
                                className="w-3 h-3 rounded-full border-2 bg-green-500 border-green-300 hover:bg-green-400 cursor-crosshair"
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    {/* </CardContent> and </Card> were here, ensured they are removed. The parent is now the main div. */}
                  </div> {/* This closes the div with class "p-3 pt-1" (Content equivalent) */}
                </div>
              );
            })}

            {/* SVG for connections */}
            <svg
              ref={svgRef}
              className="absolute inset-0 pointer-events-none w-full h-full" // Ensure SVG takes full canvas size
            >
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9" // Adjusted for line thickness
                  refY="3.5"
                  orient="auto"
                  markerUnits="strokeWidth"
                >
                  <polygon points="0 0, 10 3.5, 0 7" fill="#60a5fa" />
                </marker>
                 <marker
                  id="arrowhead-preview"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                  markerUnits="strokeWidth"
                >
                  <polygon points="0 0, 10 3.5, 0 7" fill="#facc15" /> {/* Yellow for preview */}
                </marker>
              </defs>
              {flow.connections.map(connection => {
                const sourceNode = flow.nodes.find(n => n.id === connection.sourceNodeId);
                const targetNode = flow.nodes.find(n => n.id === connection.targetNodeId);
                
                if (!sourceNode || !targetNode) return null;

                // Calculate port positions dynamically
                const sourcePortPos = getPortPosition(connection.sourceNodeId, connection.sourceOutputId, 'output');
                const targetPortPos = getPortPosition(connection.targetNodeId, connection.targetInputId, 'input');

                if (!sourcePortPos || !targetPortPos) return null;

                const sourceX = sourcePortPos.x * zoom + canvasOffset.x;
                const sourceY = sourcePortPos.y * zoom + canvasOffset.y;
                const targetX = targetPortPos.x * zoom + canvasOffset.x;
                const targetY = targetPortPos.y * zoom + canvasOffset.y;
                
                return (
                  <line
                    key={connection.id}
                    x1={sourceX}
                    y1={sourceY}
                    x2={targetX}
                    y2={targetY}
                    stroke="#60a5fa" // Blue for established connections
                    strokeWidth={2 * zoom} // Scale stroke width with zoom
                    markerEnd="url(#arrowhead)"
                  />
                );
              })}

              {/* Connection Preview Line */}
              {connectionDragState.isConnecting && connectionDragState.sourceNodeId && connectionDragState.sourceOutputId && connectionDragState.previewPosition && (() => {
                const sourcePortPos = getPortPosition(connectionDragState.sourceNodeId!, connectionDragState.sourceOutputId!, 'output');
                if (!sourcePortPos) return null;

                const startX = sourcePortPos.x * zoom + canvasOffset.x;
                const startY = sourcePortPos.y * zoom + canvasOffset.y;
                const endX = connectionDragState.previewPosition!.x * zoom + canvasOffset.x;
                const endY = connectionDragState.previewPosition!.y * zoom + canvasOffset.y;

                return (
                  <line
                    x1={startX}
                    y1={startY}
                    x2={endX}
                    y2={endY}
                    stroke="#facc15" // Yellow for preview
                    strokeWidth={2 * zoom}
                    strokeDasharray="5,5" // Dashed line for preview
                    markerEnd="url(#arrowhead-preview)"
                  />
                );
              })()}
            </svg>
          </div>

          {/* Zoom Controls */}
          <div className="absolute bottom-4 right-4 flex gap-2">
            <Button
              onClick={() => setZoom(Math.max(0.5, zoom - 0.1))}
              size="sm"
              variant="outline"
            >
              -
            </Button>
            <span className="px-2 py-1 bg-slate-800 rounded text-sm text-slate-200">
              {Math.round(zoom * 100)}%
            </span>
            <Button
              onClick={() => setZoom(Math.min(2, zoom + 0.1))}
              size="sm"
              variant="outline"
            >
              +
            </Button>
          </div>
        </div>

        {/* Properties Panel */}
        <NodePropertiesPanel
          selectedNode={selectedNode}
          onUpdateNodeData={updateNodeData}
          onClose={() => setSelectedNode(null)}
        />
      </div>

      {/* Execution Results - Improved Display */}
      {executionResult && (
        <div className="border-t border-slate-700 p-4 max-h-60 overflow-y-auto bg-slate-800/30 shrink-0"> {/* Added shrink-0 */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              {executionResult.success ? (
                <CheckCircle className="w-5 h-5 text-green-400" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-400" />
              )}
              <h3 className="font-semibold text-slate-200">Flow Execution Results</h3>
              <Badge variant={executionResult.success ? "default" : "destructive"} className={executionResult.success ? "bg-green-600" : ""}>
                {executionResult.success ? 'Success' : 'Failed'}
              </Badge>
            </div>
            <Button size="sm" variant="ghost" onClick={() => setExecutionResult(null)} className="text-slate-400 hover:text-slate-200">
              <X className="w-4 h-4"/>
            </Button>
          </div>
          
          <div className="bg-slate-900/70 p-3 rounded text-sm text-slate-300">
            <p>Duration: {executionResult.duration}ms</p>
            <p>Path: {executionResult.executionPath.join(' -> ')}</p>
            {executionResult.errors && executionResult.errors.length > 0 && (
              <div className="mt-2">
                <h4 className="font-semibold text-red-400">Errors:</h4>
                {executionResult.errors.map((err: any, index: number) => ( // Added type for err
                  <div key={index} className="mt-1 p-2 bg-red-900/30 rounded">
                    <p><strong>Node:</strong> {flow.nodes.find(n=>n.id === err.nodeId)?.data.title || err.nodeId}</p>
                    <p><strong>Message:</strong> {err.message}</p>
                    {err.details && <pre className="text-xs text-red-300 mt-1 overflow-x-auto whitespace-pre-wrap">{String(err.details)}</pre>}
                  </div>
                ))}
              </div>
            )}
             <h4 className="font-semibold text-slate-200 mt-2">Node Results:</h4>
            <pre className="max-h-40 overflow-y-auto">{JSON.stringify(executionResult.results, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
};
