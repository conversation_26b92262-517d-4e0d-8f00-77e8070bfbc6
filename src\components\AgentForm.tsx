
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, X } from 'lucide-react';
import { Agent } from '@/store/promptStore';

interface AgentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (agentData: Omit<Agent, 'id' | 'enabled'> | Partial<Agent>) => void;
  editingAgent?: Agent | null;
}

export const AgentForm = ({ isOpen, onClose, onSave, editingAgent }: AgentFormProps) => {
  const [formData, setFormData] = useState({
    name: editingAgent?.name || '',
    role: editingAgent?.role || '',
    systemPrompt: editingAgent?.systemPrompt || '',
    personality: editingAgent?.personality || '',
    expertise: editingAgent?.expertise || [],
    avatar: editingAgent?.avatar || '🤖'
  });
  
  const [newExpertise, setNewExpertise] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    handleClose();
  };

  const handleClose = () => {
    setFormData({
      name: '',
      role: '',
      systemPrompt: '',
      personality: '',
      expertise: [],
      avatar: '🤖'
    });
    setNewExpertise('');
    onClose();
  };

  const handleAddExpertise = () => {
    if (!newExpertise.trim()) return;
    setFormData(prev => ({
      ...prev,
      expertise: [...prev.expertise, newExpertise.trim()]
    }));
    setNewExpertise('');
  };

  const handleRemoveExpertise = (index: number) => {
    setFormData(prev => ({
      ...prev,
      expertise: prev.expertise.filter((_, i) => i !== index)
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-slate-900 border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-slate-200">
            {editingAgent ? 'Edit Agent' : 'Create New Agent'}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name" className="text-slate-200">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="bg-slate-800 border-slate-600 text-white"
                placeholder="Agent name..."
                required
              />
            </div>
            <div>
              <Label htmlFor="role" className="text-slate-200">Role</Label>
              <Input
                id="role"
                value={formData.role}
                onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
                className="bg-slate-800 border-slate-600 text-white"
                placeholder="e.g., Senior Developer, Marketing Expert..."
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="avatar" className="text-slate-200">Avatar (Emoji)</Label>
            <Input
              id="avatar"
              value={formData.avatar}
              onChange={(e) => setFormData(prev => ({ ...prev, avatar: e.target.value }))}
              className="bg-slate-800 border-slate-600 text-white"
              placeholder="🤖"
              maxLength={2}
            />
          </div>

          <div>
            <Label htmlFor="systemPrompt" className="text-slate-200">System Prompt</Label>
            <Textarea
              id="systemPrompt"
              value={formData.systemPrompt}
              onChange={(e) => setFormData(prev => ({ ...prev, systemPrompt: e.target.value }))}
              className="bg-slate-800 border-slate-600 text-white min-h-[120px]"
              placeholder="Define the agent's core instructions and behavior..."
              required
            />
          </div>

          <div>
            <Label htmlFor="personality" className="text-slate-200">Personality (Optional)</Label>
            <Textarea
              id="personality"
              value={formData.personality}
              onChange={(e) => setFormData(prev => ({ ...prev, personality: e.target.value }))}
              className="bg-slate-800 border-slate-600 text-white"
              placeholder="Describe the agent's personality traits..."
            />
          </div>

          <div>
            <Label className="text-slate-200">Expertise Areas</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newExpertise}
                onChange={(e) => setNewExpertise(e.target.value)}
                placeholder="Add skill or expertise..."
                className="bg-slate-800 border-slate-600 text-white flex-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddExpertise();
                  }
                }}
              />
              <Button
                type="button"
                onClick={handleAddExpertise}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            
            {formData.expertise.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-3">
                {formData.expertise.map((skill, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="bg-slate-700 text-slate-300 pr-1"
                  >
                    {skill}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveExpertise(index)}
                      className="ml-1 h-auto p-0 hover:bg-transparent"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              {editingAgent ? 'Update Agent' : 'Create Agent'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
