
import { Button } from '@/components/ui/button';
import { FileInput, Zap, Brain, Filter, FileOutput } from 'lucide-react';
import { ChainNode } from '@/lib/chain-linker';

interface NodeTypeDisplay {
  type: ChainNode['type'];
  icon: React.ComponentType<{ className?: string }>; // Fixed type
  label: string;
  color: string;
}

export const nodeTypeConfigs: NodeTypeDisplay[] = [
  { type: 'input', icon: FileInput, label: 'Input', color: 'bg-green-600' },
  { type: 'prompt', icon: Zap, label: 'Prompt', color: 'bg-blue-600' },
  { type: 'agent', icon: Brain, label: 'Agent', color: 'bg-purple-600' },
  { type: 'condition', icon: Filter, label: 'Condition', color: 'bg-yellow-600' },
  { type: 'output', icon: FileOutput, label: 'Output', color: 'bg-red-600' }
];

interface NodePaletteProps {
  onAddNode: (nodeType: ChainNode['type']) => void;
}

export const NodePalette = ({ onAddNode }: NodePaletteProps) => {
  return (
    <div className="w-64 border-r border-slate-700 p-4 space-y-4 bg-slate-800/30">
      <h3 className="font-semibold text-slate-200">Node Types</h3>
      <div className="space-y-2">
        {nodeTypeConfigs.map(({ type, icon: Icon, label, color }) => (
          <Button
            key={type}
            onClick={() => onAddNode(type)}
            variant="outline"
            className="w-full justify-start border-slate-600 hover:bg-slate-700 text-slate-200 hover:text-white"
          >
            <div className={`w-3 h-3 rounded-full ${color} mr-2 flex-shrink-0`} />
            <Icon className="w-4 h-4 mr-2 flex-shrink-0" />
            <span className="truncate">{label}</span>
          </Button>
        ))}
      </div>
    </div>
  );
};
