
import { <PERSON><PERSON> } from '@/components/ui/button';
import { FileInput, Zap, Brain, Filter, FileOutput, Bookmark, Layers } from 'lucide-react'; // Added Bookmark, Layers
import { ChainNode } from '@/lib/chain-linker';
import { usePromptStore } from '@/hooks/usePromptStore'; // Added usePromptStore
import { NodeTemplate } from '@/store/promptStore'; // Added NodeTemplate
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"; // Added Tooltip
import { Separator } from '@/components/ui/separator'; // Added Separator

interface NodeTypeDisplay {
  type: ChainNode['type'];
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  color: string;
}

// TODO: [Refactor] nodeTypeConfigs should ideally be derived from a single source of truth
// that also feeds chainLinkerService.getNodeTemplates(), potentially by augmenting
// the service's template definitions with display metadata (icon, label, color).
// For now, this static config is used for UI display in the palette.
export const nodeTypeConfigs: NodeTypeDisplay[] = [
  { type: 'input', icon: FileInput, label: 'Input', color: 'bg-green-600' },
  { type: 'prompt', icon: Zap, label: 'Prompt', color: 'bg-blue-600' },
  { type: 'agent', icon: Brain, label: 'Agent', color: 'bg-purple-600' },
  { type: 'condition', icon: Filter, label: 'Condition', color: 'bg-yellow-600' },
  { type: 'output', icon: FileOutput, label: 'Output', color: 'bg-red-600' }
];

interface NodePaletteProps {
  onAddNode: (nodeType: ChainNode['type']) => void;
  onAddNodeFromTemplate: (template: NodeTemplate) => void; // New prop
}

export const NodePalette = ({ onAddNode, onAddNodeFromTemplate }: NodePaletteProps) => {
  const { nodeTemplates } = usePromptStore();

  // Helper to get icon for template based on its nodeType
  const getIconForNodeType = (nodeType: NodeTemplate['nodeType']) => {
    const config = nodeTypeConfigs.find(c => c.type === nodeType);
    return config ? config.icon : Layers; // Default icon
  };

  return (
    <TooltipProvider> {/* Added TooltipProvider */}
      <div className="w-full p-4 space-y-6 overflow-y-auto"> {/* Changed w-64 to w-full, added overflow */}
        <div>
          <h3 className="font-semibold text-slate-200 mb-3">Core Nodes</h3>
          <div className="space-y-2">
            {nodeTypeConfigs.map(({ type, icon: Icon, label, color }) => (
              <Button
                key={type}
                onClick={() => onAddNode(type)}
                variant="outline"
                className="w-full justify-start border-slate-600 hover:bg-slate-700 text-slate-200 hover:text-white"
              >
                <div className={`w-3 h-3 rounded-full ${color} mr-2 flex-shrink-0`} />
                <Icon className="w-4 h-4 mr-2 flex-shrink-0" />
                <span className="truncate">{label}</span>
              </Button>
            ))}
          </div>
        </div>

        {nodeTemplates && nodeTemplates.length > 0 && (
          <div>
            <Separator className="my-4 bg-slate-700" />
            <h3 className="font-semibold text-slate-200 mb-3">Node Templates</h3>
            <div className="space-y-2">
              {nodeTemplates.map((template) => {
                const Icon = getIconForNodeType(template.nodeType);
                return (
                  <Tooltip key={template.id} delayDuration={300}>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={() => onAddNodeFromTemplate(template)}
                        variant="outline"
                        className="w-full justify-start border-slate-600 hover:bg-slate-700 text-slate-300 hover:text-white"
                      >
                        <Bookmark className="w-4 h-4 mr-2 flex-shrink-0 text-yellow-500" />
                        <span className="truncate flex-grow">{template.name}</span>
                        <Icon className="w-3 h-3 ml-auto text-slate-500 flex-shrink-0" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="bg-slate-800 text-slate-200 border-slate-700">
                      <p className="font-medium">{template.name} ({template.nodeType})</p>
                      {template.description && <p className="text-xs text-slate-400">{template.description}</p>}
                      {template.tags && template.tags.length > 0 && (
                        <div className="mt-1">
                          {template.tags.map(tag => <Badge key={tag} variant="secondary" className="mr-1 text-xs">{tag}</Badge>)}
                        </div>
                      )}
                    </TooltipContent>
                  </Tooltip>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
};
            variant="outline"
            className="w-full justify-start border-slate-600 hover:bg-slate-700 text-slate-200 hover:text-white"
          >
            <div className={`w-3 h-3 rounded-full ${color} mr-2 flex-shrink-0`} />
            <Icon className="w-4 h-4 mr-2 flex-shrink-0" />
            <span className="truncate">{label}</span>
          </Button>
        ))}
      </div>
    </div>
  );
};
