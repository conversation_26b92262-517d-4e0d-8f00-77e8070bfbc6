
import { Button } from '@/components/ui/button';
import { Eye, GitBranch } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { TestResult } from '@/store/promptStore';

interface PromptHistoryActionsProps {
  result: TestResult;
  onView?: (result: TestResult) => void;
  onFork?: (result: TestResult) => void;
}

export const PromptHistoryActions = ({ result, onView, onFork }: PromptHistoryActionsProps) => {
  const handleView = () => {
    if (onView) {
      onView(result);
    } else {
      // Default view behavior - show details in toast for now
      toast({
        title: "Result Details",
        description: `Type: ${result.type} | Timestamp: ${result.timestamp.toLocaleString()}`,
      });
    }
  };

  const handleFork = () => {
    if (onFork) {
      onFork(result);
    } else {
      // Default fork behavior - show coming soon message
      toast({
        title: "Fork Feature",
        description: "Fork functionality is coming soon! This will allow you to create variations based on this result.",
      });
    }
  };

  return (
    <div className="flex gap-2">
      <Button
        size="sm"
        variant="outline"
        onClick={handleView}
        className="border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white"
      >
        <Eye className="w-3 h-3 mr-1" />
        View
      </Button>
      <Button
        size="sm"
        variant="outline"
        onClick={handleFork}
        className="border-green-600 text-green-400 hover:bg-green-600 hover:text-white"
      >
        <GitBranch className="w-3 h-3 mr-1" />
        Fork
      </Button>
    </div>
  );
};
