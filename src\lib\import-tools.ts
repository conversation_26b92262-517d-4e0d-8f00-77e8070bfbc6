import JSZip from 'jszip';
import type { PromptVariant, Agent, TestResult, NodeTemplate } from '@/store/promptStore'; // Assuming types might be needed
import type { ChainFlow } from './chain-linker'; // Assuming types might be needed

export interface ImportedPromptStudioData {
  manifest?: any; // Parsed manifest.json
  prompts: PromptVariant[];
  agents: Agent[];
  results: TestResult[];
  flows: ChainFlow[];
  nodeTemplates: NodeTemplate[];
  errors?: { itemPath: string; message: string }[];
}

/**
 * Placeholder function to import data from a .promptx ZIP archive.
 * This is a basic implementation focusing on reading the archive structure and manifest.
 * Actual data ingestion into the application's store is not fully implemented here.
 *
 * @param blob The .promptx ZIP file as a Blob.
 * @returns A Promise resolving to an object containing the imported data (or parts of it).
 */
export async function importFromPromptXArchive(blob: Blob): Promise<ImportedPromptStudioData> {
  const importedData: ImportedPromptStudioData = {
    prompts: [],
    agents: [],
    results: [],
    flows: [],
    nodeTemplates: [], // Assuming node templates might also be in .promptx in future
    errors: [],
  };

  try {
    const zip = await JSZip.loadAsync(blob);

    // 1. Read manifest.json
    const manifestFile = zip.file('manifest.json');
    if (manifestFile) {
      try {
        const manifestContent = await manifestFile.async('string');
        importedData.manifest = JSON.parse(manifestContent);
        console.log('[importFromPromptXArchive] Manifest loaded:', importedData.manifest);
      } catch (e: any) {
        console.error('[importFromPromptXArchive] Failed to parse manifest.json:', e);
        importedData.errors?.push({ itemPath: 'manifest.json', message: `Failed to parse: ${e.message}` });
      }
    } else {
      console.warn('[importFromPromptXArchive] manifest.json not found in archive.');
      importedData.errors?.push({ itemPath: 'manifest.json', message: 'File not found in archive.' });
      // Depending on strictness, might return or throw here
    }

    // 2. Read prompts (example)
    const promptsFolder = zip.folder('prompts');
    if (promptsFolder && importedData.manifest?.items?.prompts) {
      for (const promptId of importedData.manifest.items.prompts) {
        const promptFile = promptsFolder.file(`${promptId}.json`);
        if (promptFile) {
          try {
            const promptContent = await promptFile.async('string');
            importedData.prompts.push(JSON.parse(promptContent) as PromptVariant);
          } catch (e: any) {
            console.error(`[importFromPromptXArchive] Failed to parse prompt ${promptId}.json:`, e);
            importedData.errors?.push({ itemPath: `prompts/${promptId}.json`, message: `Failed to parse: ${e.message}` });
          }
        }
      }
    }

    // TODO: Implement similar reading for agents, results, flows, nodeTemplates
    // based on manifest.items and their respective folders.

    // Helper to parse item JSON and handle date conversions
    const parseItem = async <T>(file: JSZip.JSZipObject | null, path: string): Promise<T | null> => {
      if (!file) {
        importedData.errors?.push({ itemPath: path, message: 'File not found in archive.' });
        return null;
      }
      try {
        const content = await file.async('string');
        const item = JSON.parse(content);
        // Convert common date fields (add more as needed per type)
        if (item.createdAt && typeof item.createdAt === 'string') item.createdAt = new Date(item.createdAt);
        if (item.updatedAt && typeof item.updatedAt === 'string') item.updatedAt = new Date(item.updatedAt);
        if (item.timestamp && typeof item.timestamp === 'string') item.timestamp = new Date(item.timestamp);
        if (item.metadata?.createdAt && typeof item.metadata.createdAt === 'string') item.metadata.createdAt = new Date(item.metadata.createdAt);
        if (item.metadata?.updatedAt && typeof item.metadata.updatedAt === 'string') item.metadata.updatedAt = new Date(item.metadata.updatedAt);
        return item as T;
      } catch (e: any) {
        importedData.errors?.push({ itemPath: path, message: `Failed to parse JSON: ${e.message}` });
        return null;
      }
    };

    // Read agents
    const agentsFolder = zip.folder('agents');
    if (agentsFolder && importedData.manifest?.items?.agents) {
      for (const agentId of importedData.manifest.items.agents) {
        const item = await parseItem<Agent>(agentsFolder.file(`${agentId}.json`), `agents/${agentId}.json`);
        if (item) importedData.agents.push(item);
      }
    }

    // Read results
    const resultsFolder = zip.folder('results');
    if (resultsFolder && importedData.manifest?.items?.results) {
      for (const resultId of importedData.manifest.items.results) {
        const item = await parseItem<TestResult>(resultsFolder.file(`${resultId}.json`), `results/${resultId}.json`);
        if (item) importedData.results.push(item);
      }
    }

    // Read flows
    const flowsFolder = zip.folder('flows');
    if (flowsFolder && importedData.manifest?.items?.flows) {
      for (const flowId of importedData.manifest.items.flows) {
        const item = await parseItem<ChainFlow>(flowsFolder.file(`${flowId}.json`), `flows/${flowId}.json`);
        if (item) importedData.flows.push(item);
      }
    }

    // Read node templates
    const nodeTemplatesFolder = zip.folder('nodeTemplates');
    if (nodeTemplatesFolder && importedData.manifest?.items?.nodeTemplates) { // Assuming manifest might list nodeTemplates
      for (const templateId of importedData.manifest.items.nodeTemplates) {
        const item = await parseItem<NodeTemplate>(nodeTemplatesFolder.file(`${templateId}.json`), `nodeTemplates/${templateId}.json`);
        if (item) importedData.nodeTemplates.push(item);
      }
    }

    console.log('[importFromPromptXArchive] Import parsing complete. Data structure populated.');
    // In a real implementation, this data would be processed and added to the application store (e.g., promptStore).

  } catch (e: any) {
    console.error('[importFromPromptXArchive] Failed to load or process ZIP archive:', e);
    importedData.errors?.push({ itemPath: 'archive_root', message: `ZIP processing error: ${e.message}` });
    // throw e; // Or handle more gracefully
  }

  return importedData;
}
