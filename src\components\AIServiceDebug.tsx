import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Loader2, Zap, CheckCircle, AlertCircle } from 'lucide-react';
import { aiService } from '@/lib/ai-service';
import { toast } from '@/hooks/use-toast';

export const AIServiceDebug = () => {
  const [testPrompt, setTestPrompt] = useState('Hello! Please respond with "AI service is working correctly."');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const testProvider = async (provider: string, model: string) => {
    setIsLoading(true);
    const startTime = Date.now();
    
    try {
      console.log(`🧪 Testing ${provider}:${model}...`);
      
      const response = await aiService.generateResponse({
        provider,
        model,
        messages: [
          { role: 'user', content: testPrompt }
        ],
        temperature: 0.1,
        maxTokens: 100
      });

      const duration = Date.now() - startTime;
      
      const result = {
        provider,
        model,
        success: true,
        response: response.content,
        duration,
        tokens: response.usage?.totalTokens || 0,
        timestamp: new Date()
      };

      setResults(prev => [result, ...prev]);
      
      toast({
        title: "Test Successful",
        description: `${provider}:${model} responded in ${duration}ms`,
      });

      console.log(`✅ ${provider}:${model} test successful:`, result);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      const result = {
        provider,
        model,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
        timestamp: new Date()
      };

      setResults(prev => [result, ...prev]);
      
      toast({
        title: "Test Failed",
        description: `${provider}:${model} - ${result.error}`,
        variant: "destructive"
      });

      console.error(`❌ ${provider}:${model} test failed:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const testAllConfigured = async () => {
    const providers = [
      { provider: 'openai', model: 'gpt-4o' },
      { provider: 'anthropic', model: 'claude-3-5-sonnet-latest' },
      { provider: 'google', model: 'gemini-2.5-flash' },
      { provider: 'mistral', model: 'mistral-medium-latest' }
    ];

    for (const { provider, model } of providers) {
      if (aiService.isConfigured(provider)) {
        await testProvider(provider, model);
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        console.log(`⏭️ Skipping ${provider} - not configured`);
      }
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-slate-800/50 border-slate-700 p-6">
        <h2 className="text-2xl font-bold text-slate-200 mb-4 flex items-center gap-2">
          <Zap className="h-6 w-6" />
          AI Service Debug
        </h2>
        
        <div className="space-y-4">
          <div>
            <label className="text-slate-200 text-sm font-medium">Test Prompt:</label>
            <Textarea
              value={testPrompt}
              onChange={(e) => setTestPrompt(e.target.value)}
              className="mt-2 bg-slate-900/50 border-slate-600 text-white"
              rows={3}
            />
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={testAllConfigured}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test All Configured'
              )}
            </Button>

            <Button
              onClick={() => testProvider('openai', 'gpt-4o')}
              disabled={isLoading || !aiService.isConfigured('openai')}
              variant="outline"
            >
              Test OpenAI
            </Button>

            <Button
              onClick={() => testProvider('google', 'gemini-2.5-flash')}
              disabled={isLoading || !aiService.isConfigured('google')}
              variant="outline"
            >
              Test Google
            </Button>

            <Button
              onClick={() => testProvider('anthropic', 'claude-3-5-sonnet-latest')}
              disabled={isLoading || !aiService.isConfigured('anthropic')}
              variant="outline"
            >
              Test Anthropic
            </Button>

            <Button
              onClick={clearResults}
              variant="ghost"
              className="text-slate-400"
            >
              Clear Results
            </Button>
          </div>
        </div>
      </Card>

      {results.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700 p-6">
          <h3 className="text-xl font-semibold text-slate-200 mb-4">Test Results</h3>
          <div className="space-y-3">
            {results.map((result, index) => (
              <div key={index} className="bg-slate-900/50 border border-slate-600 rounded p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge variant={result.success ? "default" : "destructive"}>
                      {result.provider}:{result.model}
                    </Badge>
                    {result.success ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-red-500" />
                    )}
                  </div>
                  <div className="text-xs text-slate-400">
                    {result.timestamp.toLocaleTimeString()}
                  </div>
                </div>

                {result.success ? (
                  <div className="space-y-2">
                    <div className="bg-slate-800 rounded p-3 text-sm text-slate-300">
                      {result.response}
                    </div>
                    <div className="flex gap-4 text-xs text-slate-400">
                      <span>Duration: {result.duration}ms</span>
                      <span>Tokens: {result.tokens}</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-red-400 text-sm">
                    Error: {result.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};
