import { describe, it, expect } from 'vitest';
import { interpolateVariables } from '../utils';

describe('interpolateVariables', () => {
  it('should replace {{variable}} patterns', () => {
    const result = interpolateVariables(
      'Hello {{name}}, welcome to {{place}}!',
      { name: '<PERSON>', place: 'Paris' }
    );
    expect(result).toBe('Hello John, welcome to Paris!');
  });

  it('should replace {variable} patterns', () => {
    const result = interpolateVariables(
      'Hello {name}, your age is {age}!',
      { name: '<PERSON>', age: '30' }
    );
    expect(result).toBe('Hello <PERSON>, your age is 30!');
  });

  it('should handle mixed pattern types', () => {
    const result = interpolateVariables(
      'Data: {{item1}} and {item2}.',
      { item1: 'Apple', item2: 'Banana' }
    );
    expect(result).toBe('Data: Apple and Banana.');
  });

  it('should handle missing variables gracefully (leave placeholder)', () => {
    const result = interpolateVariables(
      'Hello {{name}}! Your city is {city}.',
      { name: 'Alice' } // city is missing
    );
    expect(result).toBe('Hello Alice! Your city is {city}.');
  });

  it('should return original text when no variables object provided', () => {
    const result = interpolateVariables('Hello world!');
    expect(result).toBe('Hello world!');
  });

  it('should return original text when variables object is empty', () => {
    const result = interpolateVariables('Hello {{name}}!', {});
    expect(result).toBe('Hello {{name}}!');
  });

  it('should handle variables with special regex characters in their names', () => {
    const result = interpolateVariables(
      'Value for item[0].name is {{item[0].name}}.',
      { 'item[0].name': 'TestValue' }
    );
    expect(result).toBe('Value for item[0].name is TestValue.');
  });

  it('should stringify object values', () => {
    const result = interpolateVariables(
      'Details: {{details}}',
      { details: { color: 'blue', count: 5 } }
    );
    expect(result).toBe('Details: {"color":"blue","count":5}');
  });

  it('should handle number values', () => {
    const result = interpolateVariables(
      'Score: {score}',
      { score: 99 }
    );
    expect(result).toBe('Score: 99');
  });
});
