import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Zap, Settings, Info, Target, Brain, CheckCircle } from 'lucide-react';
import { SCORING_ARCHETYPES } from '@/lib/auto-scoring';
import { toast } from '@/hooks/use-toast';

interface AutoScoringSettings {
  enabled: boolean;
  defaultArchetype: string;
  confidenceThreshold: number;
  enableSandboxMetrics: boolean;
  enableToneAnalysis: boolean;
  enableStructureAnalysis: boolean;
  weightings: {
    fidelity: number;
    adherence: number;
    consistency: number;
    creativity: number;
    accuracy: number;
    toneMatch: number;
    structureScore: number;
  };
}

interface AutoScoringToggleProps {
  onSettingsChange?: (settings: AutoScoringSettings) => void;
}

export const AutoScoringToggle = ({ onSettingsChange }: AutoScoringToggleProps) => {
  const [settings, setSettings] = useState<AutoScoringSettings>({
    enabled: true,
    defaultArchetype: 'technical_documentation',
    confidenceThreshold: 70,
    enableSandboxMetrics: true,
    enableToneAnalysis: true,
    enableStructureAnalysis: true,
    weightings: {
      fidelity: 20,
      adherence: 15,
      consistency: 15,
      creativity: 10,
      accuracy: 20,
      toneMatch: 10,
      structureScore: 10
    }
  });

  useEffect(() => {
    // Load settings from localStorage
    const savedSettings = localStorage.getItem('auto-scoring-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Failed to parse auto-scoring settings:', error);
      }
    }
  }, []);

  useEffect(() => {
    // Save settings to localStorage and notify parent
    localStorage.setItem('auto-scoring-settings', JSON.stringify(settings));
    onSettingsChange?.(settings);
  }, [settings, onSettingsChange]);

  const handleToggle = (field: keyof AutoScoringSettings, value: boolean) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleWeightingChange = (metric: string, value: number) => {
    setSettings(prev => ({
      ...prev,
      weightings: {
        ...prev.weightings,
        [metric]: value
      }
    }));
  };

  const resetToDefaults = () => {
    setSettings({
      enabled: true,
      defaultArchetype: 'technical_documentation',
      confidenceThreshold: 70,
      enableSandboxMetrics: true,
      enableToneAnalysis: true,
      enableStructureAnalysis: true,
      weightings: {
        fidelity: 20,
        adherence: 15,
        consistency: 15,
        creativity: 10,
        accuracy: 20,
        toneMatch: 10,
        structureScore: 10
      }
    });
    
    toast({
      title: "Settings Reset",
      description: "Auto-scoring settings have been reset to defaults",
    });
  };

  const totalWeighting = Object.values(settings.weightings).reduce((sum, weight) => sum + weight, 0);

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-yellow-400" />
          Auto Scoring System
          <Badge variant={settings.enabled ? "default" : "secondary"}>
            {settings.enabled ? "Enabled" : "Disabled"}
          </Badge>
        </CardTitle>
        <CardDescription>
          Automatically score output fidelity and tone match using AI-powered analysis
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
            <TabsTrigger value="weightings">Weightings</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-slate-200">Enable Auto Scoring</Label>
                <p className="text-sm text-slate-400">
                  Automatically analyze and score all AI responses
                </p>
              </div>
              <Switch
                checked={settings.enabled}
                onCheckedChange={(checked) => handleToggle('enabled', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-slate-200">Default Response Archetype</Label>
              <Select 
                value={settings.defaultArchetype} 
                onValueChange={(value) => setSettings(prev => ({ ...prev, defaultArchetype: value }))}
              >
                <SelectTrigger className="bg-slate-900/50 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(SCORING_ARCHETYPES).map(([key, archetype]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex flex-col">
                        <span>{archetype.name}</span>
                        <span className="text-xs text-slate-400">{archetype.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-slate-400">
                Default archetype used when none is specified
              </p>
            </div>

            <div className="space-y-2">
              <Label className="text-slate-200">
                Confidence Threshold: {settings.confidenceThreshold}%
              </Label>
              <Slider
                value={[settings.confidenceThreshold]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, confidenceThreshold: value }))}
                max={100}
                min={0}
                step={5}
                className="w-full"
              />
              <p className="text-xs text-slate-400">
                Minimum confidence level for auto-scoring results
              </p>
            </div>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-slate-200 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Sandbox Metrics
                </Label>
                <p className="text-sm text-slate-400">
                  Include performance metrics in scoring
                </p>
              </div>
              <Switch
                checked={settings.enableSandboxMetrics}
                onCheckedChange={(checked) => handleToggle('enableSandboxMetrics', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-slate-200 flex items-center gap-2">
                  <Brain className="h-4 w-4" />
                  Tone Analysis
                </Label>
                <p className="text-sm text-slate-400">
                  Analyze response tone and style matching
                </p>
              </div>
              <Switch
                checked={settings.enableToneAnalysis}
                onCheckedChange={(checked) => handleToggle('enableToneAnalysis', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-slate-200 flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Structure Analysis
                </Label>
                <p className="text-sm text-slate-400">
                  Evaluate response structure and formatting
                </p>
              </div>
              <Switch
                checked={settings.enableStructureAnalysis}
                onCheckedChange={(checked) => handleToggle('enableStructureAnalysis', checked)}
              />
            </div>

            <div className="bg-slate-900/30 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Info className="h-4 w-4 text-blue-400" />
                <span className="text-sm font-medium text-slate-200">Analysis Features</span>
              </div>
              <ul className="text-xs text-slate-400 space-y-1">
                <li>• Archetype detection and matching</li>
                <li>• Sentiment and tone analysis</li>
                <li>• Content structure evaluation</li>
                <li>• Factual accuracy assessment</li>
                <li>• Response consistency checking</li>
              </ul>
            </div>
          </TabsContent>

          <TabsContent value="weightings" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-slate-200">Total Weighting</span>
                <Badge variant={totalWeighting === 100 ? "default" : "destructive"}>
                  {totalWeighting}%
                </Badge>
              </div>

              {Object.entries(settings.weightings).map(([metric, weight]) => (
                <div key={metric} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-slate-200 capitalize">
                      {metric.replace(/([A-Z])/g, ' $1').trim()}: {weight}%
                    </Label>
                  </div>
                  <Slider
                    value={[weight]}
                    onValueChange={([value]) => handleWeightingChange(metric, value)}
                    max={50}
                    min={0}
                    step={5}
                    className="w-full"
                  />
                </div>
              ))}

              {totalWeighting !== 100 && (
                <div className="bg-yellow-900/20 border border-yellow-600/30 p-3 rounded-lg">
                  <p className="text-sm text-yellow-300">
                    ⚠️ Weightings should total 100% for accurate scoring
                  </p>
                </div>
              )}
            </div>

            <div className="flex gap-2">
              <Button
                onClick={resetToDefaults}
                variant="outline"
                size="sm"
                className="flex-1"
              >
                Reset to Defaults
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        {settings.enabled && (
          <div className="mt-4 p-3 bg-green-900/20 border border-green-600/30 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-sm font-medium text-green-300">Auto Scoring Active</span>
            </div>
            <p className="text-xs text-green-400 mt-1">
              All responses will be automatically analyzed and scored using the configured settings.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
