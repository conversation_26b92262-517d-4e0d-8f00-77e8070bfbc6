
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Search, GitBranch, Archive, Star } from 'lucide-react';
import { useState, useEffect } from 'react';
import { PromptHistoryActions } from '@/components/PromptHistoryActions';
import { EmptyState } from '@/components/ui/empty-state';
import { TestResult } from '@/store/promptStore';
import { usePromptStore } from '@/hooks/usePromptStore';

export const PromptHistory = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [historyItems, setHistoryItems] = useState<TestResult[]>([]);
  const { getResults } = usePromptStore();

  useEffect(() => {
    // Load results from store
    try {
      const storeResults = getResults();
      console.log('📚 PromptHistory loaded results:', storeResults.length, 'items');
      setHistoryItems(storeResults);
    } catch (error) {
      console.error('❌ Failed to load results:', error);
      setHistoryItems([]);
    }
  }, [getResults]);

  // Fallback mock data if no real results exist
  const mockHistoryItems: TestResult[] = [
    {
      id: '1',
      timestamp: new Date('2024-01-15'),
      type: 'prompt_test',
      data: {
        title: "Creative Writing Assistant",
        prompt: "You are a creative writing assistant that helps users develop compelling narratives...",
        version: "v2.1",
        status: "greenlit",
        models: ["GPT-4", "Claude"],
        averageScore: 89
      },
      scores: { fidelity: 89, adherence: 85, consistency: 92, creativity: 88, accuracy: 90 }
    },
    {
      id: '2', 
      timestamp: new Date('2024-01-14'),
      type: 'prompt_test',
      data: {
        title: "Code Review Bot",
        prompt: "Review the following code and provide constructive feedback...",
        version: "v1.5", 
        status: "testing",
        models: ["GPT-4", "Gemini"],
        averageScore: 76
      },
      scores: { fidelity: 76, adherence: 78, consistency: 74, creativity: 70, accuracy: 82 }
    },
    {
      id: '3',
      timestamp: new Date('2024-01-12'), 
      type: 'prompt_test',
      data: {
        title: "Data Analysis Helper",
        prompt: "Analyze the following dataset and provide insights...",
        version: "v3.0",
        status: "archived", 
        models: ["Claude", "Mistral"],
        averageScore: 92
      },
      scores: { fidelity: 92, adherence: 90, consistency: 95, creativity: 85, accuracy: 94 }
    }
  ];

  // Use real data if available, otherwise show mock data
  const displayItems = historyItems.length > 0 ? historyItems : mockHistoryItems;

  const filteredItems = displayItems.filter(item => {
    const title = item.data?.title || item.data?.originalPrompt || 'Untitled';
    const prompt = item.data?.prompt || item.data?.originalPrompt || '';
    return title.toLowerCase().includes(searchTerm.toLowerCase()) ||
           prompt.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'greenlit': return 'bg-green-600';
      case 'testing': return 'bg-yellow-600';
      case 'archived': return 'bg-slate-600';
      default: return 'bg-slate-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'greenlit': return <Star className="w-3 h-3" />;
      case 'testing': return <GitBranch className="w-3 h-3" />;
      case 'archived': return <Archive className="w-3 h-3" />;
      default: return null;
    }
  };

  const handleView = (result: TestResult) => {
    console.log('Viewing result:', result);
    // TODO: Implement result viewing modal/page
  };

  const handleFork = (result: TestResult) => {
    console.log('Forking result:', result);
    // TODO: Implement result forking functionality
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-200">Prompt History</h2>
        <div className="relative w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <Input
            placeholder="Search prompts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-slate-800 border-slate-600 text-white"
          />
        </div>
      </div>

      {filteredItems.length === 0 ? (
        <EmptyState
          icon={Search}
          title={searchTerm ? "No results found" : "No prompt history"}
          description={searchTerm ? `No prompts match "${searchTerm}"` : "Your prompt test history will appear here"}
          action={searchTerm ? {
            label: "Clear search",
            onClick: () => setSearchTerm('')
          } : undefined}
        />
      ) : (
        <div className="grid gap-4">
          {filteredItems.map((item) => (
            <Card key={item.id} className="bg-slate-800/50 border-slate-700 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-slate-200">
                      {item.data?.title || item.data?.variantName || `${item.type} #${item.id}`}
                    </h3>
                    <Badge className={`${getStatusColor(item.data?.status || 'completed')} text-white flex items-center gap-1`}>
                      {getStatusIcon(item.data?.status || 'completed')}
                      {item.data?.status || 'completed'}
                    </Badge>
                    <Badge variant="outline" className="border-slate-500 text-slate-300">
                      {item.data?.version || 'v1.0'}
                    </Badge>
                    <Badge variant="outline" className="border-blue-500 text-blue-300">
                      {item.type}
                    </Badge>
                  </div>
                  <p className="text-slate-400 text-sm mb-3">
                    {(item.data?.prompt || item.data?.originalPrompt || 'No prompt available')?.slice(0, 150)}...
                  </p>
                  <div className="flex items-center gap-4 text-sm text-slate-400">
                    <span>Modified: {item.timestamp.toLocaleDateString()}</span>
                    <span>Models: {item.data?.models?.join(', ') || item.data?.modelResults?.map((r: any) => r.model).join(', ') || 'N/A'}</span>
                    <span>Avg Score: {item.data?.averageScore || Math.round((item.scores?.fidelity || 0))}%</span>
                  </div>
                </div>
                <PromptHistoryActions 
                  result={item}
                  onView={handleView}
                  onFork={handleFork}
                />
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
