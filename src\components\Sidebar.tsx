
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  TestTube,
  BarChart3,
  History,
  Settings as SettingsIcon,
  Zap,
  Users,
  GitBranch,
  Code,
  Link
} from 'lucide-react';

interface SidebarProps {
  activeView: string;
  onViewChange: (view: string) => void;
}

export const Sidebar = ({ activeView, onViewChange }: SidebarProps) => {
  const menuItems = [
    { id: 'tester', label: 'Prompt Tester', icon: TestTube },
    { id: 'variations', label: 'Packs & Variations', icon: GitBranch },
    { id: 'agents', label: 'Agent Studio', icon: Users },
    { id: 'projects', label: 'Project Simulator', icon: Code },
    { id: 'chain-linker', label: 'Chain Linker Canvas', icon: Link },
    { id: 'results', label: 'Results Dashboard', icon: BarChart3 },
    { id: 'history', label: 'Prompt History', icon: History },
    { id: 'settings', label: 'Settings', icon: SettingsIcon },
  ];

  return (
    <div className="w-64 bg-slate-800/50 backdrop-blur-sm border-r border-slate-700 h-screen p-4">
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <Zap className="h-6 w-6 text-yellow-400" />
          <span className="font-semibold">Sandbox</span>
        </div>
        <Card className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-blue-500/30 p-4">
          <p className="text-sm text-blue-200">
            v2.0 - Full simulation suite ready
          </p>
        </Card>
      </div>

      <nav className="space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          return (
            <Button
              key={item.id}
              variant={activeView === item.id ? "default" : "ghost"}
              className={`w-full justify-start gap-3 ${
                activeView === item.id 
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white" 
                  : "hover:bg-slate-700 text-slate-300"
              }`}
              onClick={() => onViewChange(item.id)}
            >
              <Icon className="h-4 w-4" />
              {item.label}
            </Button>
          );
        })}
      </nav>
    </div>
  );
};
