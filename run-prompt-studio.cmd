@echo off
:: Prompt Studio - Simple Launcher
:: This launcher handles all the setup and starts the application

title Prompt Studio Launcher

echo.
echo ===============================================
echo          💠 Prompt Studio Launcher
echo ===============================================
echo.

:: Check Node.js
echo Checking prerequisites...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Then run this script again.
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% detected

:: Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm %NPM_VERSION% detected
echo.

:: Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    echo This will take a few minutes on first run...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
    echo.
)

:: Setup environment file
if not exist ".env" (
    echo 🔧 Setting up environment configuration...
    echo.
    
    echo # Prompt Studio Environment Configuration > .env
    echo # Copy this file and add your actual API keys >> .env
    echo. >> .env
    echo # OpenAI API Key (Required) >> .env
    echo # Get from: https://platform.openai.com/api-keys >> .env
    echo VITE_OPENAI_API_KEY=your_openai_api_key_here >> .env
    echo. >> .env
    echo # Anthropic API Key (Required) >> .env
    echo # Get from: https://console.anthropic.com/ >> .env
    echo VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here >> .env
    echo. >> .env
    echo # Optional: Additional AI providers >> .env
    echo # VITE_GEMINI_API_KEY=your_gemini_api_key_here >> .env
    echo # VITE_MISTRAL_API_KEY=your_mistral_api_key_here >> .env
    
    echo ✅ Environment file created
    echo.
    echo ⚠️  IMPORTANT: You need to add your AI API keys to the .env file
    echo.
    echo API Keys needed:
    echo - OpenAI: https://platform.openai.com/api-keys
    echo - Anthropic: https://console.anthropic.com/
    echo.
    
    set /p EDIT_ENV="Would you like to edit the .env file now? (y/n): "
    if /i "%EDIT_ENV%"=="y" (
        echo Opening .env file in notepad...
        start /wait notepad .env
        echo.
        echo ✅ Environment file updated
    ) else (
        echo.
        echo ⚠️  Remember to edit .env file before using AI features
    )
    echo.
)

:: Check if API keys are configured
findstr /C:"your_openai_api_key_here" .env >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  Warning: Default API keys detected in .env file
    echo For full functionality, please add your actual API keys
    echo.
)

:: Final check - verify build works
echo 🔍 Verifying project setup...
npm run build >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Build verification failed
    echo There may be configuration issues
    echo.
    echo Trying to start anyway...
    echo.
) else (
    echo ✅ Project setup verified
    echo.
)

:: Start the development server
echo 🚀 Starting Prompt Studio...
echo.
echo ┌─────────────────────────────────────────────┐
echo │  Prompt Studio will be available at:        │
echo │  http://localhost:5173                      │
echo │                                             │
echo │  Your browser should open automatically     │
echo │  Press Ctrl+C to stop the server           │
echo └─────────────────────────────────────────────┘
echo.

:: Wait a moment then open browser
timeout /t 3 /nobreak >nul
start "" "http://localhost:5173"

:: Start the development server
npm run dev

:: If we get here, the server stopped
echo.
echo 👋 Prompt Studio has stopped
echo.
pause
