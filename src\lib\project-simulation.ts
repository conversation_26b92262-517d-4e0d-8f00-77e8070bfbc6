import { Agent, PromptVariant } from '@/store/promptStore';
import { aiService } from './ai-service';
import { agentSimulationService } from './agent-simulation';
import { TestResult } from './export-tools';

export interface ProjectStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  result?: string;
  duration?: number;
  timestamp?: Date;
}

export interface ProjectScenario {
  id: string;
  name: string;
  description: string;
  prompt: string;
  systemPrompt?: string;
  variables?: Record<string, string>;
  expectedOutcome?: string;
  tags?: string[];
}

export interface ProjectSimulation {
  id: string;
  name: string;
  description: string;
  scenarios: ProjectScenario[];
  agents: Agent[];
  prompts: PromptVariant[];
  results: ProjectSimulationResult[];
  createdAt: Date;
  updatedAt: Date;
  status: 'draft' | 'in_progress' | 'completed';
  metadata?: Record<string, any>;
}

export interface ProjectSimulationResult {
  id: string;
  scenarioId: string;
  agentId?: string;
  promptId?: string;
  response: string;
  score?: number;
  metrics?: Record<string, number>;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ProjectSimulationRequest {
  name: string;
  description: string;
  scenarios: ProjectScenario[];
  agents: Agent[];
  prompts: PromptVariant[];
  settings?: {
    maxTokens?: number;
    temperature?: number;
    evaluateResults?: boolean;
    parallelExecution?: boolean;
  };
}

export interface ProjectSimulationSummary {
  totalScenarios: number;
  completedScenarios: number;
  averageScore: number;
  topPerformingAgent?: {
    id: string;
    name: string;
    score: number;
  };
  topPerformingPrompt?: {
    id: string;
    name: string;
    score: number;
  };
  duration: number;
  timestamp: Date;
}

export interface ProjectHealthAnalysis {
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  riskFactorsIdentified: string[];
}

export interface ProjectSimulationResponse {
  simulation: ProjectSimulation;
  summary: ProjectSimulationSummary;
  healthAnalysis: ProjectHealthAnalysis;
  testResults: TestResult[];
}

export interface ProjectSimulationExecutionResult {
  name: string;
  description: string;
  projectType: string;
  complexity: 'low' | 'medium' | 'high';
  steps: ProjectStep[];
  agentContributions: any[];
  promptUsage: any[];
  insights: {
    strengths: string[];
    recommendations: string[];
  };
  successRate: number;
  overallQuality: number;
  totalEstimatedTime: number;
  actualTime: number;
  timestamp: Date;
}

class ProjectSimulationService {
  /**
   * Run a project simulation with multiple scenarios, agents, and prompts
   */
  async runSimulation(request: ProjectSimulationRequest): Promise<ProjectSimulationResponse> {
    console.log('Starting project simulation:', request.name);
    const startTime = Date.now();
    
    // Create a new simulation
    const simulation: ProjectSimulation = {
      id: crypto.randomUUID(),
      name: request.name,
      description: request.description,
      scenarios: request.scenarios,
      agents: request.agents,
      prompts: request.prompts,
      results: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'in_progress'
    };

    const testResults: TestResult[] = [];
    
    // Process each scenario
    for (const scenario of request.scenarios) {
      console.log(`Processing scenario: ${scenario.name}`);
      
      // Test with agents
      for (const agent of request.agents.filter(a => a.enabled !== false)) {
        try {
          const agentResult = await this.runAgentScenario(agent, scenario, request.settings);
          simulation.results.push(agentResult);
          
          // Create test result for storage
          testResults.push({
            id: crypto.randomUUID(),
            timestamp: new Date(),
            type: 'agent_simulation',
            scores: {
              accuracy: agentResult.score || 0,
              relevance: agentResult.metrics?.relevance || 0
            },
            data: {
              scenarioName: scenario.name,
              agentName: agent.name,
              prompt: scenario.prompt,
              systemPrompt: scenario.systemPrompt,
              response: agentResult.response,
              metrics: agentResult.metrics
            }
          });
        } catch (error) {
          console.error(`Error running agent ${agent.name} on scenario ${scenario.name}:`, error);
        }
      }
      
      // Test with prompts
      for (const prompt of request.prompts) {
        try {
          const promptResult = await this.runPromptScenario(prompt, scenario, request.settings);
          simulation.results.push(promptResult);
          
          // Create test result for storage
          testResults.push({
            id: crypto.randomUUID(),
            timestamp: new Date(),
            type: 'prompt_test',
            scores: {
              accuracy: promptResult.score || 0,
              relevance: promptResult.metrics?.relevance || 0
            },
            data: {
              scenarioName: scenario.name,
              promptName: prompt.name,
              prompt: prompt.prompt,
              systemPrompt: prompt.systemPrompt,
              response: promptResult.response,
              metrics: promptResult.metrics
            }
          });
        } catch (error) {
          console.error(`Error running prompt ${prompt.name} on scenario ${scenario.name}:`, error);
        }
      }
    }
    
    // Update simulation status
    simulation.status = 'completed';
    simulation.updatedAt = new Date();
    
    // Generate summary
    const summary = this.generateSummary(simulation, startTime);
    
    // Analyze project health
    const healthAnalysis = await this.analyzeProjectHealth(simulation);
    
    return {
      simulation,
      summary,
      healthAnalysis,
      testResults
    };
  }

  /**
   * Run project simulation (for ProjectSimulator component)
   */
  async runProjectSimulation(request: {
    name: string;
    description: string;
    projectType: string;
    complexity: 'low' | 'medium' | 'high';
    agents: Agent[];
    prompts: PromptVariant[];
    scenario?: string;
  }): Promise<ProjectSimulationExecutionResult> {
    const startTime = Date.now();
    
    // Generate project steps based on complexity
    const steps = this.generateProjectSteps(request.projectType, request.complexity);
    
    // Execute steps with agents and prompts
    const agentContributions: any[] = [];
    const promptUsage: any[] = [];
    
    for (const step of steps) {
      step.status = 'in_progress';
      
      // Simulate step execution
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Use agents for analysis
      for (const agent of request.agents) {
        try {
          const contribution = await this.simulateAgentContribution(agent, step, request.scenario);
          agentContributions.push(contribution);
        } catch (error) {
          console.error('Agent contribution failed:', error);
        }
      }
      
      // Use prompts for implementation
      for (const prompt of request.prompts) {
        try {
          const usage = await this.simulatePromptUsage(prompt, step, request.scenario);
          promptUsage.push(usage);
        } catch (error) {
          console.error('Prompt usage failed:', error);
        }
      }
      
      step.status = Math.random() > 0.1 ? 'completed' : 'failed';
      step.result = step.status === 'completed' ? 
        `${step.name} completed successfully` : 
        `${step.name} encountered issues`;
    }
    
    const actualTime = (Date.now() - startTime) / 1000 / 60; // minutes
    const successRate = (steps.filter(s => s.status === 'completed').length / steps.length) * 100;
    const overallQuality = Math.min(100, successRate + Math.random() * 20 - 10);
    
    return {
      name: request.name,
      description: request.description,
      projectType: request.projectType,
      complexity: request.complexity,
      steps,
      agentContributions,
      promptUsage,
      insights: {
        strengths: this.generateStrengths(successRate),
        recommendations: this.generateRecommendations(successRate, request.complexity)
      },
      successRate,
      overallQuality,
      totalEstimatedTime: this.getEstimatedTime(request.complexity),
      actualTime,
      timestamp: new Date()
    };
  }
  
  /**
   * Run a single scenario with an agent
   */
  private async runAgentScenario(
    agent: Agent, 
    scenario: ProjectScenario,
    settings?: ProjectSimulationRequest['settings']
  ): Promise<ProjectSimulationResult> {
    const result = await agentSimulationService.runSimulation({
      agents: [agent],
      scenario: this.processScenarioVariables(scenario),
      temperature: settings?.temperature || 0.7,
      maxTokens: settings?.maxTokens || 1024
    });
    
    const agentResponse = result.agentResponses[0];
    
    return {
      id: crypto.randomUUID(),
      scenarioId: scenario.id,
      agentId: agent.id,
      response: agentResponse.response,
      score: agentResponse.confidence,
      metrics: {
        relevance: this.calculateRelevance(agentResponse.response, scenario.expectedOutcome),
        coherence: this.calculateCoherence(agentResponse.response),
        specificity: agentResponse.keyInsights?.length || 0
      },
      timestamp: new Date(),
      metadata: {
        duration: result.metadata.duration,
        tokens: result.metadata.totalTokens,
        model: agentResponse.aiResponse?.model,
        provider: agentResponse.aiResponse?.provider
      }
    };
  }
  
  /**
   * Run a single scenario with a prompt
   */
  private async runPromptScenario(
    prompt: PromptVariant,
    scenario: ProjectScenario,
    settings?: ProjectSimulationRequest['settings']
  ): Promise<ProjectSimulationResult> {
    // Combine prompt with scenario
    const combinedPrompt = `${prompt.prompt}\n\nScenario: ${this.processScenarioVariables(scenario)}`;
    
    const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [];
    
    if (prompt.systemPrompt) {
      messages.push({ role: 'system' as const, content: prompt.systemPrompt });
    }
    messages.push({ role: 'user' as const, content: combinedPrompt });

    const response = await aiService.generateResponse({
      provider: 'openai', // Could be configurable
      model: 'gpt-4',     // Could be configurable
      messages,
      temperature: settings?.temperature || 0.7,
      maxTokens: settings?.maxTokens || 1024
    });
    
    return {
      id: crypto.randomUUID(),
      scenarioId: scenario.id,
      promptId: prompt.id,
      response: response.content,
      score: this.calculateScore(response.content, scenario.expectedOutcome),
      metrics: {
        relevance: this.calculateRelevance(response.content, scenario.expectedOutcome),
        coherence: this.calculateCoherence(response.content),
        specificity: this.calculateSpecificity(response.content)
      },
      timestamp: new Date(),
      metadata: {
        duration: 0, // Not tracked directly in aiService response
        tokens: response.usage?.totalTokens || 0,
        model: response.model,
        provider: response.provider
      }
    };
  }

  private generateProjectSteps(projectType: string, complexity: 'low' | 'medium' | 'high'): ProjectStep[] {
    const baseSteps = [
      { name: 'Project Planning', description: 'Define requirements and architecture' },
      { name: 'Design Phase', description: 'Create UI/UX designs and system architecture' },
      { name: 'Development Setup', description: 'Set up development environment and tools' },
      { name: 'Core Implementation', description: 'Implement core functionality' },
      { name: 'Testing', description: 'Unit and integration testing' },
      { name: 'Deployment', description: 'Deploy to production environment' }
    ];

    const complexitySteps = {
      low: [],
      medium: [
        { name: 'API Integration', description: 'Integrate external APIs and services' },
        { name: 'Performance Optimization', description: 'Optimize application performance' }
      ],
      high: [
        { name: 'API Integration', description: 'Integrate external APIs and services' },
        { name: 'Security Implementation', description: 'Implement security measures' },
        { name: 'Performance Optimization', description: 'Optimize application performance' },
        { name: 'Scalability Planning', description: 'Plan for scalability and load handling' },
        { name: 'Documentation', description: 'Create comprehensive documentation' }
      ]
    };

    const allSteps = [...baseSteps, ...complexitySteps[complexity]];
    
    return allSteps.map((step, index) => ({
      id: `step_${index}`,
      name: step.name,
      description: step.description,
      status: 'pending' as const,
      timestamp: new Date()
    }));
  }

  private async simulateAgentContribution(agent: Agent, step: ProjectStep, scenario?: string): Promise<any> {
    return {
      agentId: agent.id,
      agentName: agent.name,
      stepId: step.id,
      contribution: `${agent.name} provided insights for ${step.name}`,
      timestamp: new Date()
    };
  }

  private async simulatePromptUsage(prompt: PromptVariant, step: ProjectStep, scenario?: string): Promise<any> {
    return {
      promptId: prompt.id,
      promptName: prompt.name,
      stepId: step.id,
      usage: `Used ${prompt.name} for ${step.name}`,
      timestamp: new Date()
    };
  }

  private generateStrengths(successRate: number): string[] {
    const strengths = [];
    if (successRate > 80) {
      strengths.push('High success rate in project execution');
    }
    if (successRate > 60) {
      strengths.push('Good project planning and execution');
    }
    strengths.push('Effective use of AI agents and prompts');
    return strengths;
  }

  private generateRecommendations(successRate: number, complexity: 'low' | 'medium' | 'high'): string[] {
    const recommendations = [];
    if (successRate < 70) {
      recommendations.push('Consider breaking down complex tasks into smaller steps');
    }
    if (complexity === 'high') {
      recommendations.push('Implement additional quality assurance measures');
    }
    recommendations.push('Regular monitoring and feedback loops');
    return recommendations;
  }

  private getEstimatedTime(complexity: 'low' | 'medium' | 'high'): number {
    const timeMap = { low: 160, medium: 320, high: 480 };
    return timeMap[complexity];
  }
  
  /**
   * Generate a summary of the simulation results
   */
  private generateSummary(simulation: ProjectSimulation, startTime: number): ProjectSimulationSummary {
    const completedScenarios = new Set(simulation.results.map(r => r.scenarioId)).size;
    
    // Calculate average score
    const scores = simulation.results.map(r => r.score || 0).filter(s => s > 0);
    const averageScore = scores.length > 0 
      ? scores.reduce((sum, score) => sum + score, 0) / scores.length 
      : 0;
    
    // Find top performing agent
    const agentScores = new Map<string, { sum: number, count: number }>();
    for (const result of simulation.results.filter(r => r.agentId)) {
      const agentId = result.agentId!;
      const current = agentScores.get(agentId) || { sum: 0, count: 0 };
      agentScores.set(agentId, {
        sum: current.sum + (result.score || 0),
        count: current.count + 1
      });
    }
    
    let topPerformingAgent: ProjectSimulationSummary['topPerformingAgent'] = undefined;
    let highestAgentScore = 0;
    
    agentScores.forEach((scoreData, agentId) => {
      const avgScore = scoreData.sum / scoreData.count;
      if (avgScore > highestAgentScore) {
        highestAgentScore = avgScore;
        const agent = simulation.agents.find(a => a.id === agentId);
        if (agent) {
          topPerformingAgent = {
            id: agentId,
            name: agent.name,
            score: avgScore
          };
        }
      }
    });
    
    // Find top performing prompt
    const promptScores = new Map<string, { sum: number, count: number }>();
    for (const result of simulation.results.filter(r => r.promptId)) {
      const promptId = result.promptId!;
      const current = promptScores.get(promptId) || { sum: 0, count: 0 };
      promptScores.set(promptId, {
        sum: current.sum + (result.score || 0),
        count: current.count + 1
      });
    }
    
    let topPerformingPrompt: ProjectSimulationSummary['topPerformingPrompt'] = undefined;
    let highestPromptScore = 0;
    
    promptScores.forEach((scoreData, promptId) => {
      const avgScore = scoreData.sum / scoreData.count;
      if (avgScore > highestPromptScore) {
        highestPromptScore = avgScore;
        const prompt = simulation.prompts.find(p => p.id === promptId);
        if (prompt) {
          topPerformingPrompt = {
            id: promptId,
            name: prompt.name,
            score: avgScore
          };
        }
      }
    });
    
    return {
      totalScenarios: simulation.scenarios.length,
      completedScenarios,
      averageScore,
      topPerformingAgent,
      topPerformingPrompt,
      duration: Date.now() - startTime,
      timestamp: new Date()
    };
  }
  
  /**
   * Analyze the health of the project based on simulation results
   */
  private async analyzeProjectHealth(simulation: ProjectSimulation): Promise<ProjectHealthAnalysis> {
    // Simulate project health analysis
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    const recommendations: string[] = [];
    const riskFactorsIdentified: string[] = [];

    // Analyze based on simulation results
    if (simulation.results.length > 0) {
      const avgScore = simulation.results.reduce((sum, r) => sum + (r.score || 0), 0) / simulation.results.length;
      
      if (avgScore > 80) {
        strengths.push('High overall performance across scenarios');
      } else if (avgScore < 60) {
        weaknesses.push('Below average performance in key scenarios');
        riskFactorsIdentified.push('Performance concerns');
      }
    }

    // Add some baseline analysis
    if (simulation.scenarios.length > 5) {
      strengths.push('Comprehensive scenario coverage');
    } else {
      recommendations.push('Consider adding more test scenarios');
    }

    if (simulation.agents.length > 3) {
      strengths.push('Diverse agent perspectives included');
    } else {
      recommendations.push('Consider including more diverse agent roles');
    }

    return {
      strengths,
      weaknesses,
      recommendations,
      riskFactorsIdentified
    };
  }
  
  /**
   * Process variables in a scenario
   */
  private processScenarioVariables(scenario: ProjectScenario): string {
    if (!scenario.variables) return scenario.prompt;
    
    let processedPrompt = scenario.prompt;
    for (const [key, value] of Object.entries(scenario.variables)) {
      processedPrompt = processedPrompt.replace(new RegExp(`{{${key}}}`, 'g'), value);
    }
    
    return processedPrompt;
  }
  
  /**
   * Calculate a score based on expected outcome
   */
  private calculateScore(response: string, expectedOutcome?: string): number {
    if (!expectedOutcome) return 70; // Default score when no expected outcome
    
    // Simple heuristic: check for keyword overlap
    const expectedKeywords = this.extractKeywords(expectedOutcome);
    const responseKeywords = this.extractKeywords(response);
    
    const matchingKeywords = expectedKeywords.filter(k => responseKeywords.includes(k));
    const matchRatio = expectedKeywords.length > 0 
      ? matchingKeywords.length / expectedKeywords.length 
      : 0.5;
    
    return Math.round(matchRatio * 100);
  }
  
  /**
   * Calculate relevance score
   */
  private calculateRelevance(response: string, expectedOutcome?: string): number {
    if (!expectedOutcome) return 70;
    
    // Simple implementation - could be enhanced with embeddings/semantic similarity
    const keywords = this.extractKeywords(expectedOutcome);
    let matchCount = 0;
    
    for (const keyword of keywords) {
      if (response.toLowerCase().includes(keyword.toLowerCase())) {
        matchCount++;
      }
    }
    
    return keywords.length > 0 
      ? Math.min(100, Math.round((matchCount / keywords.length) * 100))
      : 70;
  }
  
  /**
   * Calculate coherence score
   */
  private calculateCoherence(response: string): number {
    // Simple heuristic based on sentence length and structure
    const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    if (sentences.length < 2) return 50;
    
    // Check for very short or very long sentences
    const sentenceLengths = sentences.map(s => s.trim().length);
    const avgLength = sentenceLengths.reduce((sum, len) => sum + len, 0) / sentenceLengths.length;
    
    // Penalize if average sentence length is too short or too long
    let coherenceScore = 80;
    if (avgLength < 5) coherenceScore -= 30;
    if (avgLength > 100) coherenceScore -= 20;
    
    // Penalize high variance in sentence length
    const variance = sentenceLengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / sentenceLengths.length;
    if (variance > 500) coherenceScore -= 10;
    
    return Math.max(0, Math.min(100, coherenceScore));
  }
  
  /**
   * Calculate specificity score
   */
  private calculateSpecificity(response: string): number {
    // Count specific elements like numbers, proper nouns, technical terms
    const specifics = [
      response.match(/\d+(\.\d+)?/g)?.length || 0, // Numbers
      response.match(/[A-Z][a-z]+/g)?.length || 0,  // Proper nouns (simplified)
      response.match(/\b[a-z]+tion\b/gi)?.length || 0, // Technical terms (simplified)
    ];
    
    const specificityScore = Math.min(100, specifics.reduce((sum, count) => sum + count, 0) * 5);
    return specificityScore;
  }
  
  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string): string[] {
    // Simple implementation - could be enhanced with NLP
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !['this', 'that', 'with', 'from', 'have', 'what'].includes(word));
  }
}

export const projectSimulationService = new ProjectSimulationService();
