
export interface ErrorReport {
  id: string;
  timestamp: Date;
  message: string;
  stack?: string;
  name?: string;
  context?: ErrorContext;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  fingerprint: string;
  metadata?: {
    userAgent: string;
    url: string;
    userId?: string;
    sessionId?: string;
    [key: string]: any;
  };
}

export interface ErrorContext {
  componentStack?: string;
  userId?: string;
  sessionId?: string;
  [key: string]: any;
}

export interface ErrorHandlingConfig {
  enableRemoteLogging: boolean;
  monitoringServiceUrl?: string;
  apiKey?: string;
  alertThreshold?: number;
}

class ErrorHandlingService {
  private config: ErrorHandlingConfig = {
    enableRemoteLogging: false,
    monitoringServiceUrl: '',
    apiKey: '',
    alertThreshold: 5
  };

  constructor(config?: Partial<ErrorHandlingConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
  }

  configure(config: Partial<ErrorHandlingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  private calculateSeverity(error: Error, context?: ErrorContext): ErrorReport['severity'] {
    // Implement logic to determine severity based on error type, message, and context
    // Example: Check for specific error messages or types
    if (error.name === 'NetworkError' || error.message.includes('Failed to fetch')) {
      return 'high';
    }

    // Check context for user impact
    if (context?.userId) {
      return 'medium';
    }

    // Default severity
    return 'low';
  }

  private generateFingerprint(error: Error): string {
    // Create a hash or simplified string that identifies the error's unique signature
    // This helps in grouping similar errors
    const baseString = `${error.name}-${error.message}`;
    let hash = 0;
    for (let i = 0; i < baseString.length; i++) {
      const char = baseString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return new Uint32Array([hash])[0].toString(36);
  }

  private async storeErrorReport(errorReport: ErrorReport): Promise<void> {
    try {
      // Store the error report locally (e.g., in localStorage or IndexedDB)
      // This allows for later analysis or resending if remote logging fails
      const existingReports = localStorage.getItem('errorReports');
      const reports = existingReports ? JSON.parse(existingReports) : [];
      reports.push(errorReport);
      localStorage.setItem('errorReports', JSON.stringify(reports));
    } catch (storageError) {
      console.error('[ErrorHandler] Failed to store error report:', storageError);
    }
  }

  private async sendToMonitoringService(errorReport: ErrorReport): Promise<void> {
    if (!this.config.enableRemoteLogging || !this.config.monitoringServiceUrl || !this.config.apiKey) {
      console.warn('[ErrorHandler] Remote logging is not configured.');
      return;
    }

    try {
      const response = await fetch(this.config.monitoringServiceUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.config.apiKey
        },
        body: JSON.stringify(errorReport)
      });

      if (!response.ok) {
        console.error('[ErrorHandler] Failed to send error report to monitoring service:', response.status, response.statusText);
      }
    } catch (networkError) {
      console.error('[ErrorHandler] Network error while sending error report:', networkError);
    }
  }

  private async triggerAlert(errorReport: ErrorReport): Promise<void> {
    // Implement logic to trigger alerts based on the error report
    // This could involve sending notifications to administrators or triggering automated rollbacks
    console.warn('[ErrorHandler] Triggering alert for critical error:', errorReport);
    // Example: Send email to admin
    // await sendAdminEmail(errorReport);
  }

  async analyzeError(error: Error, context?: ErrorContext): Promise<any> {
    // Implement advanced error analysis techniques
    // This could involve using AI to understand the root cause of the error
    console.log('[ErrorHandler] Analyzing error:', error, context);
    return {
      analysis: 'This is a placeholder for advanced error analysis',
      suggestions: ['Check the network connection', 'Review the input data']
    };
  }

  async reportError(error: Error, context?: ErrorContext): Promise<void> {
    try {
      const errorReport: ErrorReport = {
        id: crypto.randomUUID(),
        timestamp: new Date(),
        message: error.message,
        stack: error.stack,
        name: error.name,
        context: { ...context }, // Create a copy to avoid read-only issues
        severity: this.calculateSeverity(error, context),
        fingerprint: this.generateFingerprint(error),
        metadata: {
          userAgent: navigator.userAgent,
          url: window.location.href,
          userId: context?.userId,
          sessionId: context?.sessionId
        }
      };

      // Store locally
      await this.storeErrorReport(errorReport);

      // Send to monitoring service if configured
      if (this.config.enableRemoteLogging) {
        await this.sendToMonitoringService(errorReport);
      }

      // Trigger alerts if needed
      if (errorReport.severity === 'critical') {
        await this.triggerAlert(errorReport);
      }

      console.error('[ErrorHandler] Error reported:', errorReport);
    } catch (reportingError) {
      console.error('[ErrorHandler] Failed to report error:', reportingError);
      // Fallback: at least log the original error
      console.error('[ErrorHandler] Original error:', error);
    }
  }
}

export const errorHandlingService = new ErrorHandlingService();
