
import { PromptVariant, Agent } from '@/store/promptStore';

export interface TestResult {
  id: string;
  timestamp: Date | string;
  type: string;
  scores?: Record<string, number>;
  [key: string]: any;
}

export interface ExportFormat {
  id: string;
  name: string;
  extension: string;
  description: string;
  mimeType: string;
}

export interface ExportConfiguration {
  format: string;
  includeMetadata: boolean;
  includeResults: boolean;
  includeAgents: boolean;
  includeVariables: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  filters?: {
    types: string[];
    tags: string[];
    minScore?: number;
  };
}

export interface ExportData {
  prompts?: PromptVariant[];
  agents?: Agent[];
  results?: TestResult[];
  metadata?: {
    exportedAt: Date;
    version: string;
    source: string;
    configuration: ExportConfiguration;
  };
}

export const EXPORT_FORMATS: Record<string, ExportFormat> = {
  promptx: {
    id: 'promptx',
    name: 'PromptX Format',
    extension: '.promptx',
    description: 'Specialized format for prompt engineering tools',
    mimeType: 'application/json'
  },
  json: {
    id: 'json',
    name: 'JSON',
    extension: '.json',
    description: 'Standard JSON format for data interchange',
    mimeType: 'application/json'
  },
  csv: {
    id: 'csv',
    name: 'CSV',
    extension: '.csv',
    description: 'Comma-separated values for spreadsheet applications',
    mimeType: 'text/csv'
  },
  markdown: {
    id: 'markdown',
    name: 'Markdown',
    extension: '.md',
    description: 'Human-readable documentation format',
    mimeType: 'text/markdown'
  },
  yaml: {
    id: 'yaml',
    name: 'YAML',
    extension: '.yaml',
    description: 'Human-readable data serialization standard',
    mimeType: 'text/yaml'
  }
};

class ExportToolsService {
  /**
   * Export data in the specified format
   */
  async exportData(data: ExportData, configuration: ExportConfiguration): Promise<Blob> {
    const format = EXPORT_FORMATS[configuration.format];
    if (!format) {
      throw new Error(`Unsupported export format: ${configuration.format}`);
    }

    // Filter and prepare data based on configuration
    const filteredData = this.filterData(data, configuration);

    switch (configuration.format) {
      case 'promptx':
        return this.exportToPromptX(filteredData, configuration);
      case 'json':
        return this.exportToJSON(filteredData, configuration);
      case 'csv':
        return this.exportToCSV(filteredData, configuration);
      case 'markdown':
        return this.exportToMarkdown(filteredData, configuration);
      case 'yaml':
        return this.exportToYAML(filteredData, configuration);
      default:
        throw new Error(`Export format ${configuration.format} not implemented`);
    }
  }

  /**
   * Export to PromptX format (specialized for prompt engineering)
   */
  private exportToPromptX(data: ExportData, config: ExportConfiguration): Blob {
    const promptxData = {
      version: '1.0',
      format: 'promptx',
      metadata: {
        ...data.metadata,
        schema: 'https://promptx.dev/schema/v1',
        tools: ['Prompt Studio'],
        capabilities: ['prompt_testing', 'agent_simulation', 'variation_testing']
      },
      prompts: data.prompts?.map(prompt => ({
        id: prompt.id,
        name: prompt.name,
        content: {
          prompt: prompt.prompt,
          systemPrompt: prompt.systemPrompt,
          variables: prompt.variables
        },
        metadata: {
          purpose: prompt.purpose,
          tags: [], // PromptVariant doesn't have tags property
          created: new Date().toISOString(),
          version: '1.0'
        },
        testing: {
          supported_models: ['gpt-4', 'claude-3-sonnet'],
          parameters: {
            temperature: { min: 0, max: 2, default: 0.7 },
            max_tokens: { min: 1, max: 4096, default: 1024 }
          }
        }
      })),
      agents: data.agents?.map(agent => ({
        id: agent.id,
        name: agent.name,
        role: agent.role,
        personality: agent.personality,
        expertise: agent.expertise,
        systemPrompt: agent.systemPrompt,
        avatar: agent.avatar
      })),
      results: config.includeResults ? data.results : undefined
    };

    const jsonString = JSON.stringify(promptxData, null, 2);
    return new Blob([jsonString], { type: EXPORT_FORMATS.promptx.mimeType });
  }

  /**
   * Export to standard JSON format
   */
  private exportToJSON(data: ExportData, config: ExportConfiguration): Blob {
    const jsonData = {
      ...data,
      exportConfiguration: config
    };

    const jsonString = JSON.stringify(jsonData, null, 2);
    return new Blob([jsonString], { type: EXPORT_FORMATS.json.mimeType });
  }

  /**
   * Export to CSV format
   */
  private exportToCSV(data: ExportData, config: ExportConfiguration): Blob {
    let csvContent = '';

    if (data.prompts && data.prompts.length > 0) {
      csvContent += 'Type,Name,Content,Purpose,Variables\n';
      
      data.prompts.forEach(prompt => {
        const row = [
          'Prompt',
          this.escapeCsvField(prompt.name),
          this.escapeCsvField(prompt.prompt),
          this.escapeCsvField(prompt.purpose || ''),
          this.escapeCsvField(JSON.stringify(prompt.variables))
        ].join(',');
        csvContent += row + '\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      if (csvContent) csvContent += '\n';
      csvContent += 'Type,Name,Role,Personality,Expertise,System Prompt\n';
      
      data.agents.forEach(agent => {
        const row = [
          'Agent',
          this.escapeCsvField(agent.name),
          this.escapeCsvField(agent.role),
          this.escapeCsvField(agent.personality || ''),
          this.escapeCsvField(agent.expertise?.join(';') || ''),
          this.escapeCsvField(agent.systemPrompt)
        ].join(',');
        csvContent += row + '\n';
      });
    }

    return new Blob([csvContent], { type: EXPORT_FORMATS.csv.mimeType });
  }

  /**
   * Export to Markdown format
   */
  private exportToMarkdown(data: ExportData, config: ExportConfiguration): Blob {
    let markdown = '# Prompt Studio Export\n\n';
    
    if (data.metadata) {
      markdown += `**Exported:** ${data.metadata.exportedAt.toLocaleString()}\n`;
      markdown += `**Version:** ${data.metadata.version}\n\n`;
    }

    if (data.prompts && data.prompts.length > 0) {
      markdown += '## Prompts\n\n';
      
      data.prompts.forEach((prompt, index) => {
        markdown += `### ${index + 1}. ${prompt.name}\n\n`;
        
        if (prompt.purpose) {
          markdown += `**Purpose:** ${prompt.purpose}\n\n`;
        }

        if (prompt.systemPrompt) {
          markdown += '**System Prompt:**\n```\n';
          markdown += prompt.systemPrompt;
          markdown += '\n```\n\n';
        }

        markdown += '**Prompt:**\n```\n';
        markdown += prompt.prompt;
        markdown += '\n```\n\n';

        if (prompt.variables && Object.keys(prompt.variables).length > 0) {
          markdown += '**Variables:**\n';
          Object.entries(prompt.variables).forEach(([key, value]) => {
            markdown += `- **${key}:** ${value}\n`;
          });
          markdown += '\n';
        }

        markdown += '---\n\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      markdown += '## Agents\n\n';
      
      data.agents.forEach((agent, index) => {
        markdown += `### ${index + 1}. ${agent.name}\n\n`;
        markdown += `**Role:** ${agent.role}\n\n`;
        
        if (agent.personality) {
          markdown += `**Personality:** ${agent.personality}\n\n`;
        }
        
        if (agent.expertise && agent.expertise.length > 0) {
          markdown += `**Expertise:** ${agent.expertise.join(', ')}\n\n`;
        }

        markdown += '**System Prompt:**\n```\n';
        markdown += agent.systemPrompt;
        markdown += '\n```\n\n';

        markdown += '---\n\n';
      });
    }

    return new Blob([markdown], { type: EXPORT_FORMATS.markdown.mimeType });
  }

  /**
   * Export to YAML format
   */
  private exportToYAML(data: ExportData, config: ExportConfiguration): Blob {
    // Simple YAML serialization (for complex cases, would use a proper YAML library)
    let yaml = '# Prompt Studio Export\n\n';
    
    if (data.metadata) {
      yaml += 'metadata:\n';
      yaml += `  exportedAt: "${data.metadata.exportedAt.toISOString()}"\n`;
      yaml += `  version: "${data.metadata.version}"\n`;
      yaml += `  source: "${data.metadata.source}"\n\n`;
    }

    if (data.prompts && data.prompts.length > 0) {
      yaml += 'prompts:\n';
      data.prompts.forEach(prompt => {
        yaml += `  - id: "${prompt.id}"\n`;
        yaml += `    name: "${prompt.name}"\n`;
        yaml += `    prompt: |\n`;
        yaml += this.indentText(prompt.prompt, 6);
        
        if (prompt.systemPrompt) {
          yaml += `    systemPrompt: |\n`;
          yaml += this.indentText(prompt.systemPrompt, 6);
        }
        
        if (prompt.purpose) {
          yaml += `    purpose: "${prompt.purpose}"\n`;
        }
        
        if (prompt.variables && Object.keys(prompt.variables).length > 0) {
          yaml += `    variables:\n`;
          Object.entries(prompt.variables).forEach(([key, value]) => {
            yaml += `      ${key}: "${value}"\n`;
          });
        }
        
        yaml += '\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      yaml += 'agents:\n';
      data.agents.forEach(agent => {
        yaml += `  - id: "${agent.id}"\n`;
        yaml += `    name: "${agent.name}"\n`;
        yaml += `    role: "${agent.role}"\n`;
        
        if (agent.personality) {
          yaml += `    personality: "${agent.personality}"\n`;
        }
        
        if (agent.expertise && agent.expertise.length > 0) {
          yaml += `    expertise:\n`;
          agent.expertise.forEach(skill => {
            yaml += `      - "${skill}"\n`;
          });
        }
        
        yaml += `    systemPrompt: |\n`;
        yaml += this.indentText(agent.systemPrompt, 6);
        yaml += '\n';
      });
    }

    return new Blob([yaml], { type: EXPORT_FORMATS.yaml.mimeType });
  }

  /**
   * Filter data based on export configuration
   */
  private filterData(data: ExportData, config: ExportConfiguration): ExportData {
    const filtered: ExportData = {};

    // Filter prompts
    if (data.prompts) {
      filtered.prompts = data.prompts.filter(prompt => {
        // Since PromptVariant doesn't have tags, we'll just include all prompts for now
        return true;
      });
    }

    // Filter agents
    if (data.agents && config.includeAgents) {
      filtered.agents = data.agents;
    }

    // Filter results
    if (data.results && config.includeResults) {
      filtered.results = data.results.filter(result => {
        if (config.dateRange) {
          const resultDate = new Date(result.timestamp);
          if (resultDate < config.dateRange.start || resultDate > config.dateRange.end) {
            return false;
          }
        }

        if (config.filters?.types && config.filters.types.length > 0) {
          if (!config.filters.types.includes(result.type)) {
            return false;
          }
        }

        if (config.filters?.minScore && result.scores) {
          const scores = Object.values(result.scores).filter((score): score is number => typeof score === 'number');
          if (scores.length > 0) {
            const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
            if (avgScore < config.filters.minScore) {
              return false;
            }
          }
        }

        return true;
      });
    }

    // Include metadata if requested
    if (config.includeMetadata && data.metadata) {
      filtered.metadata = data.metadata;
    }

    return filtered;
  }

  /**
   * Download exported data as file
   */
  downloadExport(blob: Blob, filename: string, format: ExportFormat): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.endsWith(format.extension) ? filename : `${filename}${format.extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Generate filename based on export configuration
   */
  generateFilename(config: ExportConfiguration, data: ExportData): string {
    const timestamp = new Date().toISOString().split('T')[0];
    const format = EXPORT_FORMATS[config.format];
    
    let name = 'prompt-studio-export';
    
    if (data.prompts && data.prompts.length > 0) {
      name += `-${data.prompts.length}prompts`;
    }
    
    if (data.agents && data.agents.length > 0) {
      name += `-${data.agents.length}agents`;
    }
    
    if (data.results && data.results.length > 0) {
      name += `-${data.results.length}results`;
    }
    
    return `${name}-${timestamp}${format.extension}`;
  }

  /**
   * Utility methods
   */
  private escapeCsvField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  private indentText(text: string, spaces: number): string {
    const indent = ' '.repeat(spaces);
    return text.split('\n').map(line => indent + line).join('\n') + '\n';
  }

  /**
   * Validate export configuration
   */
  validateConfiguration(config: ExportConfiguration): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!EXPORT_FORMATS[config.format]) {
      errors.push(`Unsupported export format: ${config.format}`);
    }

    if (config.dateRange) {
      if (config.dateRange.start > config.dateRange.end) {
        errors.push('Start date must be before end date');
      }
    }

    if (config.filters?.minScore !== undefined) {
      if (config.filters.minScore < 0 || config.filters.minScore > 100) {
        errors.push('Minimum score must be between 0 and 100');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get available export formats
   */
  getAvailableFormats(): ExportFormat[] {
    return Object.values(EXPORT_FORMATS);
  }

  /**
   * Integration with external services (Catalyst, Notion)
   */
  async exportToCatalyst(data: ExportData, config: any): Promise<{ success: boolean; message: string }> {
    // This would integrate with Catalyst API
    // For now, return a mock response
    return {
      success: true,
      message: 'Successfully exported to Catalyst vault'
    };
  }

  async exportToNotion(data: ExportData, config: any): Promise<{ success: boolean; message: string }> {
    // This would integrate with Notion API
    // For now, return a mock response
    return {
      success: true,
      message: 'Successfully exported to Notion page'
    };
  }
}

export const exportToolsService = new ExportToolsService();

// New standalone functions for .promptx (ZIP) and Catalyst Sync
// These are added alongside the existing ExportToolsService to avoid major refactoring of it immediately.

import JSZip from 'jszip';

export interface StandaloneExportData {
  prompts: PromptVariant[];
  agents?: Agent[];
  results?: TestResult[];
  metadata?: {
    appName?: string;
    appVersion?: string;
    exportDate: string;
    [key: string]: any;
  };
}

export const PROMPT_STUDIO_APP_NAME = 'PromptStudio';
export const PROMPT_STUDIO_APP_VERSION = '1.0.0'; // Manage versioning appropriately

/**
 * Exports data to a .promptx ZIP archive.
 * @param data The data to export.
 * @returns A Promise that resolves with the Blob of the ZIP file.
 */
export async function exportToPromptXArchive(data: StandaloneExportData): Promise<Blob> {
  const zip = new JSZip();

  const manifest = {
    appName: data.metadata?.appName || PROMPT_STUDIO_APP_NAME,
    appVersion: data.metadata?.appVersion || PROMPT_STUDIO_APP_VERSION,
    exportDate: data.metadata?.exportDate || new Date().toISOString(),
    itemCounts: {
      prompts: data.prompts.length,
      agents: data.agents?.length || 0,
      results: data.results?.length || 0,
    },
    customMetadata: data.metadata || {},
    includedPrompts: data.prompts.map(p => p.id),
    includedAgents: data.agents?.map(a => a.id) || [],
    includedResults: data.results?.map(r => r.id) || [],
  };
  zip.file('manifest.json', JSON.stringify(manifest, null, 2));

  const promptsFolder = zip.folder('prompts');
  if (promptsFolder) {
    data.prompts.forEach(prompt => {
      // Ensure createdAt and updatedAt are stringified if they are Date objects
      const promptData = {
        ...prompt,
        createdAt: prompt.createdAt instanceof Date ? prompt.createdAt.toISOString() : prompt.createdAt,
        updatedAt: prompt.updatedAt instanceof Date ? prompt.updatedAt.toISOString() : prompt.updatedAt,
      };
      promptsFolder.file(`${prompt.id}.json`, JSON.stringify(promptData, null, 2));
    });
  }

  if (data.agents && data.agents.length > 0) {
    const agentsFolder = zip.folder('agents');
    if (agentsFolder) {
      data.agents.forEach(agent => {
        // Ensure agent data is serializable (e.g. dates)
         const agentData = {
          ...agent,
          // createdAt: agent.createdAt instanceof Date ? agent.createdAt.toISOString() : agent.createdAt, // If Agent has timestamps
          // updatedAt: agent.updatedAt instanceof Date ? agent.updatedAt.toISOString() : agent.updatedAt,
        };
        agentsFolder.file(`${agent.id}.json`, JSON.stringify(agentData, null, 2));
      });
    }
  }

  if (data.results && data.results.length > 0) {
    const resultsFolder = zip.folder('results');
    if (resultsFolder) {
      data.results.forEach(result => {
        // Ensure result data is serializable (e.g. dates)
        const resultData = {
            ...result,
            timestamp: result.timestamp instanceof Date ? result.timestamp.toISOString() : result.timestamp,
        }
        resultsFolder.file(`${result.id}.json`, JSON.stringify(resultData, null, 2));
      });
    }
  }

  const blob = await zip.generateAsync({ type: 'blob' });
  return blob;
}

/**
 * Utility function to trigger browser download of a Blob.
 */
export function downloadBlobUtil(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Placeholder for Catalyst Sync functionality.
 */
export async function syncWithCatalystPlaceholder(data: StandaloneExportData): Promise<boolean> {
  console.warn('Catalyst sync initiated with data (placeholder):', data);
  alert('Catalyst sync is not implemented yet. This feature requires integration with the Catalyst API.');
  // throw new Error('Catalyst sync not implemented yet.');
  return false; // Simulate failure or no-op
}

/**
 * Exports all application data to a single JSON file (alternative to class method).
 */
export function exportToJsonDump(data: StandaloneExportData): Blob {
  const fullExportData = {
    appName: data.metadata?.appName || PROMPT_STUDIO_APP_NAME,
    appVersion: data.metadata?.appVersion || PROMPT_STUDIO_APP_VERSION,
    exportDate: data.metadata?.exportDate || new Date().toISOString(),
    prompts: data.prompts,
    agents: data.agents || [],
    results: data.results || [],
    customMetadata: data.metadata || {},
  };
  const jsonString = JSON.stringify(fullExportData, null, 2);
  return new Blob([jsonString], { type: 'application/json' });
}
