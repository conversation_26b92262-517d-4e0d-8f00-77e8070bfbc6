
import { PromptVariant, Agent } from '@/store/promptStore';

export interface TestResult {
  id: string;
  timestamp: Date | string;
  type: string;
  scores?: Record<string, number>;
  [key: string]: any;
}

export interface ExportFormat {
  id: string;
  name: string;
  extension: string;
  description: string;
  mimeType: string;
}

export interface ExportConfiguration {
  format: string;
  includeMetadata: boolean;
  includeResults: boolean;
  includeAgents: boolean;
  includeVariables: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  filters?: {
    types: string[];
    tags: string[];
    minScore?: number;
  };
}

export interface ExportData {
  prompts?: PromptVariant[];
  agents?: Agent[];
  results?: TestResult[];
  metadata?: {
    exportedAt: Date;
    version: string;
    source: string;
    configuration: ExportConfiguration;
  };
}

export const EXPORT_FORMATS: Record<string, ExportFormat> = {
  promptx: {
    id: 'promptx',
    name: 'PromptX Format (Archive)', // Clarify it's the archive format
    extension: '.promptx',
    description: 'Standard PromptX ZIP archive for prompt engineering tools, containing manifest, prompts, agents, and results.',
    mimeType: 'application/zip' // Correct MIME type for ZIP
  },
  promptx_single_json: { // Add a new entry for the single file JSON version
    id: 'promptx_single_json',
    name: 'PromptX (Single JSON)',
    extension: '.json', // Output is a JSON file
    description: 'Legacy single-file JSON representation of PromptX data.',
    mimeType: 'application/json'
  },
  json: {
    id: 'json',
    name: 'JSON',
    extension: '.json',
    description: 'Standard JSON format for data interchange',
    mimeType: 'application/json'
  },
  csv: {
    id: 'csv',
    name: 'CSV',
    extension: '.csv',
    description: 'Comma-separated values for spreadsheet applications',
    mimeType: 'text/csv'
  },
  markdown: {
    id: 'markdown',
    name: 'Markdown',
    extension: '.md',
    description: 'Human-readable documentation format',
    mimeType: 'text/markdown'
  },
  yaml: {
    id: 'yaml',
    name: 'YAML',
    extension: '.yaml',
    description: 'Human-readable data serialization standard',
    mimeType: 'text/yaml'
  }
};

class ExportToolsService {
  /**
   * Export data in the specified format
   */
  async exportData(data: ExportData, configuration: ExportConfiguration): Promise<Blob> {
    const format = EXPORT_FORMATS[configuration.format];
    if (!format) {
      throw new Error(`Unsupported export format: ${configuration.format}`);
    }

    // Filter and prepare data based on configuration
    const filteredData = this.filterData(data, configuration);

    switch (configuration.format) {
      // Note: The 'promptx' case in ExportDialog.tsx now directly calls exportToPromptXArchive.
      // This switch case for 'promptx' here would refer to the old single-file JSON logic
      // if EXPORT_FORMATS.promptx still pointed to the class method.
      // We are renaming the class method to avoid this confusion.
      case 'promptx_single_json': // Should match the new ID for the single JSON format
        return this.exportToPromptXSingleFileJson(filteredData, configuration);
      case 'json':
        return this.exportToJSON(filteredData, configuration);
      case 'csv':
        return this.exportToCSV(filteredData, configuration);
      case 'markdown':
        return this.exportToMarkdown(filteredData, configuration);
      case 'yaml':
        return this.exportToYAML(filteredData, configuration);
      default:
        throw new Error(`Export format ${configuration.format} not implemented`);
    }
  }

  /**
   * @deprecated Prefer `exportToPromptXArchive` for the standard ZIP-based .promptx format.
   * This method exports to a legacy single-file JSON representation using some PromptX conventions.
   */
  private exportToPromptXSingleFileJson(data: ExportData, config: ExportConfiguration): Blob {
    const promptxData = {
      version: '1.0',
      format: 'promptx_single_json', // Reflects the specific format ID
      metadata: {
        ...data.metadata,
        schema: 'https://promptx.dev/schema/v1',
        tools: ['Prompt Studio'],
        capabilities: ['prompt_testing', 'agent_simulation', 'variation_testing']
      },
      prompts: data.prompts?.map(prompt => ({
        id: prompt.id,
        name: prompt.name,
        content: {
          prompt: prompt.prompt,
          systemPrompt: prompt.systemPrompt,
          variables: prompt.variables
        },
        metadata: {
          purpose: prompt.purpose,
          tags: [], // PromptVariant doesn't have tags property
          created: new Date().toISOString(),
          version: '1.0'
        },
        testing: {
          supported_models: ['gpt-4', 'claude-3-sonnet'],
          parameters: {
            temperature: { min: 0, max: 2, default: 0.7 },
            max_tokens: { min: 1, max: 4096, default: 1024 }
          }
        }
      })),
      agents: data.agents?.map(agent => ({
        id: agent.id,
        name: agent.name,
        role: agent.role,
        personality: agent.personality,
        expertise: agent.expertise,
        systemPrompt: agent.systemPrompt,
        avatar: agent.avatar
      })),
      results: config.includeResults ? data.results : undefined
    };

    const jsonString = JSON.stringify(promptxData, null, 2);
    return new Blob([jsonString], { type: EXPORT_FORMATS.promptx.mimeType });
  }

  /**
   * Export to standard JSON format
   */
  private exportToJSON(data: ExportData, config: ExportConfiguration): Blob {
    const jsonData = {
      ...data,
      exportConfiguration: config
    };

    const jsonString = JSON.stringify(jsonData, null, 2);
    return new Blob([jsonString], { type: EXPORT_FORMATS.json.mimeType });
  }

  /**
   * Export to CSV format
   */
  private exportToCSV(data: ExportData, config: ExportConfiguration): Blob {
    let csvContent = '';

    if (data.prompts && data.prompts.length > 0) {
      csvContent += 'Type,Name,Content,Purpose,Variables\n';
      
      data.prompts.forEach(prompt => {
        const row = [
          'Prompt',
          this.escapeCsvField(prompt.name),
          this.escapeCsvField(prompt.prompt),
          this.escapeCsvField(prompt.purpose || ''),
          this.escapeCsvField(JSON.stringify(prompt.variables))
        ].join(',');
        csvContent += row + '\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      if (csvContent) csvContent += '\n';
      csvContent += 'Type,Name,Role,Personality,Expertise,System Prompt\n';
      
      data.agents.forEach(agent => {
        const row = [
          'Agent',
          this.escapeCsvField(agent.name),
          this.escapeCsvField(agent.role),
          this.escapeCsvField(agent.personality || ''),
          this.escapeCsvField(agent.expertise?.join(';') || ''),
          this.escapeCsvField(agent.systemPrompt)
        ].join(',');
        csvContent += row + '\n';
      });
    }

    return new Blob([csvContent], { type: EXPORT_FORMATS.csv.mimeType });
  }

  /**
   * Export to Markdown format
   */
  private exportToMarkdown(data: ExportData, config: ExportConfiguration): Blob {
    let markdown = '# Prompt Studio Export\n\n';
    
    if (data.metadata) {
      markdown += `**Exported:** ${data.metadata.exportedAt.toLocaleString()}\n`;
      markdown += `**Version:** ${data.metadata.version}\n\n`;
    }

    if (data.prompts && data.prompts.length > 0) {
      markdown += '## Prompts\n\n';
      
      data.prompts.forEach((prompt, index) => {
        markdown += `### ${index + 1}. ${prompt.name}\n\n`;
        
        if (prompt.purpose) {
          markdown += `**Purpose:** ${prompt.purpose}\n\n`;
        }

        if (prompt.systemPrompt) {
          markdown += '**System Prompt:**\n```\n';
          markdown += prompt.systemPrompt;
          markdown += '\n```\n\n';
        }

        markdown += '**Prompt:**\n```\n';
        markdown += prompt.prompt;
        markdown += '\n```\n\n';

        if (prompt.variables && Object.keys(prompt.variables).length > 0) {
          markdown += '**Variables:**\n';
          Object.entries(prompt.variables).forEach(([key, value]) => {
            markdown += `- **${key}:** ${value}\n`;
          });
          markdown += '\n';
        }

        markdown += '---\n\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      markdown += '## Agents\n\n';
      
      data.agents.forEach((agent, index) => {
        markdown += `### ${index + 1}. ${agent.name}\n\n`;
        markdown += `**Role:** ${agent.role}\n\n`;
        
        if (agent.personality) {
          markdown += `**Personality:** ${agent.personality}\n\n`;
        }
        
        if (agent.expertise && agent.expertise.length > 0) {
          markdown += `**Expertise:** ${agent.expertise.join(', ')}\n\n`;
        }

        markdown += '**System Prompt:**\n```\n';
        markdown += agent.systemPrompt;
        markdown += '\n```\n\n';

        markdown += '---\n\n';
      });
    }

    return new Blob([markdown], { type: EXPORT_FORMATS.markdown.mimeType });
  }

  /**
   * Export to YAML format
   */
  private exportToYAML(data: ExportData, config: ExportConfiguration): Blob {
    // Simple YAML serialization (for complex cases, would use a proper YAML library)
    let yaml = '# Prompt Studio Export\n\n';
    
    if (data.metadata) {
      yaml += 'metadata:\n';
      yaml += `  exportedAt: "${data.metadata.exportedAt.toISOString()}"\n`;
      yaml += `  version: "${data.metadata.version}"\n`;
      yaml += `  source: "${data.metadata.source}"\n\n`;
    }

    if (data.prompts && data.prompts.length > 0) {
      yaml += 'prompts:\n';
      data.prompts.forEach(prompt => {
        yaml += `  - id: "${prompt.id}"\n`;
        yaml += `    name: "${prompt.name}"\n`;
        yaml += `    prompt: |\n`;
        yaml += this.indentText(prompt.prompt, 6);
        
        if (prompt.systemPrompt) {
          yaml += `    systemPrompt: |\n`;
          yaml += this.indentText(prompt.systemPrompt, 6);
        }
        
        if (prompt.purpose) {
          yaml += `    purpose: "${prompt.purpose}"\n`;
        }
        
        if (prompt.variables && Object.keys(prompt.variables).length > 0) {
          yaml += `    variables:\n`;
          Object.entries(prompt.variables).forEach(([key, value]) => {
            yaml += `      ${key}: "${value}"\n`;
          });
        }
        
        yaml += '\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      yaml += 'agents:\n';
      data.agents.forEach(agent => {
        yaml += `  - id: "${agent.id}"\n`;
        yaml += `    name: "${agent.name}"\n`;
        yaml += `    role: "${agent.role}"\n`;
        
        if (agent.personality) {
          yaml += `    personality: "${agent.personality}"\n`;
        }
        
        if (agent.expertise && agent.expertise.length > 0) {
          yaml += `    expertise:\n`;
          agent.expertise.forEach(skill => {
            yaml += `      - "${skill}"\n`;
          });
        }
        
        yaml += `    systemPrompt: |\n`;
        yaml += this.indentText(agent.systemPrompt, 6);
        yaml += '\n';
      });
    }

    return new Blob([yaml], { type: EXPORT_FORMATS.yaml.mimeType });
  }

  /**
   * Filter data based on export configuration
   */
  private filterData(data: ExportData, config: ExportConfiguration): ExportData {
    const filtered: ExportData = {};

    // Filter prompts
    if (data.prompts) {
      filtered.prompts = data.prompts.filter(prompt => {
        // Since PromptVariant doesn't have tags, we'll just include all prompts for now
        return true;
      });
    }

    // Filter agents
    if (data.agents && config.includeAgents) {
      filtered.agents = data.agents;
    }

    // Filter results
    if (data.results && config.includeResults) {
      filtered.results = data.results.filter(result => {
        if (config.dateRange) {
          const resultDate = new Date(result.timestamp);
          if (resultDate < config.dateRange.start || resultDate > config.dateRange.end) {
            return false;
          }
        }

        if (config.filters?.types && config.filters.types.length > 0) {
          if (!config.filters.types.includes(result.type)) {
            return false;
          }
        }

        if (config.filters?.minScore && result.scores) {
          const scores = Object.values(result.scores).filter((score): score is number => typeof score === 'number');
          if (scores.length > 0) {
            const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
            if (avgScore < config.filters.minScore) {
              return false;
            }
          }
        }

        return true;
      });
    }

    // Include metadata if requested
    if (config.includeMetadata && data.metadata) {
      filtered.metadata = data.metadata;
    }

    return filtered;
  }

  /**
   * Download exported data as file
   */
  downloadExport(blob: Blob, filename: string, format: ExportFormat): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.endsWith(format.extension) ? filename : `${filename}${format.extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Generate filename based on export configuration
   */
  generateFilename(config: ExportConfiguration, data: ExportData): string {
    const timestamp = new Date().toISOString().split('T')[0];
    const format = EXPORT_FORMATS[config.format];
    
    let name = 'prompt-studio-export';
    
    if (data.prompts && data.prompts.length > 0) {
      name += `-${data.prompts.length}prompts`;
    }
    
    if (data.agents && data.agents.length > 0) {
      name += `-${data.agents.length}agents`;
    }
    
    if (data.results && data.results.length > 0) {
      name += `-${data.results.length}results`;
    }
    
    return `${name}-${timestamp}${format.extension}`;
  }

  /**
   * Utility methods
   */
  private escapeCsvField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  private indentText(text: string, spaces: number): string {
    const indent = ' '.repeat(spaces);
    return text.split('\n').map(line => indent + line).join('\n') + '\n';
  }

  /**
   * Validate export configuration
   */
  validateConfiguration(config: ExportConfiguration): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!EXPORT_FORMATS[config.format]) {
      errors.push(`Unsupported export format: ${config.format}`);
    }

    if (config.dateRange) {
      if (config.dateRange.start > config.dateRange.end) {
        errors.push('Start date must be before end date');
      }
    }

    if (config.filters?.minScore !== undefined) {
      if (config.filters.minScore < 0 || config.filters.minScore > 100) {
        errors.push('Minimum score must be between 0 and 100');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get available export formats
   */
  getAvailableFormats(): ExportFormat[] {
    return Object.values(EXPORT_FORMATS);
  }

  /**
   * Integration with external services (Catalyst, Notion)
   */
  async exportToCatalyst(data: ExportData, config: any): Promise<{ success: boolean; message: string; dataPreview?: string }> {
    // Placeholder: Prepare data as a .promptx archive, then mock the API call.
    // 📄19 indicates .promptx is the pack format for Catalyst.
    try {
      const standaloneExportData: StandaloneExportData = {
        prompts: data.prompts || [],
        agents: data.agents || [],
        results: data.results || [],
        metadata: {
          appName: PROMPT_STUDIO_APP_NAME,
          appVersion: PROMPT_STUDIO_APP_VERSION,
          exportDate: new Date().toISOString(),
          // Assuming pack-specific metadata would be collected by the UI calling this
          // For now, using defaults or what's available in ExportData's metadata
          packTitle: data.metadata?.configuration?.format === 'promptx' ? "Prompt Pack for Catalyst" : `Export for Catalyst (${new Date().toLocaleDateString()})`,
          packVersion: data.metadata?.version || "1.0.0",
          packDescription: "Data prepared for Catalyst import from Prompt Studio.",
          // Add other metadata as needed if available from data.metadata.configuration or a new 'config' param
        }
      };

      const promptXBlob = await exportToPromptXArchive(standaloneExportData);

      // Mock API call here
      console.log(`[exportToCatalyst] Generated .promptx blob of size ${promptXBlob.size} bytes for Catalyst.`);
      console.log("[exportToCatalyst] Mocking API call to Catalyst endpoint...");
      // In a real scenario:
      // const formData = new FormData();
      // formData.append('file', promptXBlob, 'catalyst_package.promptx');
      // formData.append('metadata', JSON.stringify(config)); // additional config for Catalyst
      // const response = await fetch('CATALYST_API_ENDPOINT/upload', { method: 'POST', body: formData });
      // if (!response.ok) throw new Error('Failed to upload to Catalyst');
      // const result = await response.json();

      // Simulate a delay and success
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        message: 'Data prepared as .promptx and (mock) sent to Catalyst successfully.',
        dataPreview: `Generated .promptx archive (${(promptXBlob.size / 1024).toFixed(2)} KB)`
      };

    } catch (error) {
      console.error("Failed to prepare data for Catalyst:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error preparing data for Catalyst."
      };
    }
  }

  async exportToNotion(data: ExportData, config: any): Promise<{ success: boolean; message: string }> {
    // This would integrate with Notion API
    // For now, return a mock response
    return {
      success: true,
      message: 'Successfully exported to Notion page'
    };
  }
}

export const exportToolsService = new ExportToolsService();

// New standalone functions for .promptx (ZIP) and Catalyst Sync
// These are added alongside the existing ExportToolsService to avoid major refactoring of it immediately.

import JSZip from 'jszip';

import type { ChainFlow } from './chain-linker'; // Import ChainFlow

export interface StandaloneExportData {
  prompts: PromptVariant[];
  agents?: Agent[];
  results?: TestResult[];
  flows?: ChainFlow[]; // Added flows
  metadata?: {
    appName?: string;
    appVersion?: string;
    exportDate: string;
    [key: string]: any;
  };
}

export const PROMPT_STUDIO_APP_NAME = 'PromptStudio';
export const PROMPT_STUDIO_APP_VERSION = '1.0.0'; // Manage versioning appropriately

/**
 * Exports data to a .promptx ZIP archive.
 * @param data The data to export.
 * @returns A Promise that resolves with the Blob of the ZIP file.
 */
export async function exportToPromptXArchive(data: StandaloneExportData): Promise<Blob> {
  const zip = new JSZip();

  const manifest = {
    // Standard .promptx manifest fields
    formatVersion: "1.0.0", // Specify a version for the .promptx format itself
    appName: data.metadata?.appName || PROMPT_STUDIO_APP_NAME,
    appVersion: data.metadata?.appVersion || PROMPT_STUDIO_APP_VERSION,
    exportDate: data.metadata?.exportDate || new Date().toISOString(),

    // Pack-specific metadata (aligning with 📄7 and 📄19 spirit)
    packMetadata: {
      title: data.metadata?.packTitle || "Untitled Prompt Pack",
      packVersion: data.metadata?.packVersion || "1.0.0",
      description: data.metadata?.packDescription || "A collection of prompts, agents, and results from Prompt Studio.",
      authors: data.metadata?.authors || [],
      license: data.metadata?.license || "UNLICENSED",
      tags: data.metadata?.packTags || [],
      tone: data.metadata?.packTone, // Added
      outputType: data.metadata?.packOutputType, // Added
      previewSnippet: data.metadata?.packPreviewSnippet, // Added
      preferredModels: data.metadata?.packPreferredModels, // Added
      ethicsPledgeAdherence: data.metadata?.ethicsPledgeAdherence, // Added
      // intendedAudiences and usageDisclaimers can be added if collected by UI
    },

    itemCounts: {
      prompts: data.prompts.length,
      agents: data.agents?.length || 0,
      results: data.results?.length || 0,
      flows: data.flows?.length || 0, // Added flow count
    },
    items: { // Listing IDs of included items
      prompts: data.prompts.map(p => p.id),
      agents: data.agents?.map(a => a.id) || [],
      results: data.results?.map(r => r.id) || [],
      flows: data.flows?.map(f => f.id) || [], // Added flow IDs
    },

    // Store other custom metadata if provided
    customMetadata: data.metadata?.customPackData || {}
  };
  zip.file('manifest.json', JSON.stringify(manifest, null, 2));

  const promptsFolder = zip.folder('prompts');
  if (promptsFolder) {
    data.prompts.forEach(prompt => {
      // Ensure createdAt and updatedAt are stringified if they are Date objects
      const promptData = {
        ...prompt,
        createdAt: prompt.createdAt instanceof Date ? prompt.createdAt.toISOString() : prompt.createdAt,
        updatedAt: prompt.updatedAt instanceof Date ? prompt.updatedAt.toISOString() : prompt.updatedAt,
      };
      promptsFolder.file(`${prompt.id}.json`, JSON.stringify(promptData, null, 2));
    });
  }

  if (data.agents && data.agents.length > 0) {
    const agentsFolder = zip.folder('agents');
    if (agentsFolder) {
      data.agents.forEach(agent => {
        // Ensure agent data is serializable (e.g. dates)
         const agentData = {
          ...agent,
          // createdAt: agent.createdAt instanceof Date ? agent.createdAt.toISOString() : agent.createdAt, // If Agent has timestamps
          // updatedAt: agent.updatedAt instanceof Date ? agent.updatedAt.toISOString() : agent.updatedAt,
        };
        agentsFolder.file(`${agent.id}.json`, JSON.stringify(agentData, null, 2));
      });
    }
  }

  if (data.results && data.results.length > 0) {
    const resultsFolder = zip.folder('results');
    if (resultsFolder) {
      data.results.forEach(result => {
        // Ensure result data is serializable (e.g. dates)
        const resultData = {
            ...result,
            timestamp: result.timestamp instanceof Date ? result.timestamp.toISOString() : result.timestamp,
        }
        resultsFolder.file(`${result.id}.json`, JSON.stringify(resultData, null, 2));
      });
    }
  }

  if (data.flows && data.flows.length > 0) {
    const flowsFolder = zip.folder('flows');
    if (flowsFolder) {
      data.flows.forEach(flow => {
        // TODO: Implement hybrid node saving (linking vs embedding) for Prompt/Agent nodes within flows.
        // For now, serializing the flow as-is.
        // Ensure dates in flow.metadata are stringified
        const flowDataToSave = {
          ...flow,
          metadata: {
            ...flow.metadata,
            createdAt: flow.metadata.createdAt instanceof Date ? flow.metadata.createdAt.toISOString() : flow.metadata.createdAt,
            updatedAt: flow.metadata.updatedAt instanceof Date ? flow.metadata.updatedAt.toISOString() : flow.metadata.updatedAt,
          }
        };
        flowsFolder.file(`${flow.id}.json`, JSON.stringify(flowDataToSave, null, 2));
      });
    }
  }

  const blob = await zip.generateAsync({ type: 'blob' });
  return blob;
}

/**
 * Utility function to trigger browser download of a Blob.
 */
export function downloadBlobUtil(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Placeholder for Catalyst Sync functionality.
 */
export async function syncWithCatalystPlaceholder(data: StandaloneExportData): Promise<boolean> {
  console.warn('Catalyst sync initiated with data (placeholder):', data);
  alert('Catalyst sync is not implemented yet. This feature requires integration with the Catalyst API.');
  // throw new Error('Catalyst sync not implemented yet.');
  return false; // Simulate failure or no-op
}

/**
 * Exports all application data to a single JSON file (alternative to class method).
 */
export function exportToJsonDump(data: StandaloneExportData): Blob {
  const fullExportData = {
    appName: data.metadata?.appName || PROMPT_STUDIO_APP_NAME,
    appVersion: data.metadata?.appVersion || PROMPT_STUDIO_APP_VERSION,
    exportDate: data.metadata?.exportDate || new Date().toISOString(),
    prompts: data.prompts,
    agents: data.agents || [],
    results: data.results || [],
    customMetadata: data.metadata || {},
  };
  const jsonString = JSON.stringify(fullExportData, null, 2);
  return new Blob([jsonString], { type: 'application/json' });
}

// Placeholder functions for other Catalyst-compatible export options (I3.4.2)

export async function exportPromptToCatalystJsonConfig(prompt: PromptVariant): Promise<Blob> {
  // Basic config: include essential prompt details. Structure TBD by Catalyst specs.
  const config = {
    id: prompt.id,
    name: prompt.name,
    type: 'prompt_module',
    promptText: prompt.prompt,
    systemPrompt: prompt.systemPrompt,
    variables: prompt.variables,
    metadata: {
      promptType: prompt.promptType,
      definedOutputFormat: prompt.definedOutputFormat,
      tags: prompt.tags,
      targetModels: prompt.targetModels,
      desiredTone: prompt.desiredTone,
    }
  };
  const jsonString = JSON.stringify(config, null, 2);
  console.log('[exportPromptToCatalystJsonConfig] Generated (placeholder):', jsonString);
  return new Blob([jsonString], { type: 'application/json' });
}

export async function exportFlowToCatalystJsonConfig(flow: ChainFlow): Promise<Blob> {
  // Basic config: serialize the flow. Detailed Catalyst flow schema TBD.
  const config = {
    id: flow.id,
    name: flow.name,
    type: 'flow_module',
    nodes: flow.nodes.map(n => ({ id: n.id, type: n.type, title: n.data.title })), // Simplified node list
    connections: flow.connections.map(c => ({ from: c.sourceNodeId, to: c.targetNodeId })),
    // Full node data and connection details would be part of a more complex spec
  };
  const jsonString = JSON.stringify(config, null, 2);
  console.log('[exportFlowToCatalystJsonConfig] Generated (placeholder):', jsonString);
  return new Blob([jsonString], { type: 'application/json' });
}

export async function exportPromptToLLMPlayground(prompt: PromptVariant, provider: string): Promise<Blob> {
  // Example for OpenAI. Anthropic etc. would have different structures.
  let payload: any = {};
  if (provider === 'openai') {
    payload = {
      model: prompt.targetModels?.[0] || 'gpt-4o', // Default or first target model
      messages: [
        ...(prompt.systemPrompt ? [{ role: 'system', content: prompt.systemPrompt }] : []),
        { role: 'user', content: prompt.prompt }
      ],
      // Add temperature, max_tokens etc. from prompt.config if available
    };
  } else {
    payload = { note: `Playground format for ${provider} TBD`, prompt };
  }
  const jsonString = JSON.stringify(payload, null, 2);
  console.log(`[exportPromptToLLMPlayground] Generated for ${provider} (placeholder):`, jsonString);
  return new Blob([jsonString], { type: 'application/json' });
}

export async function exportPromptToTxtScaffold(prompt: PromptVariant): Promise<Blob> {
  let scaffold = `PROMPT NAME: ${prompt.name}\n`;
  scaffold += `TYPE: ${prompt.promptType || 'N/A'}\n`;
  scaffold += `OUTPUT FORMAT: ${prompt.definedOutputFormat || 'N/A'}\n\n`;
  if (prompt.systemPrompt) {
    scaffold += `--- SYSTEM PROMPT ---\n${prompt.systemPrompt}\n\n`;
  }
  scaffold += `--- USER PROMPT ---\n${prompt.prompt}\n\n`;
  if (prompt.variables && Object.keys(prompt.variables).length > 0) {
    scaffold += `--- VARIABLES ---\n${JSON.stringify(prompt.variables, null, 2)}\n`;
  }
  console.log('[exportPromptToTxtScaffold] Generated (placeholder)');
  return new Blob([scaffold], { type: 'text/plain' });
}

export async function exportPromptToGptFunction(prompt: PromptVariant): Promise<Blob> {
  // Structure for a GPT function calling definition.
  const functionDef = {
    name: prompt.name.replace(/\s+/g, '_').toLowerCase() || 'my_prompt_function',
    description: prompt.purpose || `Executes prompt: ${prompt.name}`,
    parameters: {
      type: 'object',
      properties: Object.keys(prompt.variables || {}).reduce((acc, key) => {
        acc[key] = { type: 'string', description: `Value for ${key}` };
        return acc;
      }, {} as Record<string, any>),
      required: Object.keys(prompt.variables || {}),
    },
    // The actual prompt execution logic would be handled by the calling code,
    // this just defines the function interface.
  };
  const jsonString = JSON.stringify(functionDef, null, 2);
  console.log('[exportPromptToGptFunction] Generated (placeholder):', jsonString);
  return new Blob([jsonString], { type: 'application/json' });
}

// For I3.4.3: prompt-engine.json intermediary format

export interface PromptEngineParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'json_object' | 'text_array';
  description?: string;
  required?: boolean;
  defaultValue?: any;
}
export interface PromptEngineConfig {
  engineVersion: string; // Version of this engine config schema
  id: string; // Unique ID for this engine config
  name: string; // User-friendly name
  description?: string;
  sourceType: 'prompt_variant' | 'chain_flow'; // What it was derived from
  sourceId?: string; // ID of the original PromptVariant or ChainFlow

  systemRole?: string; // Overall system role for the engine
  coreLogic: { // Details of the core prompt or flow
    promptText?: string; // If from single prompt
    systemPrompt?: string; // If from single prompt
    flowId?: string; // If from ChainFlow, this would be the ID of the flow in .promptx
    // Or embed a simplified flow structure directly if not linking
  };

  inputSchema?: PromptEngineParameter[]; // Defines expected inputs
  outputType: 'json' | 'markdown' | 'text' | 'list' | string; // Declared output type
  outputSchema?: any; // Optional: JSON schema for object outputs

  tags?: string[];
  metadata?: Record<string, any>; // Other custom metadata
}

export async function exportToPromptEngineConfig(
  data: PromptVariant | ChainFlow,
  engineId?: string, // Optional ID for the engine config itself
  engineName?: string
): Promise<Blob> {
  let config: PromptEngineConfig;

  if ('prompt' in data) { // It's a PromptVariant
    const prompt = data as PromptVariant;
    config = {
      engineVersion: '1.0.0',
      id: engineId || `engine_from_prompt_${prompt.id}`,
      name: engineName || `Engine: ${prompt.name}`,
      description: prompt.purpose || prompt.usageNotes,
      sourceType: 'prompt_variant',
      sourceId: prompt.id,
      systemRole: prompt.systemPrompt, // System prompt can be the role
      coreLogic: {
        promptText: prompt.prompt,
        systemPrompt: prompt.systemPrompt, // Redundant but can be useful
      },
      inputSchema: Object.entries(prompt.variables || {}).map(([key, exampleValue]) => ({
        name: key,
        type: 'string', // Default to string, could be inferred or specified in PromptVariant later
        description: `Variable for ${key}`,
        defaultValue: exampleValue,
      })),
      outputType: prompt.definedOutputFormat || 'text',
      tags: prompt.tags,
      metadata: {
        promptType: prompt.promptType,
        targetModels: prompt.targetModels,
        desiredTone: prompt.desiredTone,
      }
    };
  } else { // It's a ChainFlow
    const flow = data as ChainFlow;
    // Deriving inputs/outputs for a flow is more complex.
    // For now, use placeholder or expect user to define them if this flow becomes an engine.
    // A flow's "system role" could be its description or a dedicated metadata field.
    config = {
      engineVersion: '1.0.0',
      id: engineId || `engine_from_flow_${flow.id}`,
      name: engineName || `Engine: ${flow.name}`,
      description: flow.description,
      sourceType: 'chain_flow',
      sourceId: flow.id,
      coreLogic: {
        flowId: flow.id, // Assumes flow is also packaged or linkable
      },
      // Input/Output schema for flows would need to be derived from its Input/Output nodes
      inputSchema: [], // Placeholder
      outputType: 'complex_object', // Placeholder
      tags: flow.metadata.tags,
      metadata: { flowNodeCount: flow.nodes.length }
    };
  }

  const jsonString = JSON.stringify(config, null, 2);
  console.log('[exportToPromptEngineConfig] Generated (placeholder):', jsonString);
  return new Blob([jsonString], { type: 'application/json' });
}

// For P4.4: Deployment Layer Prototypes

export async function exportFlowToExpressApiScaffold(flow: ChainFlow, options?: any): Promise<Blob> {
  const zip = new JSZip();
  const flowJson = JSON.stringify(flow, null, 2);
  zip.file('flow.json', flowJson);

  const packageJson = `{
  "name": "prompt-flow-api-${flow.id.substring(0,6)}",
  "version": "1.0.0",
  "main": "server.js",
  "scripts": { "start": "node server.js" },
  "dependencies": { "express": "^4.17.1" /* Add other minimal deps */ }
}`;
  zip.file('package.json', packageJson);

  const serverJs = `
const express = require('express');
const fs = require('fs');
const app = express();
app.use(express.json());
const PORT = process.env.PORT || 3000;

let chainFlow;
try {
  chainFlow = JSON.parse(fs.readFileSync('./flow.json', 'utf-8'));
} catch (e) {
  console.error("Failed to load flow.json:", e);
  process.exit(1);
}

// TODO: Implement or import a simplified chainLinkerService.executeFlow for Node.js
async function executeFlowLogic(flow, inputs) {
  console.log("Executing flow (mock):", flow.name, "with inputs:", inputs);
  // Replace with actual execution logic that calls AI services securely from backend
  // For now, return a mock response based on flow structure
  const outputNode = flow.nodes.find(n => n.type === 'output');
  const mockOutput = { message: "Flow executed successfully (mock response).", receivedInputs: inputs };
  return outputNode ? { [outputNode.id]: mockOutput } : mockOutput;
}

app.post('/execute', async (req, res) => {
  try {
    const inputs = req.body.inputs || {};
    // This assumes 'chainFlow' is loaded and a compatible 'executeFlowLogic' exists
    const result = await executeFlowLogic(chainFlow, inputs);
    res.json({ success: true, data: result });
  } catch (error) {
    console.error("Error executing flow:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/', (req, res) => res.send('Flow API for: ' + chainFlow.name + ' is running! POST to /execute with { "inputs": { ... } }'));

app.listen(PORT, () => console.log(\`Server for flow \${chainFlow.name} listening on port \${PORT}\`));
`;
  zip.file('server.js', serverJs);
  const expressReadme = `# ${flow.name} - Express API Scaffold

This scaffold provides a basic Express.js server to execute a Prompt Studio ChainLinker flow.

## Setup
1.  Ensure you have Node.js and npm installed.
2.  Place the exported \`flow.json\` in the root of this scaffold.
3.  Run \`npm install\` to install dependencies.
4.  **Configure AI Services**:
    *   The \`server.js\` file contains a placeholder \`executeFlowLogic\` function. You will need to implement the logic to execute ChainLinker nodes, especially Prompt and Agent nodes.
    *   This involves creating or adapting an \`aiService\` compatible with Node.js for making calls to LLM providers.
    *   **Crucially, API keys for LLMs must be stored securely as environment variables on your server (e.g., in a \`.env\` file loaded by a library like \`dotenv\`) and NOT hardcoded.**
5.  Run the server: \`npm start\`.

## Usage
- The server will start (typically on port 3000).
- Send a POST request to \`/execute\` with a JSON body like:
  \`\`\`json
  {
    "inputs": {
      "your_input_node_id_or_label": "value_for_input_node"
      // Add other input values as needed by your flow's Input nodes
    }
  }
  \`\`\`
- The API will (mock) execute the flow and return a JSON response.
`;
  zip.file('README.md', expressReadme);

  console.log('[exportFlowToExpressApiScaffold] Generated ZIP (placeholder logic)');
  return zip.generateAsync({ type: 'blob' });
}

export async function exportFlowToDiscordBotScaffold(flow: ChainFlow, options?: any): Promise<Blob> {
  const zip = new JSZip();
  zip.file('flow.json', JSON.stringify(flow, null, 2));
  // Basic package.json, bot.js, config.json example
  // Actual implementation would be more involved
  zip.file('package.json', `{\n  "name": "discord-flow-bot-${flow.id.substring(0,6)}",\n  "version": "1.0.0",\n  "main": "bot.js",\n  "scripts": { "start": "node bot.js" },\n  "dependencies": { "discord.js": "^14.7.1" }\n}`);
  zip.file('config.json', `{\n  "DISCORD_BOT_TOKEN": "YOUR_BOT_TOKEN_HERE",\n  "COMMAND_PREFIX": "!"\n}`);
  const botJsContent = `// Basic Discord bot scaffold
const { Client, GatewayIntentBits } = require('discord.js');
const fs = require('fs');
const config = require('./config.json');
const client = new Client({ intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages, GatewayIntentBits.MessageContent] });

let chainFlow;
try {
  chainFlow = JSON.parse(fs.readFileSync('./flow.json', 'utf-8'));
} catch (e) {
  console.error("Failed to load flow.json:", e);
  process.exit(1);
}

// TODO: Implement or import a simplified chainLinkerService.executeFlow for Node.js
async function executeFlowLogic(flow, inputs) {
  console.log("Executing flow (mock):", flow.name, "with inputs:", inputs);
  // Replace with actual execution logic (similar to Express example)
  const outputNode = flow.nodes.find(n => n.type === 'output');
  const mockOutput = { message: "Flow executed (mock Discord response).", receivedInputs: inputs };
  return outputNode ? mockOutput.message : "Mocked flow output."; // Simplify for direct bot reply
}

client.on('ready', () => console.log(\`Logged in as \${client.user.tag}!\`));

client.on('messageCreate', async msg => {
  if (msg.author.bot || !msg.content.startsWith(config.COMMAND_PREFIX)) return;
  const args = msg.content.slice(config.COMMAND_PREFIX.length).trim().split(/ +/);
  const command = args.shift().toLowerCase();

  if (command === 'runflow') { // Example command
    try {
      // Map 'args' or msg.content to flow inputs. This is highly flow-dependent.
      // Example: use the entire message content after command as input to first input node
      const firstInputNodeId = chainFlow.nodes.find(n => n.type === 'input')?.id || 'default_input';
      const flowInputs = { [firstInputNodeId]: args.join(' ') };

      const result = await executeFlowLogic(chainFlow, flowInputs);
      msg.reply(String(result));
    } catch (error) {
      console.error("Error running flow for Discord:", error);
      msg.reply("Sorry, an error occurred while running the flow.");
    }
  }
});
client.login(config.DISCORD_BOT_TOKEN);
`;
  zip.file('bot.js', botJsContent);
  const discordReadme = `# ${flow.name} - Discord Bot Scaffold

This scaffold provides a basic Discord bot to execute a Prompt Studio ChainLinker flow.

## Setup
1.  Ensure Node.js and npm are installed.
2.  Place exported \`flow.json\` in the root.
3.  Create a Discord Bot Application and get its token.
4.  Update \`config.json\` with your \`DISCORD_BOT_TOKEN\` and desired \`COMMAND_PREFIX\`.
5.  Run \`npm install\`.
6.  **Implement Flow Execution**:
    *   The \`bot.js\` file contains a placeholder \`executeFlowLogic\` and basic command handling.
    *   Adapt this to map Discord command arguments to your flow's input nodes.
    *   Implement the actual flow execution, including secure AI service calls (see Express API scaffold README for notes on backend \`aiService\` and API keys).
7.  Run the bot: \`npm start\`.

## Example Usage
- Once the bot is running and invited to your server, use the command (e.g., \`!runflow your input text\`) to trigger the flow.
`;
  zip.file('README.md', discordReadme);

  console.log('[exportFlowToDiscordBotScaffold] Generated ZIP (placeholder logic)');
  return zip.generateAsync({ type: 'blob' });
}

export async function exportFlowToWebEmbedSnippet(flow: ChainFlow, options?: any): Promise<Blob> {
  const flowJsonString = JSON.stringify(flow).replace(/`/g, '\\`'); // Escape backticks in JSON string

  // Basic HTML structure with embedded flow and placeholder execution logic
  // Simplified the dynamic script part to avoid complex template literal issues with esbuild
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <title>${flow.name} - Web Embed</title>
  <style>body{font-family:sans-serif} input,button{padding:5px} .output{margin-top:10px;padding:10px;border:1px solid #ccc;background:#f9f9f9}</style>
</head>
<body>
  <h1>${flow.name}</h1>
  <div id="inputs-container"></div>
  <button onclick="runFlow()">Run Flow</button>
  <div id="output-container" class="output"></div>

  <script>
    const flowDataString = \`${flowJsonString}\`; // Embed escaped JSON string
    let chainFlow;
    try {
      chainFlow = JSON.parse(flowDataString);
    } catch(e) {
      console.error("Error parsing flow data:", e);
      document.getElementById('output-container').innerText = "Error: Could not load flow data.";
    }

    const inputsContainer = document.getElementById('inputs-container');
    const outputContainer = document.getElementById('output-container');

    // Dynamically create input fields
    if (chainFlow && chainFlow.nodes) {
      chainFlow.nodes.filter(node => node.type === 'input').forEach((node, index) => {
        const label = document.createElement('label');
        label.htmlFor = 'flow_input_' + node.id;
        label.innerText = node.data.title || ('Input ' + (index + 1));
        const input = document.createElement('input');
        input.id = 'flow_input_' + node.id;
        input.type = node.data.config?.inputType === 'number' ? 'number' : 'text';
        input.placeholder = node.data.description || ('Enter ' + (node.data.title?.toLowerCase() || 'input'));
        inputsContainer.appendChild(label);
        inputsContainer.appendChild(input);
        inputsContainer.appendChild(document.createElement('br'));
      });
    }

    async function executeFlowLogic_mock(flow, inputs) {
      // TODO: Implement actual client-side flow execution logic.
      // This will likely involve a simplified version of ChainLinkerService.
      // For AI Nodes, this will require proxying calls to a backend to protect API keys.
      console.log("Executing flow (mock client-side):", flow.name, "with inputs:", inputs);
      await new Promise(r => setTimeout(r, 300));
      return { mockOutput: "Flow executed (client-side placeholder response).", received: inputs };
    }

    async function runFlow() {
      if (!chainFlow) return;
      outputContainer.innerText = 'Executing...';
      const inputs = {};
      chainFlow.nodes.filter(node => node.type === 'input').forEach(node => {
        const inputElement = document.getElementById('flow_input_' + node.id);
        // Assuming input nodes have one output port named 'value' (common convention)
        if (inputElement && node.outputs && node.outputs[0]) {
          inputs[node.outputs[0].id] = inputElement.value;
        } else if (inputElement) {
           inputs[node.id] = inputElement.value; // Fallback to node.id if output structure is unknown
        }
      });

      try {
        const result = await executeFlowLogic_mock(chainFlow, inputs);
        outputContainer.innerText = JSON.stringify(result, null, 2);
      } catch (error) {
        outputContainer.innerText = 'Error: ' + error.message;
        console.error("Error executing flow:", error);
      }
    }
  <\/script>
</body>
</html>`;
  console.log('[exportFlowToWebEmbedSnippet] Generated HTML (placeholder logic)');
  return new Blob([htmlContent], { type: 'text/html' });
}
