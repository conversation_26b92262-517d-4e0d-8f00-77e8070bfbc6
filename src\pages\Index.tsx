import { useState } from 'react';
import { Sidebar } from '@/components/Sidebar';
import { usePromptStore } from '@/hooks/usePromptStore';
import { Button } from '@/components/ui/button';
import { PromptTester } from '@/components/PromptTester';
import { PromptVariations } from '@/components/PromptVariations';
import { AgentSimulator } from '@/components/AgentSimulator';
import { ProjectSimulator } from '@/components/ProjectSimulator';
import { ChainLinkerCanvas } from '@/components/ChainLinkerCanvas';
import { ResultsDashboard } from '@/components/ResultsDashboard';
import { PromptHistory } from '@/components/PromptHistory';
import { Settings } from '@/components/Settings';

// Moved imports to top level
import { agentSimulationService, AgentSimulationRequest, SimulationResult as SingleAgentSimulationResult } from '@/lib/agent-simulation';
import { multiAgentConversationService, MultiAgentSimulationRequest, MultiAgentSimulationResult } from '@/lib/multiAgentConversationService';
import { promptTestingService, PromptTestRequest, PromptTestResult } from '@/lib/prompt-testing';
import { Agent, PromptVariant, TestResult } from '@/store/promptStore';
import { toast } from '@/hooks/use-toast';

const Index = () => {
  console.log("🏠 Index component mounting...");
  const [activeView, setActiveView] = useState('tester');
  console.log("📍 Active view set to:", activeView);

  // Initialize store with error handling
  let storeData;
  try {
    storeData = usePromptStore();
    console.log('✅ Store initialized successfully');
  } catch (err) {
    console.error('❌ Store initialization failed:', err);
    // Continue with fallback
  }

  const { initialized, addResult, getResults } = storeData || {
    initialized: false,
    addResult: () => {},
    getResults: () => []
  };
  console.log("🔧 Store initialized:", initialized);

  const handleResults = (results: any) => {
    console.log('🎯 Index.handleResults called with:', results);
    if (addResult) {
      console.log('🎯 Index.handleResults: Calling addResult...');
      addResult(results);
      console.log('🎯 Index.handleResults: addResult completed');
    } else {
      console.error('🎯 Index.handleResults: addResult function not available!');
    }
  };

  // Misplaced import block removed from here

  const handleRunVariations = async (variantsToTest: PromptVariant[]) => {
    if (!addResult) {
      console.error("addResult function is not available from promptStore.");
      toast({ title: "Error", description: "Failed to access results storage.", variant: "destructive" });
      return;
    }
    if (!variantsToTest || variantsToTest.length === 0) {
      toast({ title: "No Variations Selected", description: "Please select variations to test." });
      return;
    }

    toast({ title: "Starting Variation Tests...", description: `Testing ${variantsToTest.length} variation(s). This may take a moment.` });

    let successCount = 0;
    let failCount = 0;
    // Define a default set of diverse models. Ensure these are valid specifiers as per promptTestingService.parseModelSpec
    const defaultModelsToTest = [
      'openai:gpt-4o', // Latest GPT-4o model
      'google:gemini-2.5-flash', // Latest stable Gemini model
      'anthropic:claude-3-5-sonnet-latest', // Latest Claude model
    ];

    for (const variant of variantsToTest) {
      try {
        const testRequest: PromptTestRequest = {
          prompt: variant.prompt,
          systemPrompt: variant.systemPrompt,
          models: defaultModelsToTest,
          variables: variant.variables,
          temperature: 0.7, // Default, consider making this configurable in PromptVariant later
          maxTokens: 1024,  // Default, consider making this configurable in PromptVariant later
          enableAutoScoring: true, // Default, consider global setting
          // expectedArchetype: undefined, // Or from variant if available
        };

        const promptTestResult: PromptTestResult = await promptTestingService.runPromptTest(testRequest);

        // promptStore.addResult expects Omit<TestResult, 'id'>
        // promptTestingService.runPromptTest returns PromptTestResult which includes an id.
        const { id, ...resultForStore } = promptTestResult;

        // Ensure the structure matches TestResult for the store
        const storePayload: Omit<TestResult, 'id'> = {
            timestamp: resultForStore.timestamp,
            type: 'prompt_test', // Each variant test is a 'prompt_test'
            data: { // Store raw promptTestResult data, or a subset
                originalPrompt: resultForStore.prompt,
                systemPromptUsed: resultForStore.systemPrompt,
                variablesUsed: resultForStore.variables,
                variantName: variant.name, // Add variant name for context
                modelResults: resultForStore.results, // Array of ModelTestResult
                summary: resultForStore.summary,
                metadata: resultForStore.metadata,
            },
            scores: { // Map overall scores if available, or individual model scores could be summarized
                fidelity: resultForStore.summary.averageScore, // Example: use average score as overall fidelity
                // Add other scores if applicable or leave undefined
            }
        };

        addResult(storePayload);
        successCount++;
      } catch (error: any) {
        console.error(`Failed to test variation "${variant.name || variant.id}":`, error);
        failCount++;
        // Optionally, add a placeholder error result to the store:
        addResult({
          type: 'prompt_test', // Still a prompt test, but one that failed
          data: {
            variantName: variant.name || variant.id,
            error: error.message,
            prompt: variant.prompt,
            systemPrompt: variant.systemPrompt,
            variables: variant.variables,
            modelsAttempted: defaultModelsToTest
          },
          timestamp: new Date(),
          scores: { fidelity: 0, adherence: 0, consistency: 0, creativity: 0, accuracy: 0 }
        });
      }
    }

    toast({
      title: "Variation Testing Complete",
      description: `${successCount} variation(s) tested successfully, ${failCount} failed. Results updated.`
    });
  };

  const handleRunSimulation = async (agents: Agent[], scenario: string) => { // This is for INDIVIDUAL agent simulations
    console.log('Running INDIVIDUAL agent simulation with agents:', agents, 'and scenario:', scenario);
    if (!addResult) {
      console.error("addResult function is not available from promptStore.");
      toast({ title: "Error", description: "Failed to access results storage.", variant: "destructive" });
      return;
    }

    // For individual mode, we run each agent separately against the scenario.
    // The `agentSimulationService.runSimulation` is designed to take multiple agents
    // and have them *each* respond to the scenario independently if that's its internal logic,
    // or we adapt it to call one by one if it's designed for a single agent pass.
    // Looking at agent-simulation.ts, runSimulation iterates through agents and calls simulateAgentResponse for each.
    // So, it already supports running multiple agents individually against the same scenario.

    const request: AgentSimulationRequest = {
      agents, // Pass all selected enabled agents
      scenario,
      temperature: 0.7,
      maxTokens: 1024,
    };

    try {
      const simulationResult: SingleAgentSimulationResult = await agentSimulationService.runSimulation(request);

      // The result from agentSimulationService already contains responses from ALL agents passed.
      // We can store this as one 'agent_simulation' result that encapsulates the individual responses.
      const testResultForStore: Omit<TestResult, 'id'> = {
        timestamp: simulationResult.timestamp,
        type: 'agent_simulation', // This type can represent a batch of individual agent tests
        data: {
          scenario: simulationResult.scenario,
          agentResponses: simulationResult.agentResponses, // This array contains each agent's independent response
          summary: simulationResult.summary, // The service also generates a summary over these individual responses
          metadata: simulationResult.metadata,
          simulatedAgents: agents.map(a => ({
            id: a.id, name: a.name, role: a.role, systemPrompt: a.systemPrompt,
            personality: a.personality, expertise: a.expertise
          }))
        },
        scores: {
          fidelity: simulationResult.metadata.averageConfidence,
          adherence: simulationResult.metadata.averageConfidence,
          consistency: simulationResult.metadata.averageConfidence > 50 ? (simulationResult.metadata.averageConfidence - 50) * 2 : 0,
          creativity: 100 - simulationResult.metadata.averageConfidence,
          accuracy: simulationResult.metadata.averageConfidence,
        }
      };

      addResult(testResultForStore);
      toast({
        title: "Individual Agent Simulation Complete",
        description: `${agents.length} agent(s) responded. Duration: ${simulationResult.metadata.duration / 1000}s. Results added.`
      });

    } catch (error: any) {
      console.error("Individual agent simulation failed:", error);
      toast({
        title: "Agent Simulation Failed",
        description: error.message || "An unknown error occurred.",
        variant: "destructive"
      });
    }
  };

  const handleRunMultiAgentSimulation = async (selectedAgents: Agent[], scenario: string, maxTurns: number) => {
    console.log('Running MULTI-AGENT conversation with agents:', selectedAgents, 'scenario:', scenario, 'maxTurns:', maxTurns);
    if (!addResult) {
      toast({ title: "Error", description: "Failed to access results storage.", variant: "destructive" });
      return;
    }

    const request: MultiAgentSimulationRequest = {
      participatingAgents: selectedAgents,
      initialScenario: scenario,
      maxTurns,
      interactionPolicy: 'round-robin',
    };

    try {
      const result: MultiAgentSimulationResult = await multiAgentConversationService.runSimulation(request);

      const testResultForStore: Omit<TestResult, 'id'> = {
        timestamp: result.timestamp,
        type: 'agent_simulation', // Changed from 'multi_agent_simulation' to match allowed types
        data: {
          scenario: result.scenario,
          conversationHistory: result.conversationHistory,
          summary: result.summary,
          participatingAgents: result.participatingAgents.map(a => ({
            id: a.id, name: a.name, role: a.role, systemPrompt: a.systemPrompt,
            personality: a.personality, expertise: a.expertise
          })),
          metadata: result.metadata,
          isMultiAgent: true, // Flag to distinguish from single agent simulations
        },
        scores: {
          fidelity: result.metadata.totalTurns > 0 ? 80 : 20,
          consistency: result.summary.length > 10 ? 70 : 30,
        }
      };

      addResult(testResultForStore);
      toast({
        title: "Multi-Agent Conversation Complete",
        description: `Simulation ended after ${result.metadata.totalTurns} turns. Duration: ${result.metadata.duration / 1000}s. Results added.`
      });

    } catch (error: any) {
      console.error("Multi-agent conversation failed:", error);
      toast({
        title: "Multi-Agent Conversation Failed",
        description: error.message || "An unknown error occurred.",
        variant: "destructive"
      });
    }
  };

  const renderActiveView = () => {
    console.log('🎨 Rendering active view:', activeView);

    // Fallback diagnostic view first
    const fallbackView = (
      <div className="p-8 bg-slate-800 rounded-lg border border-slate-700">
        <h2 className="text-2xl font-bold text-green-400 mb-4">🔧 Diagnostic Mode</h2>
        <div className="space-y-2 text-slate-300">
          <p>✅ App mounted successfully</p>
          <p>✅ Index component rendered</p>
          <p>📍 Active view: <span className="text-blue-400 font-semibold">{activeView}</span></p>
          <p>🔧 Store initialized: <span className="text-green-400">{initialized ? 'Yes' : 'No'}</span></p>
          <p>🕒 Timestamp: {new Date().toLocaleTimeString()}</p>
        </div>
        <div className="mt-4 p-4 bg-slate-900/50 rounded border border-slate-600">
          <p className="text-slate-400 text-sm">
            This fallback view confirms the basic React rendering pipeline is working.
            If you see this, the issue is likely with component imports or dependencies.
          </p>
        </div>
      </div>
    );

    try {
      switch (activeView) {
        case 'tester':
          console.log('🧪 Loading PromptTester...');
          return <PromptTester onResults={handleResults} />;
        case 'variations':
          console.log('🔀 Loading PromptVariations...');
          return <PromptVariations onRunVariations={handleRunVariations} />;
        case 'agents':
          console.log('🤖 Loading AgentSimulator...');
          return <AgentSimulator onRunSimulation={handleRunSimulation} onRunMultiAgentSimulation={handleRunMultiAgentSimulation} />;
        case 'projects':
          console.log('📁 Loading ProjectSimulator...');
          return <ProjectSimulator />;
        case 'chain-linker':
          console.log('🔗 Loading ChainLinkerCanvas...');
          return <ChainLinkerCanvas />;
        case 'results': {
          console.log('📊 Loading ResultsDashboard...');
          const results = getResults ? getResults() : [];
          console.log('📊 Results from store:', results.length, 'items');
          return <ResultsDashboard results={results} />;
        }
        case 'history':
          console.log('📚 Loading PromptHistory...');
          return <PromptHistory />;
        case 'settings':
          console.log('⚙️ Loading Settings...');
          return <Settings />;
        default:
          console.log('🧪 Loading default PromptTester...');
          return <PromptTester onResults={handleResults} />;
      }
    } catch (error) {
      console.error('❌ Error rendering view:', error);
      return (
        <div className="p-8 text-center">
          <h2 className="text-2xl font-bold text-red-400 mb-4">Component Error</h2>
          <p className="text-slate-300 mb-2">
            Failed to load the {activeView} component.
          </p>
          <p className="text-slate-400 text-sm">
            Error: {error?.toString()}
          </p>
          <div className="mt-4">
            <Button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Reload Page
            </Button>
          </div>
          {/* Fallback diagnostic view */}
          <div className="mt-6 p-4 bg-slate-800 rounded-lg border border-slate-700">
            {fallbackView}
          </div>
        </div>
      );
    }
  };

  console.log('🎯 Index component about to render JSX, activeView:', activeView, 'initialized:', initialized);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      <div className="flex">
        <Sidebar activeView={activeView} onViewChange={setActiveView} />
        <div className="flex-1 p-6">
          <header className="mb-8">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
              💠 Prompt Studio
            </h1>
            <p className="text-slate-400 mt-2">
              Unified workspace for prompt engineering, agent simulation, and integrated testing
            </p>
          </header>

          {renderActiveView()}
        </div>
      </div>
    </div>
  );
};

export default Index;
