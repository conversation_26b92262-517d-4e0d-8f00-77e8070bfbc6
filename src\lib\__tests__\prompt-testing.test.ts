import { describe, it, expect, vi, beforeEach } from 'vitest';
import { promptTestingService } from '../prompt-testing';
import { aiService } from '../ai-service';

// Mock the AI service
vi.mock('../ai-service', () => ({
  aiService: {
    generateResponse: vi.fn()
  }
}));

describe('PromptTestingService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('runPromptTest', () => {
    it('should run prompt test with multiple models', async () => {
      const mockResponse = {
        content: 'Test response',
        model: 'gpt-4',
        provider: 'openai',
        timestamp: new Date(),
        usage: {
          promptTokens: 10,
          completionTokens: 20,
          totalTokens: 30
        }
      };

      (aiService.generateResponse as any).mockResolvedValue(mockResponse);

      const result = await promptTestingService.runPromptTest({
        prompt: 'Test prompt',
        models: ['openai:gpt-4', 'anthropic:claude-3-sonnet'],
        temperature: 0.7,
        maxTokens: 1024
      });

      expect(result.results).toHaveLength(2);
      expect(result.results[0].model).toBe('gpt-4');
      expect(result.results[0].response).toBe('Test response');
      expect(result.summary.totalTokens).toBeGreaterThan(0);
    });

    it('should handle model failures gracefully', async () => {
      (aiService.generateResponse as any)
        .mockResolvedValueOnce({
          content: 'Success response',
          model: 'gpt-4',
          provider: 'openai',
          timestamp: new Date(),
          usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 }
        })
        .mockRejectedValueOnce(new Error('API Error'));

      const result = await promptTestingService.runPromptTest({
        prompt: 'Test prompt',
        models: ['openai:gpt-4', 'anthropic:claude-3-sonnet']
      });

      expect(result.results).toHaveLength(2);
      expect(result.results[0].error).toBeUndefined();
      expect(result.results[1].error).toBe('API Error');
      expect(result.metadata.successfulTests).toBe(1);
      expect(result.metadata.failedTests).toBe(1);
    });

    it('should process variables in prompts', async () => {
      const mockResponse = {
        content: 'Hello John!',
        model: 'gpt-4',
        provider: 'openai',
        timestamp: new Date(),
        usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 }
      };

      (aiService.generateResponse as any).mockResolvedValue(mockResponse);

      const result = await promptTestingService.runPromptTest({
        prompt: 'Say hello to {{name}}',
        models: ['openai:gpt-4'],
        variables: { name: 'John' }
      });

      expect(result.prompt).toBe('Say hello to John');
    });

    it('should calculate scores correctly', async () => {
      const mockResponse = {
        content: 'This is a well-structured response that addresses the prompt directly.',
        model: 'gpt-4',
        provider: 'openai',
        timestamp: new Date(),
        usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 }
      };

      (aiService.generateResponse as any).mockResolvedValue(mockResponse);

      const result = await promptTestingService.runPromptTest({
        prompt: 'Write a response',
        models: ['openai:gpt-4']
      });

      const scores = result.results[0].score;
      expect(scores.fidelity).toBeGreaterThan(0);
      expect(scores.fidelity).toBeLessThanOrEqual(100);
      expect(scores.adherence).toBeGreaterThan(0);
      expect(scores.consistency).toBeGreaterThan(0);
      expect(scores.creativity).toBeGreaterThan(0);
    });
  });

  describe('parseModelSpec', () => {
    it('should parse provider:model format', () => {
      const service = promptTestingService as any;
      const result = service.parseModelSpec('openai:gpt-4o');
      expect(result.provider).toBe('openai');
      expect(result.model).toBe('gpt-4o');
    });

    it('should handle model-only format', () => {
      const service = promptTestingService as any;
      const result = service.parseModelSpec('gpt-4o');
      expect(result.provider).toBe('openai');
      expect(result.model).toBe('gpt-4o');
    });

    it('should map common model names', () => {
      const service = promptTestingService as any;
      const result = service.parseModelSpec('claude-3-sonnet');
      expect(result.provider).toBe('anthropic');
      expect(result.model).toBe('claude-3-5-sonnet-latest');
    });
  });

  describe('processVariables', () => {
    it('should replace {{variable}} patterns', () => {
      const service = promptTestingService as any;
      const result = service.processVariables(
        'Hello {{name}}, welcome to {{place}}!',
        { name: 'John', place: 'Paris' }
      );
      expect(result).toBe('Hello John, welcome to Paris!');
    });

    it('should replace {variable} patterns', () => {
      const service = promptTestingService as any;
      const result = service.processVariables(
        'Hello {name}!',
        { name: 'Jane' }
      );
      expect(result).toBe('Hello Jane!');
    });

    it('should handle missing variables', () => {
      const service = promptTestingService as any;
      const result = service.processVariables(
        'Hello {{name}}!',
        { other: 'value' }
      );
      expect(result).toBe('Hello {{name}}!');
    });

    it('should return original text when no variables provided', () => {
      const service = promptTestingService as any;
      const result = service.processVariables('Hello world!');
      expect(result).toBe('Hello world!');
    });
  });

  describe('calculateScores', () => {
    it('should calculate fidelity score', () => {
      const service = promptTestingService as any;
      const score = service.calculateFidelity(
        'This is a comprehensive response that directly addresses the question.',
        'Please provide a detailed answer.'
      );
      expect(score).toBeGreaterThan(50);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should penalize very short responses', () => {
      const service = promptTestingService as any;
      const shortScore = service.calculateFidelity('Yes.', 'Explain the concept.');
      const longScore = service.calculateFidelity(
        'This is a detailed explanation that covers multiple aspects.',
        'Explain the concept.'
      );
      expect(longScore).toBeGreaterThan(shortScore);
    });

    it('should calculate adherence score with system prompt', () => {
      const service = promptTestingService as any;
      const score = service.calculateAdherence(
        'Here is a professional response.',
        'Respond in a professional tone.'
      );
      expect(score).toBeGreaterThan(50);
    });

    it('should return default adherence score without system prompt', () => {
      const service = promptTestingService as any;
      const score = service.calculateAdherence('Any response.');
      expect(score).toBe(75);
    });

    it('should calculate consistency score', () => {
      const service = promptTestingService as any;
      const consistentScore = service.calculateConsistency(
        'This is good. Therefore, it is positive.'
      );
      const inconsistentScore = service.calculateConsistency(
        'This is good. However, it is bad.'
      );
      expect(consistentScore).toBeGreaterThan(inconsistentScore);
    });

    it('should calculate creativity score', () => {
      const service = promptTestingService as any;
      const creativeScore = service.calculateCreativity(
        'Imagine a world where innovative solutions create unique opportunities.'
      );
      const boringScore = service.calculateCreativity('Yes. No. Maybe.');
      expect(creativeScore).toBeGreaterThan(boringScore);
    });
  });

  describe('generateSummary', () => {
    it('should generate summary with successful results', () => {
      const service = promptTestingService as any;
      const results = [
        {
          model: 'gpt-4',
          score: { fidelity: 80, adherence: 85, consistency: 90, creativity: 75 },
          timeToCompletion: 1000,
          tokens: { total: 100 }
        },
        {
          model: 'claude-3-sonnet',
          score: { fidelity: 85, adherence: 80, consistency: 85, creativity: 80 },
          timeToCompletion: 1200,
          tokens: { total: 120 }
        }
      ];

      const summary = service.generateSummary(results);
      expect(summary.averageScore).toBeGreaterThan(0);
      expect(summary.bestModel).toBeTruthy();
      expect(summary.worstModel).toBeTruthy();
      expect(summary.totalTokens).toBe(220);
      expect(summary.averageTime).toBe(1100);
    });

    it('should handle empty results', () => {
      const service = promptTestingService as any;
      const summary = service.generateSummary([]);
      expect(summary.averageScore).toBe(0);
      expect(summary.bestModel).toBe('None');
      expect(summary.worstModel).toBe('None');
      expect(summary.totalTokens).toBe(0);
      expect(summary.averageTime).toBe(0);
    });
  });

  describe('getAvailableModels', () => {
    it('should return list of available models', () => {
      const models = promptTestingService.getAvailableModels();
      expect(Array.isArray(models)).toBe(true);
      expect(models.length).toBeGreaterThan(0);
      expect(models[0]).toHaveProperty('id');
      expect(models[0]).toHaveProperty('name');
      expect(models[0]).toHaveProperty('provider');
    });
  });
});
