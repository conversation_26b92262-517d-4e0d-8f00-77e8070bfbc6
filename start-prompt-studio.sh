#!/bin/bash

# Prompt Studio Launcher
# This script helps you set up and run the Prompt Studio application

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

# Function to print header
print_header() {
    echo
    print_color $CYAN "==============================================="
    print_color $CYAN "          💠 Prompt Studio Launcher"
    print_color $CYAN "==============================================="
    echo
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to get user input
get_input() {
    read -p "$1" input
    echo $input
}

# Function to pause
pause() {
    read -p "Press any key to continue..." -n1 -s
    echo
}

print_header

# Check if Node.js is installed
if ! command_exists node; then
    print_color $RED "❌ Node.js is not installed or not in PATH"
    print_color $YELLOW "Please install Node.js from https://nodejs.org/"
    echo
    exit 1
fi

# Display Node.js version
NODE_VERSION=$(node --version)
print_color $GREEN "✅ Node.js version: $NODE_VERSION"

# Check if npm is available
if ! command_exists npm; then
    print_color $RED "❌ npm is not available"
    print_color $YELLOW "Please ensure npm is installed with Node.js"
    echo
    exit 1
fi

# Display npm version
NPM_VERSION=$(npm --version)
print_color $GREEN "✅ npm version: $NPM_VERSION"
echo

# Check if package.json exists
if [ ! -f "package.json" ]; then
    print_color $RED "❌ package.json not found"
    print_color $YELLOW "Please run this script from the Prompt Studio project directory"
    echo
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_color $BLUE "📦 Installing dependencies..."
    print_color $YELLOW "This may take a few minutes on first run..."
    echo
    npm install
    if [ $? -ne 0 ]; then
        print_color $RED "❌ Failed to install dependencies"
        print_color $YELLOW "Please check your internet connection and try again"
        echo
        exit 1
    fi
    print_color $GREEN "✅ Dependencies installed successfully"
    echo
else
    print_color $GREEN "✅ Dependencies already installed"
    echo
fi

# Check for environment file
if [ ! -f ".env" ]; then
    print_color $BLUE "🔧 Setting up environment configuration..."
    echo
    if [ -f ".env.example" ]; then
        cp ".env.example" ".env"
        print_color $GREEN "✅ Created .env file from .env.example"
    else
        cat > .env << EOF
# Prompt Studio Environment Configuration
# Add your AI API keys below

# OpenAI API Key
VITE_OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key
VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Other AI providers
# VITE_GEMINI_API_KEY=your_gemini_api_key_here
# VITE_MISTRAL_API_KEY=your_mistral_api_key_here
EOF
        print_color $GREEN "✅ Created .env file with template"
    fi
    echo
    print_color $YELLOW "⚠️  IMPORTANT: Please edit the .env file and add your AI API keys"
    print_color $YELLOW "   You can get API keys from:"
    print_color $YELLOW "   - OpenAI: https://platform.openai.com/api-keys"
    print_color $YELLOW "   - Anthropic: https://console.anthropic.com/"
    echo
    
    OPEN_ENV=$(get_input "Would you like to open the .env file now? (y/n): ")
    if [[ "$OPEN_ENV" =~ ^[Yy]$ ]]; then
        # Try different editors
        if command_exists code; then
            code .env
        elif command_exists nano; then
            nano .env
        elif command_exists vim; then
            vim .env
        elif command_exists gedit; then
            gedit .env
        else
            print_color $YELLOW "Please edit .env file manually with your preferred editor"
        fi
        echo
        print_color $YELLOW "Please save the .env file after adding your API keys, then press any key to continue..."
        pause
    fi
    echo
fi

# Check if .env has been configured
if grep -q "your_openai_api_key_here" .env 2>/dev/null; then
    print_color $YELLOW "⚠️  Warning: .env file still contains placeholder values"
    print_color $YELLOW "Please make sure to add your actual API keys for full functionality"
    echo
fi

# Function to show menu
show_menu() {
    print_color $BLUE "🚀 What would you like to do?"
    echo
    echo "1. Start development server (recommended)"
    echo "2. Build for production"
    echo "3. Run tests"
    echo "4. Install/update dependencies"
    echo "5. Open project in default editor"
    echo "6. View project info"
    echo "7. Exit"
    echo
}

# Function to start development server
start_dev() {
    echo
    print_color $BLUE "🚀 Starting Prompt Studio development server..."
    echo
    print_color $GREEN "The application will open in your default browser at http://localhost:5173"
    print_color $YELLOW "Press Ctrl+C to stop the server"
    echo
    npm run dev
}

# Function to build for production
build_prod() {
    echo
    print_color $BLUE "🏗️  Building Prompt Studio for production..."
    echo
    npm run build
    if [ $? -eq 0 ]; then
        print_color $GREEN "✅ Build completed successfully!"
        print_color $GREEN "Built files are in the 'dist' directory"
        echo
        print_color $CYAN "You can now deploy the contents of the 'dist' folder to your web server"
    else
        print_color $RED "❌ Build failed"
    fi
    echo
    pause
}

# Function to run tests
run_tests() {
    echo
    print_color $BLUE "🧪 Running tests..."
    echo
    npm test
    echo
    pause
}

# Function to install dependencies
install_deps() {
    echo
    print_color $BLUE "📦 Installing/updating dependencies..."
    echo
    npm install
    if [ $? -eq 0 ]; then
        print_color $GREEN "✅ Dependencies updated successfully!"
    else
        print_color $RED "❌ Failed to update dependencies"
    fi
    echo
    pause
}

# Function to open editor
open_editor() {
    echo
    print_color $BLUE "📝 Opening project in default editor..."
    echo
    if command_exists code; then
        code .
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        open .
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        xdg-open .
    else
        print_color $YELLOW "Please open the current directory in your preferred editor"
    fi
}

# Function to show project info
project_info() {
    echo
    print_color $CYAN "📋 Prompt Studio Project Information"
    print_color $CYAN "====================================="
    echo
    print_color $PURPLE "Project: Prompt Studio"
    print_color $PURPLE "Description: Comprehensive AI prompt engineering and testing platform"
    print_color $PURPLE "Version: 2.0.0"
    echo
    print_color $GREEN "Features:"
    echo "- Multi-model AI testing (OpenAI, Anthropic, etc.)"
    echo "- Agent simulation and conversation testing"
    echo "- Visual workflow builder (Chain Linker Canvas)"
    echo "- Prompt variations and A/B testing"
    echo "- Comprehensive analytics and scoring"
    echo "- Export tools and integrations"
    echo "- Persistent data storage"
    echo
    print_color $GREEN "Tech Stack:"
    echo "- React 18 + TypeScript"
    echo "- Vite build tool"
    echo "- Tailwind CSS + Shadcn/UI"
    echo "- IndexedDB for persistence"
    echo
    print_color $CYAN "Documentation: README.md"
    print_color $CYAN "Repository: Check package.json for details"
    echo
    pause
}

# Main menu loop
while true; do
    show_menu
    CHOICE=$(get_input "Enter your choice (1-7): ")
    
    case $CHOICE in
        1)
            start_dev
            break
            ;;
        2)
            build_prod
            ;;
        3)
            run_tests
            ;;
        4)
            install_deps
            ;;
        5)
            open_editor
            ;;
        6)
            project_info
            ;;
        7)
            echo
            print_color $GREEN "👋 Thanks for using Prompt Studio!"
            echo
            break
            ;;
        *)
            echo
            print_color $RED "❌ Invalid choice. Please enter a number between 1-7."
            echo
            pause
            ;;
    esac
done
