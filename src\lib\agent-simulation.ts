import { aiService, AIResponse } from './ai-service';
import { Agent } from '@/store/promptStore';

export interface AgentSimulationRequest {
  agents: Agent[];
  scenario: string;
  context?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface AgentResponse {
  agentId: string;
  agentName: string;
  response: string;
  confidence: number;
  sentiment: 'positive' | 'neutral' | 'constructive' | 'critical';
  keyInsights: string[];
  reasoning: string;
  aiResponse: AIResponse;
}

export interface SimulationResult {
  id: string;
  timestamp: Date;
  scenario: string;
  agentResponses: AgentResponse[];
  summary: {
    consensus: string;
    conflicts: string[];
    recommendations: string[];
  };
  metadata: {
    totalTokens: number;
    averageConfidence: number;
    duration: number;
  };
}

class AgentSimulationService {
  async runSimulation(request: AgentSimulationRequest): Promise<SimulationResult> {
    const startTime = Date.now();
    const agentResponses: AgentResponse[] = [];
    let totalTokens = 0;

    // Run simulation for each agent
    for (const agent of request.agents) {
      try {
        const agentResponse = await this.simulateAgentResponse(agent, request);
        agentResponses.push(agentResponse);
        totalTokens += agentResponse.aiResponse.usage?.totalTokens || 0;
      } catch (error) {
        console.error(`Failed to simulate response for agent ${agent.name}:`, error);
        // Add fallback response for failed agents
        agentResponses.push(this.createFallbackResponse(agent, error as Error));
      }
    }

    // Generate summary analysis
    const summary = await this.generateSummary(request.scenario, agentResponses);
    
    const duration = Date.now() - startTime;
    const averageConfidence = agentResponses.reduce((sum, r) => sum + r.confidence, 0) / agentResponses.length;

    return {
      id: Date.now().toString(),
      timestamp: new Date(),
      scenario: request.scenario,
      agentResponses,
      summary,
      metadata: {
        totalTokens,
        averageConfidence,
        duration
      }
    };
  }

  private async simulateAgentResponse(agent: Agent, request: AgentSimulationRequest): Promise<AgentResponse> {
    // Construct the prompt for the agent
    const messages = [
      {
        role: 'system' as const,
        content: this.buildAgentSystemPrompt(agent)
      },
      {
        role: 'user' as const,
        content: this.buildScenarioPrompt(request.scenario, request.context)
      }
    ];

    // Get AI response
    const aiResponse = await aiService.generateResponse({
      messages,
      temperature: request.temperature || 0.7,
      maxTokens: request.maxTokens || 1024
    });

    // Parse and analyze the response
    const analysis = this.analyzeResponse(aiResponse.content, agent);

    return {
      agentId: agent.id,
      agentName: agent.name,
      response: aiResponse.content,
      confidence: analysis.confidence,
      sentiment: analysis.sentiment,
      keyInsights: analysis.keyInsights,
      reasoning: analysis.reasoning,
      aiResponse
    };
  }

  private buildAgentSystemPrompt(agent: Agent): string {
    let prompt = agent.systemPrompt;
    
    if (agent.personality) {
      prompt += `\n\nPersonality: ${agent.personality}`;
    }
    
    if (agent.expertise && agent.expertise.length > 0) {
      prompt += `\n\nExpertise: ${agent.expertise.join(', ')}`;
    }

    prompt += `\n\nWhen responding to scenarios:
1. Stay in character as ${agent.role}
2. Provide thoughtful, detailed analysis
3. Include specific insights based on your expertise
4. Be constructive and professional
5. End with 2-3 key insights or recommendations
6. Format your response clearly with reasoning`;

    return prompt;
  }

  private buildScenarioPrompt(scenario: string, context?: string): string {
    let prompt = `Please analyze and respond to the following scenario:\n\n${scenario}`;
    
    if (context) {
      prompt += `\n\nAdditional context: ${context}`;
    }

    prompt += `\n\nPlease provide:
1. Your analysis of the situation
2. Your recommendations or perspective
3. Key insights based on your role and expertise
4. Any concerns or considerations

Format your response clearly and professionally.`;

    return prompt;
  }

  private analyzeResponse(response: string, agent: Agent): {
    confidence: number;
    sentiment: 'positive' | 'neutral' | 'constructive' | 'critical';
    keyInsights: string[];
    reasoning: string;
  } {
    // Extract key insights (look for numbered lists, bullet points, or "key" mentions)
    const keyInsights = this.extractKeyInsights(response);
    
    // Analyze sentiment based on language patterns
    const sentiment = this.analyzeSentiment(response);
    
    // Calculate confidence based on response quality indicators
    const confidence = this.calculateConfidence(response, agent);
    
    // Extract reasoning (first paragraph or main analysis)
    const reasoning = this.extractReasoning(response);

    return {
      confidence,
      sentiment,
      keyInsights,
      reasoning
    };
  }

  private extractKeyInsights(response: string): string[] {
    const insights: string[] = [];
    
    // Look for numbered lists
    const numberedMatches = response.match(/\d+\.\s*([^\n]+)/g);
    if (numberedMatches) {
      insights.push(...numberedMatches.map(match => match.replace(/\d+\.\s*/, '')));
    }
    
    // Look for bullet points
    const bulletMatches = response.match(/[•\-\*]\s*([^\n]+)/g);
    if (bulletMatches) {
      insights.push(...bulletMatches.map(match => match.replace(/[•\-\*]\s*/, '')));
    }
    
    // Look for "key" mentions
    const keyMatches = response.match(/(?:key|important|critical|main)\s+(?:insight|point|consideration|recommendation)[:\s]*([^\n\.]+)/gi);
    if (keyMatches) {
      insights.push(...keyMatches);
    }
    
    // If no structured insights found, extract last few sentences
    if (insights.length === 0) {
      const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 20);
      insights.push(...sentences.slice(-2).map(s => s.trim()));
    }
    
    return insights.slice(0, 3); // Limit to 3 key insights
  }

  private analyzeSentiment(response: string): 'positive' | 'neutral' | 'constructive' | 'critical' {
    const positiveWords = ['excellent', 'great', 'good', 'positive', 'beneficial', 'opportunity', 'success'];
    const criticalWords = ['problem', 'issue', 'concern', 'risk', 'challenge', 'difficult', 'poor'];
    const constructiveWords = ['improve', 'suggest', 'recommend', 'consider', 'enhance', 'optimize'];
    
    const lowerResponse = response.toLowerCase();
    
    const positiveCount = positiveWords.filter(word => lowerResponse.includes(word)).length;
    const criticalCount = criticalWords.filter(word => lowerResponse.includes(word)).length;
    const constructiveCount = constructiveWords.filter(word => lowerResponse.includes(word)).length;
    
    if (constructiveCount > positiveCount && constructiveCount > criticalCount) {
      return 'constructive';
    } else if (criticalCount > positiveCount) {
      return 'critical';
    } else if (positiveCount > 0) {
      return 'positive';
    } else {
      return 'neutral';
    }
  }

  private calculateConfidence(response: string, agent: Agent): number {
    let confidence = 50; // Base confidence
    
    // Length factor (longer responses generally indicate more thought)
    if (response.length > 500) confidence += 15;
    else if (response.length > 200) confidence += 10;
    
    // Structure factor (well-structured responses)
    if (response.includes('\n') || response.match(/\d+\./)) confidence += 10;
    
    // Expertise factor (mentions relevant expertise)
    if (agent.expertise) {
      const expertiseMatches = agent.expertise.filter(skill => 
        response.toLowerCase().includes(skill.toLowerCase())
      ).length;
      confidence += expertiseMatches * 5;
    }
    
    // Specificity factor (specific recommendations or insights)
    const specificWords = ['specifically', 'recommend', 'suggest', 'should', 'must', 'need to'];
    const specificCount = specificWords.filter(word => 
      response.toLowerCase().includes(word)
    ).length;
    confidence += specificCount * 3;
    
    return Math.min(95, Math.max(30, confidence)); // Clamp between 30-95
  }

  private extractReasoning(response: string): string {
    // Get the first substantial paragraph
    const paragraphs = response.split('\n\n').filter(p => p.trim().length > 50);
    return paragraphs[0] || response.substring(0, 200) + '...';
  }

  private createFallbackResponse(agent: Agent, error: Error): AgentResponse {
    return {
      agentId: agent.id,
      agentName: agent.name,
      response: `I apologize, but I'm currently unable to provide a detailed analysis due to a technical issue. As a ${agent.role}, I would typically focus on ${agent.expertise?.[0] || 'relevant aspects'} of this scenario.`,
      confidence: 20,
      sentiment: 'neutral',
      keyInsights: [`Technical issue prevented full analysis`, `Would focus on ${agent.role} perspective`],
      reasoning: `Error occurred: ${error.message}`,
      aiResponse: {
        content: '',
        model: 'fallback',
        provider: 'fallback',
        timestamp: new Date()
      }
    };
  }

  private async generateSummary(scenario: string, responses: AgentResponse[]): Promise<{
    consensus: string;
    conflicts: string[];
    recommendations: string[];
  }> {
    try {
      const summaryPrompt = this.buildSummaryPrompt(scenario, responses);
      
      const aiResponse = await aiService.generateResponse({
        messages: [
          {
            role: 'system',
            content: 'You are an expert facilitator analyzing multiple perspectives on a scenario. Provide a clear, structured summary.'
          },
          {
            role: 'user',
            content: summaryPrompt
          }
        ],
        temperature: 0.3,
        maxTokens: 512
      });

      return this.parseSummaryResponse(aiResponse.content);
    } catch (error) {
      console.error('Failed to generate summary:', error);
      return this.createFallbackSummary(responses);
    }
  }

  private buildSummaryPrompt(scenario: string, responses: AgentResponse[]): string {
    let prompt = `Scenario: ${scenario}\n\nAgent Responses:\n`;
    
    responses.forEach((response, index) => {
      prompt += `\n${index + 1}. ${response.agentName} (${response.sentiment}): ${response.response.substring(0, 200)}...\n`;
    });

    prompt += `\nPlease provide:
1. CONSENSUS: What do the agents generally agree on?
2. CONFLICTS: What are the main disagreements or different perspectives?
3. RECOMMENDATIONS: What are the top 3 actionable recommendations?

Format your response with clear sections.`;

    return prompt;
  }

  private parseSummaryResponse(content: string): {
    consensus: string;
    conflicts: string[];
    recommendations: string[];
  } {
    const sections = content.split(/(?:CONSENSUS|CONFLICTS|RECOMMENDATIONS):/i);
    
    return {
      consensus: sections[1]?.trim() || 'Multiple perspectives provided valuable insights.',
      conflicts: this.extractListItems(sections[2] || ''),
      recommendations: this.extractListItems(sections[3] || '')
    };
  }

  private extractListItems(text: string): string[] {
    const items = text.match(/(?:\d+\.|[•\-\*])\s*([^\n]+)/g);
    return items ? items.map(item => item.replace(/(?:\d+\.|[•\-\*])\s*/, '').trim()) : [];
  }

  private createFallbackSummary(responses: AgentResponse[]): {
    consensus: string;
    conflicts: string[];
    recommendations: string[];
  } {
    const sentiments = responses.map(r => r.sentiment);
    const avgConfidence = responses.reduce((sum, r) => sum + r.confidence, 0) / responses.length;
    
    return {
      consensus: `${responses.length} agents provided perspectives with ${avgConfidence.toFixed(0)}% average confidence.`,
      conflicts: sentiments.includes('critical') ? ['Some agents raised concerns'] : [],
      recommendations: responses.flatMap(r => r.keyInsights).slice(0, 3)
    };
  }
}

export const agentSimulationService = new AgentSimulationService();
