@echo off
setlocal enabledelayedexpansion

:: Prompt Studio Launcher
:: This script helps you set up and run the Prompt Studio application

echo.
echo ===============================================
echo          💠 Prompt Studio Launcher
echo ===============================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:: Display Node.js version
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js version: %NODE_VERSION%

:: Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available
    echo Please ensure npm is installed with Node.js
    echo.
    pause
    exit /b 1
)

:: Display npm version
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm version: %NPM_VERSION%
echo.

:: Check if package.json exists
if not exist "package.json" (
    echo ❌ package.json not found
    echo Please run this script from the Prompt Studio project directory
    echo.
    pause
    exit /b 1
)

:: Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    echo This may take a few minutes on first run...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        echo Please check your internet connection and try again
        echo.
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
    echo.

    echo 🔧 Fixing common setup issues...
    echo Updating browserslist database...
    npx browserslist@latest --update-db >nul 2>&1
    if %errorlevel% neq 0 (
        npm update caniuse-lite browserslist >nul 2>&1
    )
    echo ✅ Setup issues resolved
    echo.
) else (
    echo ✅ Dependencies already installed
    echo.
)

:: Check for environment file
if not exist ".env" (
    echo 🔧 Setting up environment configuration...
    echo.
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ Created .env file from .env.example
    ) else (
        echo # Prompt Studio Environment Configuration > .env
        echo # Add your AI API keys below >> .env
        echo. >> .env
        echo # OpenAI API Key >> .env
        echo VITE_OPENAI_API_KEY=your_openai_api_key_here >> .env
        echo. >> .env
        echo # Anthropic API Key >> .env
        echo VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here >> .env
        echo. >> .env
        echo # Optional: Other AI providers >> .env
        echo # VITE_GEMINI_API_KEY=your_gemini_api_key_here >> .env
        echo # VITE_MISTRAL_API_KEY=your_mistral_api_key_here >> .env
        echo.
        echo ✅ Created .env file with template
    )
    echo.
    echo ⚠️  IMPORTANT: Please edit the .env file and add your AI API keys
    echo    You can get API keys from:
    echo    - OpenAI: https://platform.openai.com/api-keys
    echo    - Anthropic: https://console.anthropic.com/
    echo.
    echo Would you like to open the .env file now? (y/n)
    set /p OPEN_ENV=
    if /i "!OPEN_ENV!"=="y" (
        start notepad .env
        echo.
        echo Please save the .env file after adding your API keys, then press any key to continue...
        pause >nul
    )
    echo.
)

:: Check if .env has been configured
findstr /C:"your_openai_api_key_here" .env >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  Warning: .env file still contains placeholder values
    echo Please make sure to add your actual API keys for full functionality
    echo.
)

:: Offer menu options
echo 🚀 What would you like to do?
echo.
echo 1. Start development server (recommended)
echo 2. Build for production
echo 3. Run tests
echo 4. Install/update dependencies
echo 5. Fix common issues (vulnerabilities, browserslist)
echo 6. Open project in default editor
echo 7. View project info
echo 8. Exit
echo.
set /p CHOICE=Enter your choice (1-8):

if "%CHOICE%"=="1" goto :start_dev
if "%CHOICE%"=="2" goto :build_prod
if "%CHOICE%"=="3" goto :run_tests
if "%CHOICE%"=="4" goto :install_deps
if "%CHOICE%"=="5" goto :fix_issues
if "%CHOICE%"=="6" goto :open_editor
if "%CHOICE%"=="7" goto :project_info
if "%CHOICE%"=="8" goto :exit
goto :invalid_choice

:start_dev
echo.
echo 🚀 Starting Prompt Studio development server...
echo.
echo The application will open in your default browser at http://localhost:5173
echo Press Ctrl+C to stop the server
echo.
npm run dev
goto :end

:build_prod
echo.
echo 🏗️  Building Prompt Studio for production...
echo.
npm run build
if %errorlevel% equ 0 (
    echo ✅ Build completed successfully!
    echo Built files are in the 'dist' directory
    echo.
    echo You can now deploy the contents of the 'dist' folder to your web server
) else (
    echo ❌ Build failed
)
echo.
pause
goto :end

:run_tests
echo.
echo 🧪 Running tests...
echo.
npm test
echo.
pause
goto :end

:install_deps
echo.
echo 📦 Installing/updating dependencies...
echo.
npm install
if %errorlevel% equ 0 (
    echo ✅ Dependencies updated successfully!
) else (
    echo ❌ Failed to update dependencies
)
echo.
pause
goto :end

:fix_issues
echo.
echo 🔧 Fixing common issues...
echo.
echo 1. Updating browserslist database...
npx browserslist@latest --update-db
if %errorlevel% neq 0 (
    echo   Trying alternative method...
    npm update caniuse-lite browserslist
)
echo   ✅ Browserslist updated
echo.

echo 2. Fixing npm vulnerabilities...
npm audit fix
if %errorlevel% neq 0 (
    echo   Some vulnerabilities require manual review
    echo   Run 'npm audit' to see details
)
echo   ✅ Vulnerabilities addressed
echo.

echo 3. Clearing npm cache...
npm cache clean --force
echo   ✅ Cache cleared
echo.

echo 🎉 Issues fixed!
echo.
pause
goto :end

:open_editor
echo.
echo 📝 Opening project in default editor...
echo.
start .
goto :end

:project_info
echo.
echo 📋 Prompt Studio Project Information
echo =====================================
echo.
echo Project: Prompt Studio
echo Description: Comprehensive AI prompt engineering and testing platform
echo Version: 2.0.0
echo.
echo Features:
echo - Multi-model AI testing (OpenAI, Anthropic, etc.)
echo - Agent simulation and conversation testing
echo - Visual workflow builder (Chain Linker Canvas)
echo - Prompt variations and A/B testing
echo - Comprehensive analytics and scoring
echo - Export tools and integrations
echo - Persistent data storage
echo.
echo Tech Stack:
echo - React 18 + TypeScript
echo - Vite build tool
echo - Tailwind CSS + Shadcn/UI
echo - IndexedDB for persistence
echo.
echo Documentation: README.md
echo Repository: Check package.json for details
echo.
pause
goto :end

:invalid_choice
echo.
echo ❌ Invalid choice. Please enter a number between 1-8.
echo.
pause
goto :end

:exit
echo.
echo 👋 Thanks for using Prompt Studio!
echo.
goto :end

:end
echo.
echo Press any key to exit...
pause >nul
