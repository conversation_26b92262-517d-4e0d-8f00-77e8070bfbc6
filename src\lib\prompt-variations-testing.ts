import { promptTestingService, PromptTestResult } from './prompt-testing';
import { PromptVariant } from '@/store/promptStore';

export interface VariationTestRequest {
  variants: PromptVariant[];
  models: string[];
  temperature?: number;
  maxTokens?: number;
  testScenarios?: string[];
}

export interface VariantTestResult {
  variant: PromptVariant;
  testResults: PromptTestResult[];
  aggregatedScores: {
    averageFidelity: number;
    averageAdherence: number;
    averageConsistency: number;
    averageCreativity: number;
    overallScore: number;
  };
  performance: {
    averageTime: number;
    totalTokens: number;
    successRate: number;
  };
  bestModel: string;
  worstModel: string;
}

export interface VariationTestSummary {
  id: string;
  timestamp: Date;
  packInfo: {
    title: string;
    totalVariations: number;
    timestamp: Date;
  };
  variants: VariantTestResult[];
  comparison: {
    bestVariant: string;
    worstVariant: string;
    mostConsistent: string;
    mostCreative: string;
    recommendations: string[];
  };
  statistics: {
    totalTests: number;
    totalTokens: number;
    averageTime: number;
    successRate: number;
  };
  metadata: {
    modelsUsed: string[];
    duration: number;
    testScenarios: string[];
  };
}

class PromptVariationsTestingService {
  async runVariationTests(request: VariationTestRequest): Promise<VariationTestSummary> {
    const startTime = Date.now();
    const variantResults: VariantTestResult[] = [];

    // Default test scenarios if none provided
    const testScenarios = request.testScenarios || ['default'];

    for (const variant of request.variants) {
      try {
        const variantResult = await this.testVariant(
          variant,
          request.models,
          testScenarios,
          request.temperature,
          request.maxTokens
        );
        variantResults.push(variantResult);
      } catch (error) {
        console.error(`Failed to test variant ${variant.name}:`, error);
        variantResults.push(this.createFailedVariantResult(variant, error as Error));
      }
    }

    const duration = Date.now() - startTime;
    const comparison = this.generateComparison(variantResults);
    const statistics = this.calculateStatistics(variantResults);

    return {
      id: Date.now().toString(),
      timestamp: new Date(),
      packInfo: {
        title: 'Prompt Variation Test',
        totalVariations: request.variants.length,
        timestamp: new Date()
      },
      variants: variantResults,
      comparison,
      statistics,
      metadata: {
        modelsUsed: request.models,
        duration,
        testScenarios
      }
    };
  }

  private async testVariant(
    variant: PromptVariant,
    models: string[],
    testScenarios: string[],
    temperature?: number,
    maxTokens?: number
  ): Promise<VariantTestResult> {
    const testResults: PromptTestResult[] = [];

    // Test the variant with each scenario
    for (const scenario of testScenarios) {
      // Process the prompt with variables and scenario context
      const processedPrompt = this.processVariantPrompt(variant, scenario);
      
      const testResult = await promptTestingService.runPromptTest({
        prompt: processedPrompt,
        systemPrompt: variant.systemPrompt,
        models,
        temperature,
        maxTokens,
        variables: variant.variables
      });

      testResults.push(testResult);
    }

    // Aggregate scores across all test results
    const aggregatedScores = this.aggregateScores(testResults);
    const performance = this.calculatePerformance(testResults);
    const { bestModel, worstModel } = this.findBestWorstModels(testResults);

    return {
      variant,
      testResults,
      aggregatedScores,
      performance,
      bestModel,
      worstModel
    };
  }

  private processVariantPrompt(variant: PromptVariant, scenario: string): string {
    let processedPrompt = variant.prompt;

    // Replace variables in the prompt
    Object.entries(variant.variables).forEach(([key, value]) => {
      const patterns = [
        new RegExp(`\\{\\{${key}\\}\\}`, 'g'),
        new RegExp(`\\{${key}\\}`, 'g')
      ];
      
      for (const pattern of patterns) {
        processedPrompt = processedPrompt.replace(pattern, value);
      }
    });

    // Add scenario context if it's not the default
    if (scenario !== 'default') {
      processedPrompt = `Context: ${scenario}\n\n${processedPrompt}`;
    }

    return processedPrompt;
  }

  private aggregateScores(testResults: PromptTestResult[]): {
    averageFidelity: number;
    averageAdherence: number;
    averageConsistency: number;
    averageCreativity: number;
    overallScore: number;
  } {
    if (testResults.length === 0) {
      return {
        averageFidelity: 0,
        averageAdherence: 0,
        averageConsistency: 0,
        averageCreativity: 0,
        overallScore: 0
      };
    }

    let totalFidelity = 0;
    let totalAdherence = 0;
    let totalConsistency = 0;
    let totalCreativity = 0;
    let totalResults = 0;

    testResults.forEach(testResult => {
      testResult.results.forEach(result => {
        if (!result.error) {
          totalFidelity += result.score.fidelity;
          totalAdherence += result.score.adherence;
          totalConsistency += result.score.consistency;
          totalCreativity += result.score.creativity;
          totalResults++;
        }
      });
    });

    if (totalResults === 0) {
      return {
        averageFidelity: 0,
        averageAdherence: 0,
        averageConsistency: 0,
        averageCreativity: 0,
        overallScore: 0
      };
    }

    const averageFidelity = totalFidelity / totalResults;
    const averageAdherence = totalAdherence / totalResults;
    const averageConsistency = totalConsistency / totalResults;
    const averageCreativity = totalCreativity / totalResults;
    const overallScore = (averageFidelity + averageAdherence + averageConsistency + averageCreativity) / 4;

    return {
      averageFidelity,
      averageAdherence,
      averageConsistency,
      averageCreativity,
      overallScore
    };
  }

  private calculatePerformance(testResults: PromptTestResult[]): {
    averageTime: number;
    totalTokens: number;
    successRate: number;
  } {
    let totalTime = 0;
    let totalTokens = 0;
    let totalTests = 0;
    let successfulTests = 0;

    testResults.forEach(testResult => {
      testResult.results.forEach(result => {
        totalTime += result.timeToCompletion;
        totalTokens += result.tokens.total;
        totalTests++;
        if (!result.error) {
          successfulTests++;
        }
      });
    });

    return {
      averageTime: totalTests > 0 ? totalTime / totalTests : 0,
      totalTokens,
      successRate: totalTests > 0 ? (successfulTests / totalTests) * 100 : 0
    };
  }

  private findBestWorstModels(testResults: PromptTestResult[]): {
    bestModel: string;
    worstModel: string;
  } {
    const modelScores: Record<string, number[]> = {};

    testResults.forEach(testResult => {
      testResult.results.forEach(result => {
        if (!result.error) {
          const overallScore = (
            result.score.fidelity + 
            result.score.adherence + 
            result.score.consistency + 
            result.score.creativity
          ) / 4;

          if (!modelScores[result.model]) {
            modelScores[result.model] = [];
          }
          modelScores[result.model].push(overallScore);
        }
      });
    });

    let bestModel = 'Unknown';
    let worstModel = 'Unknown';
    let bestAverage = -1;
    let worstAverage = Infinity;

    Object.entries(modelScores).forEach(([model, scores]) => {
      const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      
      if (average > bestAverage) {
        bestAverage = average;
        bestModel = model;
      }
      
      if (average < worstAverage) {
        worstAverage = average;
        worstModel = model;
      }
    });

    return { bestModel, worstModel };
  }

  private generateComparison(variantResults: VariantTestResult[]): {
    bestVariant: string;
    worstVariant: string;
    mostConsistent: string;
    mostCreative: string;
    recommendations: string[];
  } {
    if (variantResults.length === 0) {
      return {
        bestVariant: 'None',
        worstVariant: 'None',
        mostConsistent: 'None',
        mostCreative: 'None',
        recommendations: []
      };
    }

    // Find best and worst variants by overall score
    let bestVariant = variantResults[0];
    let worstVariant = variantResults[0];
    let mostConsistent = variantResults[0];
    let mostCreative = variantResults[0];

    variantResults.forEach(result => {
      if (result.aggregatedScores.overallScore > bestVariant.aggregatedScores.overallScore) {
        bestVariant = result;
      }
      if (result.aggregatedScores.overallScore < worstVariant.aggregatedScores.overallScore) {
        worstVariant = result;
      }
      if (result.aggregatedScores.averageConsistency > mostConsistent.aggregatedScores.averageConsistency) {
        mostConsistent = result;
      }
      if (result.aggregatedScores.averageCreativity > mostCreative.aggregatedScores.averageCreativity) {
        mostCreative = result;
      }
    });

    // Generate recommendations
    const recommendations = this.generateRecommendations(variantResults);

    return {
      bestVariant: bestVariant.variant.name,
      worstVariant: worstVariant.variant.name,
      mostConsistent: mostConsistent.variant.name,
      mostCreative: mostCreative.variant.name,
      recommendations
    };
  }

  private generateRecommendations(variantResults: VariantTestResult[]): string[] {
    const recommendations: string[] = [];

    // Analyze performance patterns
    const averageScore = variantResults.reduce((sum, result) => 
      sum + result.aggregatedScores.overallScore, 0) / variantResults.length;

    if (averageScore < 60) {
      recommendations.push('Consider revising prompts for better clarity and specificity');
    }

    // Check for consistency issues
    const consistencyScores = variantResults.map(r => r.aggregatedScores.averageConsistency);
    const consistencyVariance = this.calculateVariance(consistencyScores);
    
    if (consistencyVariance > 400) {
      recommendations.push('High variance in consistency scores - review prompt structure');
    }

    // Check for performance issues
    const averageTime = variantResults.reduce((sum, result) => 
      sum + result.performance.averageTime, 0) / variantResults.length;

    if (averageTime > 5000) {
      recommendations.push('Consider reducing prompt complexity to improve response time');
    }

    // Success rate analysis
    const averageSuccessRate = variantResults.reduce((sum, result) => 
      sum + result.performance.successRate, 0) / variantResults.length;

    if (averageSuccessRate < 90) {
      recommendations.push('Low success rate detected - check API configuration and prompt formatting');
    }

    return recommendations.length > 0 ? recommendations : ['All variants performed well - consider A/B testing in production'];
  }

  private calculateVariance(numbers: number[]): number {
    const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
    const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
  }

  private calculateStatistics(variantResults: VariantTestResult[]): {
    totalTests: number;
    totalTokens: number;
    averageTime: number;
    successRate: number;
  } {
    let totalTests = 0;
    let totalTokens = 0;
    let totalTime = 0;
    let successfulTests = 0;

    variantResults.forEach(result => {
      result.testResults.forEach(testResult => {
        testResult.results.forEach(modelResult => {
          totalTests++;
          totalTokens += modelResult.tokens.total;
          totalTime += modelResult.timeToCompletion;
          if (!modelResult.error) {
            successfulTests++;
          }
        });
      });
    });

    return {
      totalTests,
      totalTokens,
      averageTime: totalTests > 0 ? totalTime / totalTests : 0,
      successRate: totalTests > 0 ? (successfulTests / totalTests) * 100 : 0
    };
  }

  private createFailedVariantResult(variant: PromptVariant, error: Error): VariantTestResult {
    return {
      variant,
      testResults: [],
      aggregatedScores: {
        averageFidelity: 0,
        averageAdherence: 0,
        averageConsistency: 0,
        averageCreativity: 0,
        overallScore: 0
      },
      performance: {
        averageTime: 0,
        totalTokens: 0,
        successRate: 0
      },
      bestModel: 'None',
      worstModel: 'None'
    };
  }
}

export const promptVariationsTestingService = new PromptVariationsTestingService();
