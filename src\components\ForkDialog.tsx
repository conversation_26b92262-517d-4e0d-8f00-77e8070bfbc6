
import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  GitBranch, 
  Lightbulb, 
  Settings, 
  FileText, 
  Zap, 
  AlertCircle,
  CheckCircle,
  Copy
} from 'lucide-react';
import { ForkableResult, ForkConfiguration, liveForkingService } from '@/lib/live-forking';
import { usePromptStore } from '@/hooks/usePromptStore';
import { toast } from '@/hooks/use-toast';

interface ForkDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  forkableContent: ForkableResult | null;
}

export const ForkDialog = ({ open, onOpenChange, forkableContent }: ForkDialogProps) => {
  const { addPrompt } = usePromptStore();
  
  const [configuration, setConfiguration] = useState<ForkConfiguration>({
    name: '',
    purpose: '',
    modifications: {},
    preserveOriginal: true,
    autoTest: false
  });

  const [isForking, setIsForking] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [suggestedModifications, setSuggestedModifications] = useState<any>({});

  useEffect(() => {
    if (forkableContent && open) {
      // Generate default values
      const defaultName = liveForkingService.generateDefaultForkName(forkableContent);
      const defaultPurpose = liveForkingService.generateDefaultPurpose(forkableContent);
      const suggestions = liveForkingService.generateSuggestedModifications(forkableContent);

      setConfiguration({
        name: defaultName,
        purpose: defaultPurpose,
        modifications: {
          prompt: forkableContent.content.prompt,
          systemPrompt: forkableContent.content.systemPrompt,
          variables: forkableContent.content.variables || {},
          temperature: 0.7,
          maxTokens: 1024
        },
        preserveOriginal: true,
        autoTest: false
      });

      setSuggestedModifications(suggestions);
      setValidationErrors([]);
    }
  }, [forkableContent, open]);

  const handleFork = async () => {
    if (!forkableContent) return;

    // Validate configuration
    const validation = liveForkingService.validateForkConfiguration(configuration);
    if (!validation.valid) {
      setValidationErrors(validation.errors);
      return;
    }

    setIsForking(true);
    setValidationErrors([]);

    try {
      const result = await liveForkingService.forkToVariant(
        forkableContent,
        configuration,
        addPrompt
      );

      if (result.success) {
        toast({
          title: "Fork Created Successfully",
          description: `Created new variant: ${configuration.name}`,
        });
        onOpenChange(false);
      } else {
        toast({
          title: "Fork Failed",
          description: result.error || "Failed to create fork",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Fork Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsForking(false);
    }
  };

  const applySuggestion = (field: string, value: any) => {
    setConfiguration(prev => ({
      ...prev,
      modifications: {
        ...prev.modifications,
        [field]: value
      }
    }));
  };

  const copyOriginalContent = () => {
    if (!forkableContent) return;
    
    setConfiguration(prev => ({
      ...prev,
      modifications: {
        ...prev.modifications,
        prompt: forkableContent.content.prompt,
        systemPrompt: forkableContent.content.systemPrompt,
        variables: forkableContent.content.variables || {}
      }
    }));

    toast({
      title: "Content Copied",
      description: "Original content has been copied to the fork",
    });
  };

  if (!forkableContent) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5 text-purple-400" />
            Fork to Prompt Variations
          </DialogTitle>
          <DialogDescription>
            Create a new prompt variant based on this {forkableContent.type.replace('_', ' ')} result.
            You can modify the content and settings before creating the fork.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Original Content Preview */}
          <div className="bg-slate-900/50 p-4 rounded-lg border border-slate-700">
            <div className="flex items-center gap-2 mb-3">
              <FileText className="h-4 w-4 text-blue-400" />
              <span className="font-medium text-slate-200">Original Content</span>
              <Badge variant="outline">{forkableContent.type.replace('_', ' ')}</Badge>
            </div>
            
            <div className="space-y-2 text-sm">
              {forkableContent.content.prompt && (
                <div>
                  <span className="text-slate-400">Prompt:</span>
                  <p className="text-slate-300 bg-slate-800 p-2 rounded mt-1">
                    {forkableContent.content.prompt.substring(0, 200)}
                    {forkableContent.content.prompt.length > 200 && '...'}
                  </p>
                </div>
              )}
              
              {forkableContent.content.response && (
                <div>
                  <span className="text-slate-400">Response:</span>
                  <p className="text-slate-300 bg-slate-800 p-2 rounded mt-1">
                    {forkableContent.content.response.substring(0, 200)}
                    {forkableContent.content.response.length > 200 && '...'}
                  </p>
                </div>
              )}

              {forkableContent.content.score && (
                <div>
                  <span className="text-slate-400">Scores:</span>
                  <div className="flex gap-2 mt-1">
                    {Object.entries(forkableContent.content.score).map(([key, value]) => (
                      <Badge key={key} variant="secondary" className="text-xs">
                        {key}: {typeof value === 'number' ? value.toFixed(1) : String(value)}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Fork Configuration */}
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fork-name">Variant Name *</Label>
                <Input
                  id="fork-name"
                  value={configuration.name}
                  onChange={(e) => setConfiguration(prev => ({ ...prev, name: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="Enter variant name..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="fork-purpose">Purpose *</Label>
                <Textarea
                  id="fork-purpose"
                  value={configuration.purpose}
                  onChange={(e) => setConfiguration(prev => ({ ...prev, purpose: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white min-h-[100px]"
                  placeholder="Describe the purpose of this variant..."
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-slate-200">Preserve Original</Label>
                  <p className="text-sm text-slate-400">Keep the original result in history</p>
                </div>
                <Switch
                  checked={configuration.preserveOriginal}
                  onCheckedChange={(checked) => setConfiguration(prev => ({ ...prev, preserveOriginal: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-slate-200">Auto Test</Label>
                  <p className="text-sm text-slate-400">Automatically test the forked variant</p>
                </div>
                <Switch
                  checked={configuration.autoTest}
                  onCheckedChange={(checked) => setConfiguration(prev => ({ ...prev, autoTest: checked }))}
                />
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-slate-200">Content Modifications</h3>
                <Button onClick={copyOriginalContent} variant="outline" size="sm">
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Original
                </Button>
              </div>

              {/* Suggestions */}
              {Object.keys(suggestedModifications).length > 0 && (
                <Alert className="border-yellow-600/30 bg-yellow-900/20">
                  <Lightbulb className="h-4 w-4 text-yellow-400" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="text-yellow-300 font-medium">Suggested Improvements:</p>
                      {Object.entries(suggestedModifications).map(([field, value]) => (
                        <div key={field} className="flex items-center justify-between">
                          <span className="text-sm text-yellow-200 capitalize">
                            {field.replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                          <Button
                            onClick={() => applySuggestion(field, value as string)}
                            size="sm"
                            variant="outline"
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        </div>
                      ))}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="fork-prompt">Prompt</Label>
                <Textarea
                  id="fork-prompt"
                  value={configuration.modifications.prompt || ''}
                  onChange={(e) => setConfiguration(prev => ({
                    ...prev,
                    modifications: { ...prev.modifications, prompt: e.target.value }
                  }))}
                  className="bg-slate-900/50 border-slate-600 text-white min-h-[150px]"
                  placeholder="Enter the modified prompt..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="fork-system-prompt">System Prompt (Optional)</Label>
                <Textarea
                  id="fork-system-prompt"
                  value={configuration.modifications.systemPrompt || ''}
                  onChange={(e) => setConfiguration(prev => ({
                    ...prev,
                    modifications: { ...prev.modifications, systemPrompt: e.target.value }
                  }))}
                  className="bg-slate-900/50 border-slate-600 text-white min-h-[100px]"
                  placeholder="Enter system prompt instructions..."
                />
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Temperature: {configuration.modifications.temperature || 0.7}</Label>
                  <Slider
                    value={[configuration.modifications.temperature || 0.7]}
                    onValueChange={([value]) => setConfiguration(prev => ({
                      ...prev,
                      modifications: { ...prev.modifications, temperature: value }
                    }))}
                    max={2}
                    min={0}
                    step={0.1}
                    className="w-full"
                  />
                  <p className="text-xs text-slate-400">
                    Controls randomness. Lower values make responses more focused.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Max Tokens: {configuration.modifications.maxTokens || 1024}</Label>
                  <Slider
                    value={[configuration.modifications.maxTokens || 1024]}
                    onValueChange={([value]) => setConfiguration(prev => ({
                      ...prev,
                      modifications: { ...prev.modifications, maxTokens: value }
                    }))}
                    max={4096}
                    min={100}
                    step={100}
                    className="w-full"
                  />
                  <p className="text-xs text-slate-400">
                    Maximum number of tokens in the response.
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <Alert className="border-red-600/30 bg-red-900/20">
              <AlertCircle className="h-4 w-4 text-red-400" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="text-red-300 font-medium">Please fix the following errors:</p>
                  {validationErrors.map((error, index) => (
                    <p key={index} className="text-sm text-red-200">• {error}</p>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleFork} 
            disabled={isForking || validationErrors.length > 0}
            className="bg-purple-600 hover:bg-purple-700"
          >
            {isForking ? (
              <>
                <Zap className="h-4 w-4 mr-2 animate-pulse" />
                Creating Fork...
              </>
            ) : (
              <>
                <GitBranch className="h-4 w-4 mr-2" />
                Create Fork
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
