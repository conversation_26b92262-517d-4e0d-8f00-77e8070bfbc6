import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Plus, Copy, Trash2, Play, GitBranch, FileText, Settings, BookOpen, Lightbulb, Wrench, GitFork } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { VariantEditor } from './VariantEditor'; // Import the new VariantEditor

// Align with the main PromptVariant type from the store for consistency
interface PromptVariant {
  id: string;
  name: string;
  prompt: string;
  systemPrompt?: string;
  variables: Record<string, string>;
  purpose?: string;
  whenToUse?: string;
  whenNotToUse?: string;
  suggestions?: string;
  experimentalSubVariants?: string[];
  buildKit?: {
    frame: string;
    example: string;
  };
  // Lineage fields
  parentId?: string;
  version?: number;
  lineageId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface PromptPack {
  title: string;
  version: string;
  authors: string;
  date: string;
  tags: string[];
  purpose: string;
  variants: PromptVariant[];
}

interface PromptVariationsProps {
  onRunVariations: (variations: PromptVariant[]) => void;
}

export const PromptVariations = ({ onRunVariations }: PromptVariationsProps) => {
  const [promptPack, setPromptPack] = useState<PromptPack>({
    title: 'Untitled Prompt Pack',
    version: '1.0',
    authors: '',
    date: new Date().toLocaleDateString(),
    tags: [],
    purpose: '',
    variants: [
      {
        id: '1',
        name: 'Base Variant',
        prompt: 'Write a comprehensive guide about {topic} for {audience}.',
        systemPrompt: 'You are a helpful technical writer.',
        variables: { topic: 'React hooks', audience: 'beginners' },
        version: 1,
        lineageId: '1', // Initial variant's lineageId is its own Id
        createdAt: new Date(),
        updatedAt: new Date(),
        purpose: 'Foundational prompt for creating structured technical guides',
        whenToUse: 'When you need clear, comprehensive documentation or tutorials',
        whenNotToUse: 'For quick answers or when brevity is required',
        suggestions: 'Works well as a base for more specialized variants',
        experimentalSubVariants: [
          'Create a detailed guide about {topic} specifically designed for {audience}.',
          'Develop a step-by-step tutorial on {topic} tailored for {audience}.'
        ],
        buildKit: {
          frame: '[ Action Directive + Content Specification + Audience Target ]',
          example: 'Write a guide | about {topic} | for {audience}'
        }
      }
    ]
  });

  const [activeVariantId, setActiveVariantId] = useState('1');
  const [isAddingVariant, setIsAddingVariant] = useState(false);
  const [newVariant, setNewVariant] = useState<Partial<PromptVariant>>({
    name: '',
    prompt: '',
    systemPrompt: '',
    variables: {},
    purpose: '',
    whenToUse: '',
    whenNotToUse: '',
    suggestions: '',
    experimentalSubVariants: [''],
    buildKit: { frame: '', example: '' }
  });
  const [variableInput, setVariableInput] = useState({ key: '', value: '' });
  const [newTag, setNewTag] = useState('');

  const handleUpdatePackInfo = (field: keyof PromptPack, value: any) => {
    setPromptPack(prev => ({ ...prev, [field]: value }));
  };

  const handleAddTag = () => {
    if (!newTag.trim()) return;
    setPromptPack(prev => ({
      ...prev,
      tags: [...prev.tags, newTag.trim()]
    }));
    setNewTag('');
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setPromptPack(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleAddVariant = () => {
    if (!newVariant.name || !newVariant.prompt) {
      toast({
        title: "Error",
        description: "Please provide a name and prompt",
        variant: "destructive"
      });
      return;
    }

    const newId = Date.now().toString();
    const variant: PromptVariant = {
      id: newId,
      name: newVariant.name || '',
      prompt: newVariant.prompt || '',
      systemPrompt: newVariant.systemPrompt || '',
      variables: newVariant.variables || {},
      parentId: undefined, // Not a fork by default
      version: 1,          // Initial version
      lineageId: newId,    // Root of its own lineage
      createdAt: new Date(),
      updatedAt: new Date(),
      purpose: newVariant.purpose || '',
      whenToUse: newVariant.whenToUse || '',
      whenNotToUse: newVariant.whenNotToUse || '',
      suggestions: newVariant.suggestions || '',
      experimentalSubVariants: newVariant.experimentalSubVariants?.filter(v => v.trim()) || [],
      buildKit: newVariant.buildKit || { frame: '', example: '' }
    };

    setPromptPack(prev => ({
      ...prev,
      variants: [...prev.variants, variant]
    }));
    setNewVariant({
      name: '', prompt: '', systemPrompt: '', variables: {},
      purpose: '', whenToUse: '', whenNotToUse: '', suggestions: '',
      experimentalSubVariants: [''], buildKit: { frame: '', example: '' }
    });
    setIsAddingVariant(false);
    setActiveVariantId(variant.id);
    toast({
      title: "Variant Added",
      description: `${variant.name} has been added to the prompt pack`,
    });
  };

  const handleDuplicateVariant = (variant: PromptVariant) => {
    const duplicate: PromptVariant = {
      ...JSON.parse(JSON.stringify(variant)), // Deep copy
      id: Date.now().toString(),
      name: `${variant.name} (Copy)`,
      // When duplicating, it's a new "root" in terms of versioning for this copy, but could share lineage.
      // Or, consider if duplicate should also be a "fork". For now, let's make it independent.
      parentId: undefined, // A duplicate is not a direct child in a fork tree by default
      version: 1,
      lineageId: Date.now().toString() + "_copy_lineage", // New lineage for a distinct copy
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setPromptPack(prev => ({
      ...prev,
      variants: [...prev.variants, duplicate]
    }));
    setActiveVariantId(duplicate.id);
    toast({ title: "Variant Duplicated", description: `Created a copy: ${duplicate.name}`});
  };

  const handleForkVariant = (variantToFork: PromptVariant) => {
    const forkName = window.prompt("Enter a name for the forked variant:", `${variantToFork.name} - Fork`);
    if (!forkName) return; // User cancelled

    const forkedId = Date.now().toString();
    // Deep copy relevant data for forking
    const {
      id, parentId, version, name, lineageId, createdAt, updatedAt,
      ...originalDataToCopy
    } = variantToFork;

    const forkedVariant: PromptVariant = {
      ...JSON.parse(JSON.stringify(originalDataToCopy)),
      id: forkedId,
      name: forkName,
      parentId: variantToFork.id, // Link to the parent
      version: 1,                  // New forks start at version 1
      lineageId: variantToFork.lineageId || variantToFork.id, // Inherit lineage ID
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setPromptPack(prev => ({
      ...prev,
      variants: [...prev.variants, forkedVariant]
    }));
    setActiveVariantId(forkedVariant.id);
    toast({ title: "Variant Forked", description: `Created fork: ${forkedVariant.name}`});
  };

  const handleDeleteVariant = (id: string) => {
    setPromptPack(prev => ({
      ...prev,
      variants: prev.variants.filter(v => v.id !== id)
    }));
    if (activeVariantId === id) {
      setActiveVariantId(promptPack.variants[0]?.id || '');
    }
  };

  const handleUpdateVariant = (id: string, field: keyof PromptVariant, value: any) => {
    setPromptPack(prev => ({
      ...prev,
      variants: prev.variants.map(v => 
        v.id === id ? { ...v, [field]: value } : v
      )
    }));
  };

  const handleAddVariable = () => {
    if (!variableInput.key || !variableInput.value) return;
    
    setNewVariant(prev => ({
      ...prev,
      variables: {
        ...prev.variables,
        [variableInput.key]: variableInput.value
      }
    }));
    setVariableInput({ key: '', value: '' });
  };

  const handleAddSubVariant = () => {
    setNewVariant(prev => ({
      ...prev,
      experimentalSubVariants: [...(prev.experimentalSubVariants || []), '']
    }));
  };

  const handleUpdateSubVariant = (index: number, value: string) => {
    setNewVariant(prev => ({
      ...prev,
      experimentalSubVariants: prev.experimentalSubVariants?.map((v, i) => 
        i === index ? value : v
      ) || []
    }));
  };

  const handleRemoveSubVariant = (index: number) => {
    setNewVariant(prev => ({
      ...prev,
      experimentalSubVariants: prev.experimentalSubVariants?.filter((_, i) => i !== index) || []
    }));
  };

  const renderPromptWithVariables = (prompt: string, variables: Record<string, string>) => {
    let rendered = prompt;
    Object.entries(variables).forEach(([key, value]) => {
      rendered = rendered.replace(new RegExp(`{${key}}`, 'g'), `[${value}]`);
    });
    return rendered;
  };

  const activeVariant = promptPack.variants.find(v => v.id === activeVariantId);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-200">Prompt Studio</h2>
        <div className="flex gap-2">
          <Button
            onClick={() => setIsAddingVariant(true)}
            className="bg-gradient-to-r from-brand-blue to-brand-purple hover:from-brand-blue/90 hover:to-brand-purple/90 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Variant
          </Button>
          <Button
            onClick={() => onRunVariations(promptPack.variants)}
            disabled={promptPack.variants.length === 0}
            className="bg-gradient-to-r from-brand-blue to-brand-purple hover:from-brand-blue/90 hover:to-brand-purple/90 text-white"
          >
            <Play className="w-4 h-4 mr-2" />
            Test Pack
          </Button>
        </div>
      </div>

      <Tabs defaultValue="pack-info" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-slate-800/50 border border-slate-600">
          <TabsTrigger 
            value="pack-info" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-blue-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <FileText className="w-4 h-4 mr-2" />
            Pack Info
          </TabsTrigger>
          <TabsTrigger 
            value="variants" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <GitBranch className="w-4 h-4 mr-2" />
            Variants
          </TabsTrigger>
          <TabsTrigger 
            value="documentation" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-green-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <BookOpen className="w-4 h-4 mr-2" />
            Documentation
          </TabsTrigger>
          <TabsTrigger 
            value="testing" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-600 data-[state=active]:to-orange-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <Settings className="w-4 h-4 mr-2" />
            Testing
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pack-info" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-200 mb-4 flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-400" />
              Prompt Pack Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-slate-200">Pack Title</Label>
                <Input
                  value={promptPack.title}
                  onChange={(e) => handleUpdatePackInfo('title', e.target.value)}
                  className="bg-slate-900/50 border-slate-600 text-white"
                />
              </div>
              <div>
                <Label className="text-slate-200">Version</Label>
                <Input
                  value={promptPack.version}
                  onChange={(e) => handleUpdatePackInfo('version', e.target.value)}
                  className="bg-slate-900/50 border-slate-600 text-white"
                />
              </div>
              <div>
                <Label className="text-slate-200">Authors</Label>
                <Input
                  value={promptPack.authors}
                  onChange={(e) => handleUpdatePackInfo('authors', e.target.value)}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="e.g., Alice, Bob"
                />
              </div>
              <div>
                <Label className="text-slate-200">Date</Label>
                <Input
                  value={promptPack.date}
                  onChange={(e) => handleUpdatePackInfo('date', e.target.value)}
                  className="bg-slate-900/50 border-slate-600 text-white"
                />
              </div>
            </div>
            
            <div className="mt-4">
              <Label className="text-slate-200">Purpose & Description</Label>
              <Textarea
                value={promptPack.purpose}
                onChange={(e) => handleUpdatePackInfo('purpose', e.target.value)}
                className="bg-slate-900/50 border-slate-600 text-white mt-2"
                placeholder="Describe the purpose and scope of this prompt pack..."
              />
            </div>

            <div className="mt-4">
              <Label className="text-slate-200">Tags</Label>
              <div className="flex gap-2 mt-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag..."
                  className="bg-slate-900/50 border-slate-600 text-white flex-1"
                  onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                />
                <Button onClick={handleAddTag} variant="outline" className="border-slate-600">
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-3">
                {promptPack.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="bg-slate-700 text-slate-300">
                    {tag}
                    <button
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-2 text-red-400 hover:text-red-300"
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="variants" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Variants Sidebar */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-slate-200 mb-4">Variants ({promptPack.variants.length})</h3>
              {promptPack.variants.map((variant) => (
                <Card 
                  key={variant.id} 
                  className={`p-3 cursor-pointer transition-colors ${
                    activeVariantId === variant.id 
                      ? 'bg-blue-600/20 border-blue-500' 
                      : 'bg-slate-800/50 border-slate-700 hover:bg-slate-700/50'
                  }`}
                  onClick={() => setActiveVariantId(variant.id)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-slate-200 text-sm">{variant.name}</h4>
                      <div className="text-xs text-slate-400 mt-1 space-x-2">
                        <span>{Object.keys(variant.variables).length} vars</span>
                        <span>v{variant.version || 1}</span>
                        {variant.parentId && promptPack.variants.find(p=>p.id === variant.parentId) && (
                          <span className="italic" title={`Parent: ${promptPack.variants.find(p=>p.id === variant.parentId)?.name}`}>
                            (Fork)
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1 items-center">
                       {/* Removed icon-only lineage display here as it's now text */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => { e.stopPropagation(); handleForkVariant(variant); }}
                        className="text-yellow-400 hover:text-yellow-300 p-1 h-auto"
                        title="Fork this variant"
                      >
                        <GitFork className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicateVariant(variant);
                        }}
                        className="text-slate-400 hover:text-slate-300 p-1 h-auto"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteVariant(variant.id);
                        }}
                        className="text-red-400 hover:text-red-300 p-1 h-auto"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Variant Editor */}
            <div className="lg:col-span-3">
              <VariantEditor
                variant={activeVariant || null}
                allVariants={promptPack.variants}
                onUpdateVariant={handleUpdateVariant}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="documentation" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-200 mb-4">Generated Documentation Preview</h3>
            <div className="space-y-6">
              <div>
                <h4 className="text-xl font-bold text-slate-200">{promptPack.title}</h4>
                <p className="text-slate-400">Version: {promptPack.version} | Authors: <AUTHORS>
                <div className="flex flex-wrap gap-2 mt-2">
                  {promptPack.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="border-slate-500 text-slate-300">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h5 className="font-semibold text-slate-200">Purpose</h5>
                <p className="text-slate-300 mt-1">{promptPack.purpose || 'No purpose defined yet.'}</p>
              </div>

              <div>
                <h5 className="font-semibold text-slate-200">Variants ({promptPack.variants.length})</h5>
                <div className="space-y-4 mt-3">
                  {promptPack.variants.map((variant, index) => (
                    <Card key={variant.id} className="bg-slate-900/50 border-slate-600 p-4">
                      <h6 className="font-medium text-slate-200">{index + 1}. {variant.name}</h6>
                      <p className="text-sm text-slate-300 mt-2 font-mono bg-slate-800 p-2 rounded">
                        "{variant.prompt}"
                      </p>
                      {variant.purpose && (
                        <div className="mt-3">
                          <p className="text-xs font-semibold text-slate-400">Purpose & Utility:</p>
                          <p className="text-sm text-slate-300">{variant.purpose}</p>
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-200 mb-4">Testing Configuration</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{promptPack.variants.length}</div>
                  <div className="text-sm text-slate-400">Total Variants</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {promptPack.variants.reduce((acc, v) => acc + Object.keys(v.variables).length, 0)}
                  </div>
                  <div className="text-sm text-slate-400">Total Variables</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">
                    {promptPack.variants.reduce((acc, v) => acc + (v.experimentalSubVariants?.length || 0), 0)}
                  </div>
                  <div className="text-sm text-slate-400">Sub-Variants</div>
                </div>
              </div>
              
              <Button
                onClick={() => onRunVariations(promptPack.variants)}
                disabled={promptPack.variants.length === 0}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                size="lg"
              >
                <Play className="w-4 h-4 mr-2" />
                Test All Variants in Pack
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Variant Modal */}
      {isAddingVariant && (
        <Card className="bg-slate-800/50 border-slate-700 p-6 space-y-4">
          <h3 className="text-lg font-semibold text-slate-200">Create New Variant</h3>
          
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-slate-900/50">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="advanced">Documentation</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-4">
              <div>
                <Label className="text-slate-200">Variant Name *</Label>
                <Input
                  value={newVariant.name || ''}
                  onChange={(e) => setNewVariant(prev => ({ ...prev, name: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="e.g., Context-Aware Sequencer"
                />
              </div>

              <div>
                <Label className="text-slate-200">System Prompt</Label>
                <Textarea
                  value={newVariant.systemPrompt || ''}
                  onChange={(e) => setNewVariant(prev => ({ ...prev, systemPrompt: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="Define system behavior..."
                />
              </div>

              <div>
                <Label className="text-slate-200">Main Prompt *</Label>
                <Textarea
                  value={newVariant.prompt || ''}
                  onChange={(e) => setNewVariant(prev => ({ ...prev, prompt: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white min-h-[120px]"
                  placeholder="Use {variable_name} for dynamic content..."
                />
              </div>

              <div>
                <Label className="text-slate-200">Variables</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    placeholder="Variable name"
                    value={variableInput.key}
                    onChange={(e) => setVariableInput(prev => ({ ...prev, key: e.target.value }))}
                    className="bg-slate-900/50 border-slate-600 text-white flex-1"
                  />
                  <Input
                    placeholder="Value"
                    value={variableInput.value}
                    onChange={(e) => setVariableInput(prev => ({ ...prev, value: e.target.value }))}
                    className="bg-slate-900/50 border-slate-600 text-white flex-1"
                  />
                  <Button onClick={handleAddVariable} variant="outline" className="border-slate-600">
                    Add
                  </Button>
                </div>
                {newVariant.variables && Object.keys(newVariant.variables).length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-2">
                    {Object.entries(newVariant.variables).map(([key, value]) => (
                      <Badge key={key} variant="secondary" className="bg-slate-700 text-slate-300">
                        {key}: {value}
                        <button
                          onClick={() => {
                            const newVars = { ...newVariant.variables };
                            delete newVars[key];
                            setNewVariant(prev => ({ ...prev, variables: newVars }));
                          }}
                          className="ml-2 text-red-400 hover:text-red-300"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4 mt-4">
              <div>
                <Label className="text-slate-200">Purpose & Utility</Label>
                <Textarea
                  value={newVariant.purpose || ''}
                  onChange={(e) => setNewVariant(prev => ({ ...prev, purpose: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="Explain what this variant does and why it's useful..."
                />
              </div>

              <div>
                <Label className="text-slate-200">When to Use</Label>
                <Textarea
                  value={newVariant.whenToUse || ''}
                  onChange={(e) => setNewVariant(prev => ({ ...prev, whenToUse: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="Describe ideal use cases..."
                />
              </div>

              <div>
                <Label className="text-slate-200">When NOT to Use</Label>
                <Textarea
                  value={newVariant.whenNotToUse || ''}
                  onChange={(e) => setNewVariant(prev => ({ ...prev, whenNotToUse: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="Warn about inappropriate use cases..."
                />
              </div>

              <div>
                <Label className="text-slate-200">Experimental Sub-Variants</Label>
                <div className="space-y-2 mt-2">
                  {newVariant.experimentalSubVariants?.map((subVariant, index) => (
                    <div key={index} className="flex gap-2">
                      <Textarea
                        value={subVariant}
                        onChange={(e) => handleUpdateSubVariant(index, e.target.value)}
                        className="bg-slate-900/50 border-slate-600 text-white flex-1"
                        placeholder="Alternative version..."
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveSubVariant(index)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddSubVariant}
                    className="border-slate-600 text-slate-300"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Sub-Variant
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex gap-2 pt-4 border-t border-slate-600">
            <Button onClick={handleAddVariant} className="bg-gradient-to-r from-brand-blue to-brand-purple hover:from-brand-blue/90 hover:to-brand-purple/90 text-white">
              <Plus className="w-4 h-4 mr-2" />
              Add to Pack
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setIsAddingVariant(false)}
              className="border-slate-600 text-slate-300"
            >
              Cancel
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};
