import { databaseService } from '@/lib/database';
import { agentSimulationService, AgentSimulationRequest, SimulationResult as SingleAgentSimulationResult, AgentResponse as SingleAgentResponse } from '@/lib/agent-simulation';

interface Agent {
  id: string;
  name: string;
  role: string;
  systemPrompt: string;
  personality: string;
  enabled: boolean;
  expertise?: string[];
  avatar?: string;
  modelConfig?: {
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
  };
}

interface PromptVariant {
  id: string;
  name: string;
  prompt: string;
  systemPrompt?: string;
  variables: Record<string, string>;
  purpose?: string;
  whenToUse?: string;
  whenNotToUse?: string;
  suggestions?: string;
  experimentalSubVariants?: string[];
  buildKit?: {
    frame: string;
    example: string;
  };
  compatibleAgents?: string[];
  parentId?: string; // ID of the prompt this was forked from
  version?: number;   // Version number, e.g., 1 for initial, increments on edit/fork
  lineageId?: string; // ID to group a prompt and all its forks
  createdAt?: Date;   // Added for tracking creation
  updatedAt?: Date;   // Added for tracking updates
}

interface TestResult {
  id: string;
  timestamp: Date;
  type: 'prompt_test' | 'agent_simulation' | 'variation_testing' | 'project_simulation' | 'integrated_test';
  data: any;
  scores?: {
    fidelity?: number;
    adherence?: number;
    consistency?: number;
    creativity?: number;
    accuracy?: number;
  };
}

interface InteractionDetail {
  promptId: string;
  promptName: string;
  agentId: string;
  agentName: string;
  response?: string;
  confidence?: number;
  sentiment?: 'positive' | 'neutral' | 'constructive' | 'critical';
  keyInsights?: string[];
  reasoning?: string;
  error?: string;
  aiResponseSnapshot?: {
    content: string;
    usage?: { promptTokens: number; completionTokens: number; totalTokens: number };
    model: string;
    provider: string;
  };
}

class PromptStore {
  private agents: Agent[] = [];
  private prompts: PromptVariant[] = [];
  private results: TestResult[] = [];
  private listeners: (() => void)[] = [];
  private initialized = false;

  private defaultAgents: Agent[] = [
    {
      id: '1',
      name: 'Code Reviewer',
      role: 'Senior Developer',
      systemPrompt: 'You are a senior developer conducting code reviews. Focus on code quality, best practices, and potential issues.',
      personality: 'Thorough, constructive, detail-oriented',
      enabled: true,
      expertise: ['React', 'TypeScript', 'Testing', 'Architecture'],
      avatar: '👨‍💻'
    },
    {
      id: '2',
      name: 'Product Manager',
      role: 'Product Strategy',
      systemPrompt: 'You are a product manager evaluating features from a business perspective. Consider user needs and market impact.',
      personality: 'Strategic, user-focused, data-driven',
      enabled: true,
      expertise: ['Strategy', 'User Research', 'Analytics', 'Roadmapping'],
      avatar: '📊'
    },
    {
      id: '3',
      name: 'UX Designer',
      role: 'User Experience',
      systemPrompt: 'You are a UX designer focused on creating intuitive and accessible user experiences.',
      personality: 'Creative, empathetic, user-centric',
      enabled: false,
      expertise: ['Design Systems', 'Accessibility', 'User Testing', 'Prototyping'],
      avatar: '🎨'
    }
  ];

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await databaseService.initialize();
      await this.loadFromDatabase();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize database, using memory storage:', error);
      this.agents = [...this.defaultAgents];
      this.initialized = true;
    }
  }

  private async loadFromDatabase(): Promise<void> {
    try {
      const [agents, prompts, results] = await Promise.all([
        databaseService.getAll('agents'),
        databaseService.getAll('prompts'),
        databaseService.getAll('results')
      ]);

      this.agents = agents.length > 0 ? agents : [...this.defaultAgents];
      this.prompts = prompts;
      this.results = results;

      // If no agents in database, save default ones
      if (agents.length === 0) {
        for (const agent of this.defaultAgents) {
          await databaseService.add('agents', agent);
        }
      }
    } catch (error) {
      console.error('Failed to load from database:', error);
      this.agents = [...this.defaultAgents];
    }
  }

  // Agent methods
  getAgents(): Agent[] {
    return [...this.agents];
  }

  getEnabledAgents(): Agent[] {
    return this.agents.filter(agent => agent.enabled);
  }

  async addAgent(agent: Omit<Agent, 'id'>): Promise<Agent> {
    const newAgent: Agent = {
      ...agent,
      id: Date.now().toString()
    };

    this.agents.push(newAgent);

    try {
      await databaseService.add('agents', newAgent);
    } catch (error) {
      console.error('Failed to save agent to database:', error);
    }

    this.notifyListeners();
    return newAgent;
  }

  async updateAgent(id: string, updates: Partial<Agent>): Promise<void> {
    const index = this.agents.findIndex(agent => agent.id === id);
    if (index !== -1) {
      this.agents[index] = { ...this.agents[index], ...updates };

      try {
        await databaseService.update('agents', this.agents[index]);
      } catch (error) {
        console.error('Failed to update agent in database:', error);
      }

      this.notifyListeners();
    }
  }

  async deleteAgent(id: string): Promise<void> {
    this.agents = this.agents.filter(agent => agent.id !== id);

    try {
      await databaseService.delete('agents', id);
    } catch (error) {
      console.error('Failed to delete agent from database:', error);
    }

    this.notifyListeners();
  }

  toggleAgent(id: string): void {
    const agent = this.agents.find(a => a.id === id);
    if (agent) {
      agent.enabled = !agent.enabled;
      this.notifyListeners();
    }
  }

  // Prompt methods
  getPrompts(): PromptVariant[] {
    return [...this.prompts];
  }

  async addPrompt(promptData: Omit<PromptVariant, 'id' | 'version' | 'lineageId' | 'createdAt' | 'updatedAt'>): Promise<PromptVariant> {
    const newId = Date.now().toString();
    const newPrompt: PromptVariant = {
      ...promptData,
      id: newId,
      parentId: undefined, // Not a fork by default
      version: 1,          // Initial version
      lineageId: newId,    // Root of its own lineage
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.prompts.push(newPrompt);

    try {
      await databaseService.add('prompts', newPrompt);
    } catch (error) {
      console.error('Failed to save prompt to database:', error);
    }

    this.notifyListeners();
    return newPrompt;
  }

  async updatePrompt(id: string, updates: Partial<PromptVariant>): Promise<void> {
    const index = this.prompts.findIndex(prompt => prompt.id === id);
    if (index !== -1) {
      this.prompts[index] = {
        ...this.prompts[index],
        ...updates,
        updatedAt: new Date(), // Update timestamp
        // Optionally, increment version here if updates are considered significant
        // version: (this.prompts[index].version || 1) + (updates.prompt || updates.systemPrompt ? 0.1 : 0)
      };

      try {
        await databaseService.update('prompts', this.prompts[index]);
      } catch (error) {
        console.error('Failed to update prompt in database:', error);
      }

      this.notifyListeners();
    }
  }

  async forkPrompt(originalPromptId: string, newName?: string): Promise<PromptVariant> {
    const originalPrompt = this.prompts.find(p => p.id === originalPromptId);
    if (!originalPrompt) {
      throw new Error(`Prompt with ID ${originalPromptId} not found.`);
    }

    // Deep copy the original prompt's data, excluding fields that should be new
    const { id, parentId, version, name, createdAt, updatedAt, ...originalDataToCopy } = originalPrompt;

    const forkedId = Date.now().toString();
    const forkedPrompt: PromptVariant = {
      ...JSON.parse(JSON.stringify(originalDataToCopy)), // Deep copy relevant data
      id: forkedId,
      name: newName || `${originalPrompt.name} - Fork`,
      parentId: originalPrompt.id,
      version: 1, // New forks start at version 1 of their own branch
      lineageId: originalPrompt.lineageId || originalPrompt.id, // Inherit lineage or start one from parent
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.prompts.push(forkedPrompt); // Add to in-memory store

    try {
      await databaseService.add('prompts', forkedPrompt); // Persist to DB
    } catch (error) {
      console.error('Failed to save forked prompt to database:', error);
      // Potentially remove from in-memory store if DB save fails, or handle differently
      this.prompts = this.prompts.filter(p => p.id !== forkedId);
      throw error; // Re-throw to notify caller
    }

    this.notifyListeners();
    return forkedPrompt;
  }

  async deletePrompt(id: string): Promise<void> {
    this.prompts = this.prompts.filter(prompt => prompt.id !== id);

    try {
      await databaseService.delete('prompts', id);
    } catch (error) {
      console.error('Failed to delete prompt from database:', error);
    }

    this.notifyListeners();
  }

  // Results methods
  getResults(): TestResult[] {
    return [...this.results].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  async addResult(result: Omit<TestResult, 'id'>): Promise<void> {
    const newResult: TestResult = {
      ...result,
      id: Date.now().toString()
    };

    this.results.unshift(newResult);

    try {
      await databaseService.add('results', newResult);
    } catch (error) {
      console.error('Failed to save result to database:', error);
    }

    this.notifyListeners();
  }

  async clearResults(): Promise<void> {
    this.results = [];

    try {
      await databaseService.clear('results');
    } catch (error) {
      console.error('Failed to clear results from database:', error);
    }

    this.notifyListeners();
  }

  // Subscription methods
  subscribe(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  // Cross-feature integration methods
  getCompatibleAgents(promptId: string): Agent[] {
    const prompt = this.prompts.find(p => p.id === promptId);
    if (!prompt?.compatibleAgents) return this.getEnabledAgents();
    
    return this.agents.filter(agent => 
      prompt.compatibleAgents!.includes(agent.id) && agent.enabled
    );
  }

  async runIntegratedTest(promptIds: string[], agentIds: string[], scenario?: string): Promise<TestResult> {
    const selectedPrompts = this.prompts.filter(p => promptIds.includes(p.id));
    const selectedAgents = this.agents.filter(a => agentIds.includes(a.id) && a.enabled);

    const testId = Date.now().toString();

    if (selectedAgents.length === 0) {
      const errorData: Omit<TestResult, 'id'> = {
        timestamp: new Date(),
        type: 'integrated_test',
        data: {
          originalPrompts: selectedPrompts.map(p => ({id: p.id, name: p.name})),
          originalAgents: [],
          overallScenario: scenario,
          interactionResults: [],
          summary: "Test aborted: No enabled agents were selected for the test.",
          metadata: { totalPromptTokens: 0, totalCompletionTokens: 0, totalTokens: 0, averageConfidence: 0 },
        },
        scores: { fidelity: 0, adherence: 0 }
      };
      await this.addResult(errorData);
      return { ...errorData, id: testId };
    }

    if (selectedPrompts.length === 0) {
      const errorData: Omit<TestResult, 'id'> = {
        timestamp: new Date(),
        type: 'integrated_test',
        data: {
          originalPrompts: [],
          originalAgents: selectedAgents.map(a => ({id: a.id, name: a.name, role: a.role})),
          overallScenario: scenario,
          interactionResults: [],
          summary: "Test aborted: No prompts were selected for the test.",
          metadata: { totalPromptTokens: 0, totalCompletionTokens: 0, totalTokens: 0, averageConfidence: 0 },
        },
        scores: { fidelity: 0, adherence: 0 }
      };
      await this.addResult(errorData);
      return { ...errorData, id: testId };
    }

    const interactionResults: InteractionDetail[] = [];
    let totalPromptTokensOverall = 0;
    let totalCompletionTokensOverall = 0;
    let totalSuccessfulInteractions = 0;
    let cumulativeConfidence = 0;

    for (const promptVariant of selectedPrompts) {
      for (const agent of selectedAgents) {
        const effectiveSystemPrompt = promptVariant.systemPrompt || agent.systemPrompt;

        const agentForThisRun: Agent = {
          ...agent,
          systemPrompt: effectiveSystemPrompt,
          modelConfig: {
            provider: agent.modelConfig?.provider || 'openai',
            model: agent.modelConfig?.model || 'gpt-4',
            temperature: agent.modelConfig?.temperature ?? 0.7,
            maxTokens: agent.modelConfig?.maxTokens ?? 1024,
          }
        };

        let currentTaskForAgent = "";
        if (scenario && scenario.trim().length > 0) {
          currentTaskForAgent += `Overall Scenario Context: "${scenario}"\n\n`;
        }
        currentTaskForAgent += `Based on your role and the overall scenario (if any), please address the following specific prompt:\n\nPrompt: "${promptVariant.prompt}"`;

        const simulationRequest: AgentSimulationRequest = {
          agents: [agentForThisRun],
          scenario: currentTaskForAgent,
          temperature: agentForThisRun.modelConfig.temperature,
          maxTokens: agentForThisRun.modelConfig.maxTokens,
        };

        try {
          const singleAgentSimResult: SingleAgentSimulationResult = await agentSimulationService.runSimulation(simulationRequest);

          if (singleAgentSimResult.agentResponses && singleAgentSimResult.agentResponses.length > 0) {
            const agentResponse = singleAgentSimResult.agentResponses[0];
            interactionResults.push({
              promptId: promptVariant.id,
              promptName: promptVariant.name,
              agentId: agent.id,
              agentName: agent.name,
              response: agentResponse.response,
              confidence: agentResponse.confidence,
              sentiment: agentResponse.sentiment,
              keyInsights: agentResponse.keyInsights,
              reasoning: agentResponse.reasoning,
              aiResponseSnapshot: agentResponse.aiResponse ? {
                content: agentResponse.aiResponse.content,
                usage: agentResponse.aiResponse.usage,
                model: agentResponse.aiResponse.model,
                provider: agentResponse.aiResponse.provider,
              } : undefined,
            });
            totalPromptTokensOverall += agentResponse.aiResponse?.usage?.promptTokens || 0;
            totalCompletionTokensOverall += agentResponse.aiResponse?.usage?.completionTokens || 0;
            cumulativeConfidence += agentResponse.confidence || 0;
            totalSuccessfulInteractions++;
          } else {
            interactionResults.push({
              promptId: promptVariant.id,
              promptName: promptVariant.name,
              agentId: agent.id,
              agentName: agent.name,
              error: "Agent did not produce a response in the simulation.",
            });
          }
        } catch (error: any) {
          console.error(`Error during interaction test (Prompt: ${promptVariant.name}, Agent: ${agent.name}):`, error);
          interactionResults.push({
            promptId: promptVariant.id,
            promptName: promptVariant.name,
            agentId: agent.id,
            agentName: agent.name,
            error: error.message || "Unknown error during agent-prompt interaction.",
          });
        }
      }
    }

    const averageConfidence = totalSuccessfulInteractions > 0
      ? cumulativeConfidence / totalSuccessfulInteractions
      : 0;
    const totalInteractionsCount = selectedPrompts.length * selectedAgents.length;

    const resultToStore: Omit<TestResult, 'id'> = {
      timestamp: new Date(),
      type: 'integrated_test' as const,
      data: {
        originalPrompts: selectedPrompts.map(p => ({id: p.id, name: p.name, prompt: p.prompt, systemPrompt: p.systemPrompt})),
        originalAgents: selectedAgents.map(a => ({id: a.id, name: a.name, role: a.role, systemPrompt: a.systemPrompt})),
        overallScenario: scenario,
        interactionResults,
        summary: `Tested ${selectedPrompts.length} prompt(s) against ${selectedAgents.length} agent(s). ${totalSuccessfulInteractions}/${totalInteractionsCount} interactions successful.`,
        metadata: {
            totalPromptTokens: totalPromptTokensOverall,
            totalCompletionTokens: totalCompletionTokensOverall,
            totalTokens: totalPromptTokensOverall + totalCompletionTokensOverall,
            averageConfidence: parseFloat(averageConfidence.toFixed(2)),
        }
      },
      scores: {
        fidelity: parseFloat(averageConfidence.toFixed(2)),
        adherence: parseFloat(averageConfidence.toFixed(2)),
      }
    };

    await this.addResult(resultToStore);
    return { ...resultToStore, id: testId };
  }

  // Backup and restore methods
  async createBackup(name: string): Promise<string> {
    try {
      return await databaseService.createBackup(name);
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw error;
    }
  }

  async restoreBackup(backupId: string): Promise<void> {
    try {
      await databaseService.restoreBackup(backupId);
      await this.loadFromDatabase();
    } catch (error) {
      console.error('Failed to restore backup:', error);
      throw error;
    }
  }

  async getBackups(): Promise<any[]> {
    try {
      return await databaseService.getBackups();
    } catch (error) {
      console.error('Failed to get backups:', error);
      return [];
    }
  }

  async deleteBackup(backupId: string): Promise<void> {
    try {
      await databaseService.deleteBackup(backupId);
    } catch (error) {
      console.error('Failed to delete backup:', error);
      throw error;
    }
  }

  // Export and import methods
  async exportData(): Promise<any> {
    try {
      return await databaseService.exportData();
    } catch (error) {
      console.error('Failed to export data:', error);
      throw error;
    }
  }

  async importData(data: any): Promise<void> {
    try {
      await databaseService.importData(data);
      await this.loadFromDatabase();
    } catch (error) {
      console.error('Failed to import data:', error);
      throw error;
    }
  }

  // Database statistics
  async getStats(): Promise<any> {
    try {
      return await databaseService.getStats();
    } catch (error) {
      console.error('Failed to get stats:', error);
      return {
        prompts: this.prompts.length,
        agents: this.agents.length,
        results: this.results.length,
        backups: 0,
        totalSize: 0
      };
    }
  }

  // Cleanup old data
  async cleanup(daysToKeep: number = 30): Promise<void> {
    try {
      await databaseService.cleanup(daysToKeep);
      await this.loadFromDatabase();
    } catch (error) {
      console.error('Failed to cleanup data:', error);
      throw error;
    }
  }
}

export const promptStore = new PromptStore();
export type { Agent, PromptVariant, TestResult };
