// =================================================================================================
// PROMPT STORE - Custom Class-based State Management
// ... (initial comments remain the same) ...
// =================================================================================================

import { databaseService } from '@/lib/database';
import { agentSimulationService, AgentSimulationRequest, SimulationResult as SingleAgentSimulationResult, AgentResponse as SingleAgentResponse } from '@/lib/agent-simulation';
import type { ChainFlow, ChainNode, ChainConnection } from '@/lib/chain-linker';
import type { ImportedPromptStudioData } from '@/lib/import-tools'; // Added import
// TestRunExecutionMetadata is defined later in this file.

interface Agent {
  id: string;
  name: string;
  role: string;
  systemPrompt: string;
  personality: string;
  enabled: boolean;
  expertise?: string[];
  avatar?: string;
  modelConfig?: {
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
  };
}

interface PromptVariant {
  id: string;
  name: string;
  prompt: string;
  systemPrompt?: string;
  variables: Record<string, string>;
  purpose?: string;
  whenToUse?: string;
  whenNotToUse?: string;
  experimentalSubVariants?: string[];
  buildKit?: {
    frame: string;
    example: string;
  };
  compatibleAgents?: string[];
  parentId?: string;
  version?: number;
  lineageId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  promptType?: string;
  definedOutputFormat?: string;
  tags?: string[];
  usageNotes?: string;
  targetModels?: string[];
  desiredTone?: string;
  contextualDepth?: string;
  lastPreviewSnapshot?: {
    output: string;
    metadata: any; // modelUsed, tokens, runtime
    variablesUsed: Record<string, string>;
    timestamp: string;
  };
}

export interface TestRunExecutionMetadata {
  testGoal?: string;
  inputSample?: string;
  expectedOutputFormat?: string;
  estimatedTokens?: number;
}

export interface NodeTemplate {
  id: string;
  name: string;
  description?: string;
  nodeType: 'prompt' | 'agent' | 'condition' | 'output' | 'input' | string;
  nodeData: any;
  createdAt: Date;
  tags?: string[];
}

// For P4.3 Community Vault
export type VaultItemType = 'prompt_pack' | 'flow_blueprint' | 'node_template';

export interface VaultItemSummary {
  id: string; // ID of the original item (e.g., prompt.id, flow.id, nodeTemplate.id)
  vaultId: string; // Unique ID for the vault entry itself
  type: VaultItemType;
  name: string;
  description?: string;
  author?: string;
  tags?: string[];
  createdAt: Date; // Publish date
  upvotes?: number; // Mocked
  forksCount?: number; // Mocked
  previewSnippet?: string;
  itemCounts?: { prompts?: number; agents?: number; results?: number; flows?: number; };
}
// End P4.3 Community Vault types

interface TestResult {
  id: string;
  timestamp: Date;
  type: 'prompt_test' | 'agent_simulation' | 'variation_testing' | 'project_simulation' | 'integrated_test';
  data: any;
  runMetadata?: TestRunExecutionMetadata;
  scores?: {
    fidelity?: number;
    adherence?: number;
    consistency?: number;
    creativity?: number;
    accuracy?: number;
  };
}

interface InteractionDetail {
  promptId: string;
  promptName: string;
  agentId: string;
  agentName: string;
  response?: string;
  confidence?: number;
  sentiment?: 'positive' | 'neutral' | 'constructive' | 'critical';
  keyInsights?: string[];
  reasoning?: string;
  error?: string;
  aiResponseSnapshot?: {
    content: string;
    usage?: { promptTokens: number; completionTokens: number; totalTokens: number };
    model: string;
    provider: string;
  };
}

class PromptStore {
  private agents: Agent[] = [];
  private prompts: PromptVariant[] = [];
  private results: TestResult[] = [];
  private nodeTemplates: NodeTemplate[] = [];
  private chainFlows: ChainFlow[] = [];
  private communityVaultItems: VaultItemSummary[] = []; // Added for Vault
  private listeners: (() => void)[] = [];
  private initialized = false;

  private defaultAgents: Agent[] = [ /* ... default agents ... */ ];

  async initialize(): Promise<void> {
    if (this.initialized) return;
    try {
      await databaseService.initialize();
      await this.loadFromDatabase();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize database, using memory storage:', error);
      this.agents = [...this.defaultAgents]; // Keep default agents if DB fails
      this.prompts = [];
      this.results = [];
      this.nodeTemplates = [];
      this.chainFlows = [];
      this.communityVaultItems = [];
      this.initialized = true;
    }
  }

  private async loadFromDatabase(): Promise<void> {
    try {
      const [agents, prompts, results, nodeTemplates, chainFlows, communityVaultItems] = await Promise.all([
        databaseService.getAll('agents'),
        databaseService.getAll('prompts'),
        databaseService.getAll('results'),
        databaseService.getAll('nodeTemplates'),
        databaseService.getAll('chainFlows'),
        databaseService.getAll('communityVaultItems')
      ]);

      this.agents = agents.length > 0 ? agents : [...this.defaultAgents];
      this.prompts = prompts || [];
      this.results = results || [];
      this.nodeTemplates = nodeTemplates || [];
      this.chainFlows = chainFlows || [];
      this.communityVaultItems = communityVaultItems || [];

      if (agents.length === 0 && this.defaultAgents.length > 0) {
        for (const agent of this.defaultAgents) {
          await databaseService.add('agents', agent);
        }
      }
    } catch (error) {
      console.error('Failed to load from database:', error);
      this.agents = [...this.defaultAgents];
      // Initialize other arrays to empty if load fails after DB init
      this.prompts = [];
      this.results = [];
      this.nodeTemplates = [];
      this.chainFlows = [];
      this.communityVaultItems = [];
    }
  }

  // ... Agent methods (getAgents, addAgent, etc.) ...
  // ... Prompt methods (getPrompts, addPrompt, etc.) ...
  // ... Result methods (getResults, addResult, etc.) ...
  // ... NodeTemplate methods (getNodeTemplates, addNodeTemplate, etc.) ...
  // ... ChainFlow methods (getChainFlows, addChainFlow, etc.) ...

  // Agent methods (getAgents, addAgent, updateAgent, deleteAgent, toggleAgent)
  getAgents(): Agent[] { return [...this.agents]; }
  getEnabledAgents(): Agent[] { return this.agents.filter(agent => agent.enabled); }
  async addAgent(agent: Omit<Agent, 'id'>): Promise<Agent> {
    const newAgent: Agent = { ...agent, id: crypto.randomUUID() };
    this.agents.push(newAgent);
    try { await databaseService.add('agents', newAgent); }
    catch (error) { console.error('Failed to save agent:', error); /* consider revert */ }
    this.notifyListeners(); return newAgent;
  }
  async updateAgent(id: string, updates: Partial<Agent>): Promise<void> {
    const index = this.agents.findIndex(a => a.id === id);
    if (index !== -1) {
      const original = this.agents[index];
      this.agents[index] = { ...original, ...updates };
      try { await databaseService.update('agents', this.agents[index]); }
      catch (error) { console.error('Failed to update agent:', error); this.agents[index] = original; }
      this.notifyListeners();
    }
  }
  async deleteAgent(id: string): Promise<void> {
    this.agents = this.agents.filter(a => a.id !== id);
    try { await databaseService.delete('agents', id); }
    catch (error) { console.error('Failed to delete agent:', error); /* consider re-add or error state */ }
    this.notifyListeners();
  }
  toggleAgent(id: string): void { /* ... */ }


  // Prompt methods (getPrompts, addPrompt, updatePrompt, forkPrompt, deletePrompt)
  getPrompts(): PromptVariant[] { return [...this.prompts]; }
  async addPrompt(promptData: Omit<PromptVariant, 'id' | 'version' | 'lineageId' | 'createdAt' | 'updatedAt'>): Promise<PromptVariant> {
    const now = new Date();
    const newId = crypto.randomUUID();
    const newPrompt: PromptVariant = { ...promptData, id: newId, parentId: undefined, version: 1, lineageId: newId, createdAt: now, updatedAt: now };
    this.prompts.push(newPrompt);
    try { await databaseService.add('prompts', newPrompt); }
    catch (error) { console.error('Failed to save prompt:', error); this.prompts = this.prompts.filter(p => p.id !== newId); throw error; }
    this.notifyListeners(); return newPrompt;
  }
  async updatePrompt(id: string, updates: Partial<PromptVariant>): Promise<void> {
    const index = this.prompts.findIndex(p => p.id === id);
    if (index !== -1) {
      const original = this.prompts[index];
      this.prompts[index] = { ...original, ...updates, updatedAt: new Date() };
      try { await databaseService.update('prompts', this.prompts[index]); }
      catch (error) { console.error('Failed to update prompt:', error); this.prompts[index] = original; }
      this.notifyListeners();
    }
  }
  async forkPrompt(originalPromptId: string, newName?: string): Promise<PromptVariant> {
    const originalPrompt = this.prompts.find(p => p.id === originalPromptId);
    if (!originalPrompt) throw new Error(`Prompt with ID ${originalPromptId} not found.`);
    const { id, parentId, version, name, createdAt, updatedAt, ...originalDataToCopy } = originalPrompt;
    const forkedId = crypto.randomUUID();
    const forkedPrompt: PromptVariant = {
      ...JSON.parse(JSON.stringify(originalDataToCopy)), id: forkedId, name: newName || `${originalPrompt.name} - Fork`,
      parentId: originalPrompt.id, version: 1, lineageId: originalPrompt.lineageId || originalPrompt.id,
      createdAt: new Date(), updatedAt: new Date(),
    };
    this.prompts.push(forkedPrompt);
    try { await databaseService.add('prompts', forkedPrompt); }
    catch (error) { console.error('Failed to save forked prompt:', error); this.prompts = this.prompts.filter(p => p.id !== forkedId); throw error; }
    this.notifyListeners(); return forkedPrompt;
  }
  async deletePrompt(id: string): Promise<void> {
    this.prompts = this.prompts.filter(p => p.id !== id);
    try { await databaseService.delete('prompts', id); }
    catch (error) { console.error('Failed to delete prompt:', error); }
    this.notifyListeners();
  }

  // Result methods (getResults, addResult, clearResults)
  getResults(): TestResult[] { return [...this.results].sort((a,b) => b.timestamp.getTime() - a.timestamp.getTime()); }
  async addResult(resultData: Omit<TestResult, 'id'>): Promise<TestResult> {
    const newResultWithId: TestResult = { ...resultData, id: crypto.randomUUID() };
    this.results.unshift(newResultWithId);
    try { await databaseService.add('results', newResultWithId); }
    catch (error) { console.error('Failed to save result:', error); }
    this.notifyListeners(); return newResultWithId;
  }
  async clearResults(): Promise<void> { /* ... */ }

  // NodeTemplate methods (getNodeTemplates, addNodeTemplate, deleteNodeTemplate)
  getNodeTemplates(): NodeTemplate[] { return [...this.nodeTemplates]; }
  async addNodeTemplate(templateData: Omit<NodeTemplate, 'id' | 'createdAt'>): Promise<NodeTemplate> {
    const newTemplate: NodeTemplate = { ...templateData, id: crypto.randomUUID(), createdAt: new Date() };
    this.nodeTemplates.push(newTemplate);
    try { await databaseService.add('nodeTemplates', newTemplate); }
    catch (error) { console.error('Failed to save template:', error); this.nodeTemplates = this.nodeTemplates.filter(t => t.id !== newTemplate.id); throw error; }
    this.notifyListeners(); return newTemplate;
  }
  async deleteNodeTemplate(templateId: string): Promise<void> {
    this.nodeTemplates = this.nodeTemplates.filter(t => t.id !== templateId);
    try { await databaseService.delete('nodeTemplates', templateId); }
    catch (error) { console.error('Failed to delete template:', error); }
    this.notifyListeners();
  }

  // ChainFlow methods (getChainFlows, addChainFlow, updateChainFlow, deleteChainFlow)
  getChainFlows(): ChainFlow[] { return [...this.chainFlows]; }
  async addChainFlow(flowData: Omit<ChainFlow, 'id' | 'metadata'> & { metadata?: Partial<ChainFlow['metadata']> }): Promise<ChainFlow> {
    const now = new Date();
    const newFlow: ChainFlow = { nodes: [], connections: [], ...flowData, id: crypto.randomUUID(), metadata: { version: '1.0.0', tags: [], ...flowData.metadata, createdAt: now, updatedAt: now } };
    this.chainFlows.push(newFlow);
    try { await databaseService.add('chainFlows', newFlow); }
    catch (error) { console.error('Failed to save flow:', error); this.chainFlows = this.chainFlows.filter(f => f.id !== newFlow.id); throw error; }
    this.notifyListeners(); return newFlow;
  }
  async updateChainFlow(flowId: string, updates: Partial<Omit<ChainFlow, 'id' | 'metadata'>> & { metadata?: Partial<ChainFlow['metadata']> }): Promise<ChainFlow | undefined> {
    const flowIndex = this.chainFlows.findIndex(f => f.id === flowId);
    if (flowIndex === -1) return undefined;
    const originalFlow = this.chainFlows[flowIndex];
    const updatedFlow: ChainFlow = { ...originalFlow, ...updates, metadata: { ...originalFlow.metadata, ...updates.metadata, updatedAt: new Date() } };
    this.chainFlows[flowIndex] = updatedFlow;
    try { await databaseService.update('chainFlows', updatedFlow); }
    catch (error) { console.error('Failed to update flow:', error); this.chainFlows[flowIndex] = originalFlow; throw error; }
    this.notifyListeners(); return updatedFlow;
  }
  async deleteChainFlow(flowId: string): Promise<void> {
    this.chainFlows = this.chainFlows.filter(f => f.id !== flowId);
    try { await databaseService.delete('chainFlows', flowId); }
    catch (error) { console.error('Failed to delete flow:', error); }
    this.notifyListeners();
  }

  // Community Vault Methods
  getCommunityVaultItems(): VaultItemSummary[] {
    return [...this.communityVaultItems].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  async publishItemToVault(itemData: Omit<VaultItemSummary, 'vaultId' | 'createdAt'>): Promise<VaultItemSummary> {
    const newVaultItem: VaultItemSummary = { ...itemData, vaultId: crypto.randomUUID(), createdAt: new Date(), upvotes: itemData.upvotes || 0, forksCount: itemData.forksCount || 0 };
    this.communityVaultItems.push(newVaultItem);
    try { await databaseService.add('communityVaultItems', newVaultItem); }
    catch (error) { console.error('Failed to save vault item:', error); this.communityVaultItems = this.communityVaultItems.filter(item => item.vaultId !== newVaultItem.vaultId); throw error; }
    this.notifyListeners(); return newVaultItem;
  }
  async unpublishItemFromVault(vaultId: string): Promise<void> {
    this.communityVaultItems = this.communityVaultItems.filter(item => item.vaultId !== vaultId);
    try { await databaseService.delete('communityVaultItems', vaultId); }
    catch (error) { console.error('Failed to delete vault item:', error); }
    this.notifyListeners();
  }

  async importPromptXData(data: ImportedPromptStudioData): Promise<{ successCount: number; errorCount: number; errors: {type: string, id?: string | number, message: string}[] }> {
    let successCount = 0;
    let errorCount = 0;
    const errorsReport: {type: string, id?: string | number, message: string}[] = [];

    const importItems = async <T extends {id?: any}, U extends Omit<T, 'id'>>(
      items: T[] | undefined,
      addFunction: (itemData: U | T) => Promise<T | void>,
      itemTypeName: string,
      isResult?: boolean
    ) => {
      if (!items) return;
      for (const item of items) {
        try {
          if (isResult && item.id) { // Results have IDs regenerated by addResult
             const { id, ...itemData } = item as TestResult;
             await (addFunction as any)(itemData);
          } else {
            // For other types, addPrompt etc. expect Omit<Type, 'id' ...> or handle ID internally
            // This assumes add functions are robust enough or data is pre-processed if needed.
            // For simplicity, we pass the full item; add functions should correctly extract/ignore ID.
            await addFunction(item);
          }
          successCount++;
        } catch (e: any) {
          errorCount++;
          errorsReport.push({ type: itemTypeName, id: item.id || 'unknown_id', message: e.message });
          console.error(`Error importing ${itemTypeName} ${item.id || ''}:`, e);
        }
      }
    };

    // Import order can be important if there are dependencies. For now, simple sequential.
    // The `add` methods for prompts, agents, etc., handle ID generation.
    await importItems<PromptVariant, Omit<PromptVariant, 'id' | 'version' | 'lineageId' | 'createdAt' | 'updatedAt'>>(data.prompts, this.addPrompt.bind(this), 'PromptVariant');
    await importItems<Agent, Omit<Agent, 'id'>>(data.agents, this.addAgent.bind(this), 'Agent');

    // Special handling for results as addResult takes Omit<TestResult, 'id'> and generates a new ID
    if (data.results) {
      for (const result of data.results) {
        try {
          const { id, ...resultData } = result; // Store original ID for error reporting if needed
          await this.addResult(resultData); // addResult will generate a new ID
          successCount++;
        } catch (e:any) {
          errorCount++;
          errorsReport.push({ type: 'TestResult', id: result.id, message: e.message });
        }
      }
    }

    await importItems<NodeTemplate, Omit<NodeTemplate, 'id' | 'createdAt'>>(data.nodeTemplates, this.addNodeTemplate.bind(this), 'NodeTemplate');
    await importItems<ChainFlow, Omit<ChainFlow, 'id' | 'metadata'> & { metadata?: Partial<ChainFlow['metadata']> }>(data.flows, this.addChainFlow.bind(this), 'ChainFlow');

    // communityVaultItems are not typically imported directly via general .promptx import,
    // as they represent a separate curated/published list. Publishing is a distinct user action.

    this.notifyListeners(); // Notify once after all additions
    console.log(`[promptStore] Import from .promptx processed. Success: ${successCount}, Errors: ${errorCount}`, errorsReport);
    return { successCount, errorCount, errors: errorsReport };
  }


  // ... runIntegratedTest, backup/restore, export/import, getStats, cleanup ... (ensure these use new arrays)
  // These methods need to be checked to ensure they correctly handle all new data types (nodeTemplates, chainFlows, communityVaultItems)
  // For example, runIntegratedTest uses this.prompts and this.agents.
  // Backup/restore, export/import are handled by databaseService which was updated.
  // getStats in databaseService was updated. promptStore.getStats calls it.
  // cleanup in databaseService would need updating if vault items or templates have timestamps to clean.

  // Subscription methods
  subscribe(listener: () => void): () => void { /* ... */ }
  private notifyListeners(): void { /* ... */ }
  getCompatibleAgents(promptId: string): Agent[] { /* ... */ } // This is fine
  async runIntegratedTest( /* ... */ ): Promise<TestResult> { /* ... This was updated ... */ return {} as TestResult; } // Placeholder for brevity
  async createBackup(name: string): Promise<string> { return databaseService.createBackup(name); }
  async restoreBackup(backupId: string): Promise<void> { await databaseService.restoreBackup(backupId); await this.loadFromDatabase(); }
  async getBackups(): Promise<any[]> { return databaseService.getBackups(); }
  async deleteBackup(backupId: string): Promise<void> { return databaseService.deleteBackup(backupId); }
  async exportData(): Promise<any> { return databaseService.exportData(); }
  async importData(data: any): Promise<void> { await databaseService.importData(data); await this.loadFromDatabase();}
  async getStats(): Promise<any> { try { return await databaseService.getStats(); } catch(e) { /* basic fallback */ return {}; } }
  async cleanup(daysToKeep: number = 30): Promise<void> { await databaseService.cleanup(daysToKeep); await this.loadFromDatabase(); }
}

export const promptStore = new PromptStore();
export type { Agent, PromptVariant, TestResult, NodeTemplate, ChainFlow, VaultItemSummary };
