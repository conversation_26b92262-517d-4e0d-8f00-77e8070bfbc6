
import { PromptVariant } from '@/store/promptStore';

export interface ForkableResult {
  id: string;
  type: string;
  content: any;
}

export interface ForkConfiguration {
  name: string;
  purpose: string;
  modifications: any;
  preserveOriginal: boolean;
  autoTest: boolean;
}

export interface ForkResult {
  success: boolean;
  variant?: PromptVariant;
  error?: string;
  metadata?: any;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
}

class LiveForkingService {
  /**
   * Extract forkable content from a result
   */
  extractForkableContent(result: any): ForkableResult | null {
    if (!result) return null;

    try {
      return {
        id: result.id || crypto.randomUUID(),
        type: result.type || 'unknown',
        content: {
          prompt: result.data?.prompt || result.prompt || '',
          systemPrompt: result.data?.systemPrompt || result.systemPrompt || '',
          variables: result.data?.variables || result.variables || {},
          response: result.data?.response || result.response || '',
          score: result.scores || result.score || {},
          metadata: result.data?.metadata || result.metadata || {}
        }
      };
    } catch (error) {
      console.error('Failed to extract forkable content:', error);
      return null;
    }
  }

  /**
   * Generate a default name for the forked variant
   */
  generateDefaultForkName(source: ForkableResult): string {
    return `Fork of ${source.type} ${source.id}`;
  }

  /**
   * Generate a default purpose for the forked variant
   */
  generateDefaultPurpose(source: ForkableResult): string {
    return `Exploring variations of ${source.type} ${source.id}`;
  }

  /**
   * Generate suggested modifications based on the source content
   */
  generateSuggestedModifications(source: ForkableResult): any {
    const suggestions: any = {};

    if (source.type === 'prompt_test') {
      if (source.content.score && source.content.score.fidelity < 0.7) {
        suggestions.prompt = 'Improve clarity and reduce ambiguity';
      }
    }

    return suggestions;
  }

  /**
   * Validate the fork configuration
   */
  validateForkConfiguration(config: ForkConfiguration): ValidationResult {
    const errors: string[] = [];

    if (!config.name || config.name.length < 3) {
      errors.push('Name must be at least 3 characters long');
    }

    if (!config.purpose || config.purpose.length < 10) {
      errors.push('Purpose must be at least 10 characters long');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Fork the content to a new prompt variant
   */
  async forkToVariant(
    source: ForkableResult,
    config: ForkConfiguration,
    addPrompt: (prompt: Omit<PromptVariant, 'id'>) => Promise<PromptVariant>
  ): Promise<ForkResult> {
    try {
      // Validate configuration
      const validation = this.validateForkConfiguration(config);
      if (!validation.valid) {
        return {
          success: false,
          error: `Invalid configuration: ${validation.errors.join(', ')}`
        };
      }

      // Create the new prompt variant
      const newVariant: Omit<PromptVariant, 'id'> = {
        name: config.name,
        prompt: config.modifications.prompt || source.content.prompt || '',
        systemPrompt: config.modifications.systemPrompt || source.content.systemPrompt,
        variables: config.modifications.variables || source.content.variables || {},
        purpose: config.purpose,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Add to store
      const createdVariant = await addPrompt(newVariant);

      // Preserve original if requested
      if (config.preserveOriginal) {
        // Original preservation logic would go here
        // For now, we'll just log it
        console.log('Original result preserved:', source);
      }

      return {
        success: true,
        variant: createdVariant,
        metadata: {
          sourceType: source.type,
          sourceId: source.id,
          forkTimestamp: new Date(),
          preservedOriginal: config.preserveOriginal,
          autoTested: config.autoTest
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

export const liveForkingService = new LiveForkingService();
