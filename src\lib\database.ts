import { PromptVariant, Agent, NodeTemplate, VaultItemSummary } from '@/store/promptStore'; // Added VaultItemSummary
import type { ChainFlow } from './chain-linker';

export interface DatabaseSchema {
  prompts: PromptVariant;
  agents: Agent;
  results: any;
  settings: {
    id: string;
    key: string;
    value: any;
    updatedAt: Date;
  };
  backups: {
    id: string;
    name: string;
    data: any;
    createdAt: Date;
    size: number;
  };
  nodeTemplates: NodeTemplate;
  chainFlows: ChainFlow;
  communityVaultItems: VaultItemSummary; // Added for Vault items
}

class DatabaseService {
  private db: IDBDatabase | null = null;
  private readonly dbName = 'PromptStudioDB';
  private readonly version = 1;

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if IndexedDB is available
      if (!window.indexedDB) {
        reject(new Error('IndexedDB not supported'));
        return;
      }

      const request = indexedDB.open(this.dbName, this.version);

      // Set a timeout to prevent hanging
      const timeout = setTimeout(() => {
        reject(new Error('Database initialization timeout'));
      }, 3000);

      request.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('Failed to open database'));
      };

      request.onsuccess = () => {
        clearTimeout(timeout);
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        try {
          const db = (event.target as IDBOpenDBRequest).result;
          this.createStores(db);
        } catch (error) {
          clearTimeout(timeout);
          reject(new Error('Failed to create database stores'));
        }
      };
    });
  }

  private createStores(db: IDBDatabase): void {
    // Prompts store
    if (!db.objectStoreNames.contains('prompts')) {
      const promptStore = db.createObjectStore('prompts', { keyPath: 'id' });
      promptStore.createIndex('name', 'name', { unique: false });
      promptStore.createIndex('tags', 'tags', { unique: false, multiEntry: true });
    }

    // Agents store
    if (!db.objectStoreNames.contains('agents')) {
      const agentStore = db.createObjectStore('agents', { keyPath: 'id' });
      agentStore.createIndex('name', 'name', { unique: false });
      agentStore.createIndex('role', 'role', { unique: false });
    }

    // Results store
    if (!db.objectStoreNames.contains('results')) {
      const resultStore = db.createObjectStore('results', { keyPath: 'id', autoIncrement: true });
      resultStore.createIndex('timestamp', 'timestamp', { unique: false });
      resultStore.createIndex('type', 'type', { unique: false });
    }

    // Settings store
    if (!db.objectStoreNames.contains('settings')) {
      const settingsStore = db.createObjectStore('settings', { keyPath: 'id' });
      settingsStore.createIndex('key', 'key', { unique: true });
    }

    // Backups store
    if (!db.objectStoreNames.contains('backups')) {
      const backupStore = db.createObjectStore('backups', { keyPath: 'id' });
      backupStore.createIndex('createdAt', 'createdAt', { unique: false });
    }

    // NodeTemplates store
    if (!db.objectStoreNames.contains('nodeTemplates')) {
      const templateStore = db.createObjectStore('nodeTemplates', { keyPath: 'id' });
      templateStore.createIndex('name', 'name', { unique: false });
      templateStore.createIndex('nodeType', 'nodeType', { unique: false });
      templateStore.createIndex('tags', 'tags', { unique: false, multiEntry: true });
    }

    // ChainFlows store
    if (!db.objectStoreNames.contains('chainFlows')) {
      const flowStore = db.createObjectStore('chainFlows', { keyPath: 'id' });
      flowStore.createIndex('name', 'name', { unique: false });
      // flowStore.createIndex('tags', 'metadata.tags', { unique: false, multiEntry: true });
    }

    // CommunityVaultItems store
    if (!db.objectStoreNames.contains('communityVaultItems')) {
      const vaultStore = db.createObjectStore('communityVaultItems', { keyPath: 'vaultId' }); // Use vaultId as keyPath
      vaultStore.createIndex('type', 'type', { unique: false });
      vaultStore.createIndex('name', 'name', { unique: false });
      vaultStore.createIndex('tags', 'tags', { unique: false, multiEntry: true });
      vaultStore.createIndex('createdAt', 'createdAt', { unique: false });
    }
  }

  async add<T extends keyof DatabaseSchema>(
    storeName: T,
    data: DatabaseSchema[T]
  ): Promise<void> {
    if (!this.db) {
      // console.error(`Attempted to call 'add' on ${storeName} but DB not initialized.`);
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error(`Failed to add to ${storeName}`));
    });
  }

  async update<T extends keyof DatabaseSchema>(
    storeName: T,
    data: DatabaseSchema[T]
  ): Promise<void> {
    if (!this.db) {
      // console.error(`Attempted to call 'update' on ${storeName} but DB not initialized.`);
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error(`Failed to update ${storeName}`));
    });
  }

  async get<T extends keyof DatabaseSchema>(
    storeName: T,
    id: string
  ): Promise<DatabaseSchema[T] | undefined> {
    if (!this.db) {
      // console.error(`Attempted to call 'get' on ${storeName} but DB not initialized.`);
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error(`Failed to get from ${storeName}`));
    });
  }

  async getAll<T extends keyof DatabaseSchema>(
    storeName: T
  ): Promise<DatabaseSchema[T][]> {
    if (!this.db) {
      // console.error(`Attempted to call 'getAll' on ${storeName} but DB not initialized.`);
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error(`Failed to get all from ${storeName}`));
    });
  }

  async delete<T extends keyof DatabaseSchema>(
    storeName: T,
    id: string
  ): Promise<void> {
    if (!this.db) {
      // console.error(`Attempted to call 'delete' on ${storeName} but DB not initialized.`);
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error(`Failed to delete from ${storeName}`));
    });
  }

  async clear<T extends keyof DatabaseSchema>(storeName: T): Promise<void> {
    if (!this.db) {
      // console.error(`Attempted to call 'clear' on ${storeName} but DB not initialized.`);
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error(`Failed to clear ${storeName}`));
    });
  }

  async query<T extends keyof DatabaseSchema>(
    storeName: T,
    indexName: string,
    value: any
  ): Promise<DatabaseSchema[T][]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(value);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error(`Failed to query ${storeName} by ${indexName}`));
    });
  }

  async count<T extends keyof DatabaseSchema>(storeName: T): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.count();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error(`Failed to count ${storeName}`));
    });
  }

  // Backup and restore functionality
  async createBackup(name: string): Promise<string> {
    if (!this.db) throw new Error('Database not initialized');

    const backup = {
      id: `backup_${Date.now()}`,
      name,
      createdAt: new Date(),
      data: {
        prompts: await this.getAll('prompts'),
        agents: await this.getAll('agents'),
        results: await this.getAll('results'),
        settings: await this.getAll('settings'),
        nodeTemplates: await this.getAll('nodeTemplates'),
        chainFlows: await this.getAll('chainFlows'),
        communityVaultItems: await this.getAll('communityVaultItems') // Added vault items
      },
      size: 0
    };

    // Calculate backup size
    backup.size = JSON.stringify(backup.data).length;

    await this.add('backups', backup);
    return backup.id;
  }

  async restoreBackup(backupId: string): Promise<void> {
    const backup = await this.get('backups', backupId);
    if (!backup) throw new Error('Backup not found');

    // Clear existing data
    await this.clear('prompts');
    await this.clear('agents');
    await this.clear('results');
    await this.clear('settings');
    await this.clear('nodeTemplates');
    await this.clear('chainFlows');
    await this.clear('communityVaultItems'); // Clear vault items

    // Restore data
    const data = backup.data as any;
    
    for (const prompt of data.prompts || []) {
      await this.add('prompts', prompt);
    }
    
    for (const agent of data.agents || []) {
      await this.add('agents', agent);
    }
    
    for (const result of data.results || []) {
      await this.add('results', result);
    }
    
    for (const setting of data.settings || []) {
      await this.add('settings', setting);
    }

    for (const template of data.nodeTemplates || []) {
      await this.add('nodeTemplates', template);
    }

    for (const flow of data.chainFlows || []) {
      await this.add('chainFlows', flow);
    }

    for (const item of data.communityVaultItems || []) { // Added vault items
      await this.add('communityVaultItems', item);
    }
  }

  async getBackups(): Promise<DatabaseSchema['backups'][]> {
    return this.getAll('backups');
  }

  async deleteBackup(backupId: string): Promise<void> {
    await this.delete('backups', backupId);
  }

  // Migration functionality
  async migrate(): Promise<void> {
    // Check if migration is needed
    const version = await this.getSetting('db_version');
    const currentVersion = this.version;

    if (!version || version.value < currentVersion) {
      console.log('Running database migration...');
      
      // Perform migration steps here
      await this.setSetting('db_version', currentVersion);
      
      console.log('Database migration completed');
    }
  }

  // Settings helpers
  async getSetting(key: string): Promise<DatabaseSchema['settings'] | undefined> {
    const settings = await this.query('settings', 'key', key);
    return settings[0];
  }

  async setSetting(key: string, value: any): Promise<void> {
    const setting = {
      id: `setting_${key}`,
      key,
      value,
      updatedAt: new Date()
    };
    
    const existing = await this.getSetting(key);
    if (existing) {
      await this.update('settings', setting);
    } else {
      await this.add('settings', setting);
    }
  }

  // Export/Import functionality
  async exportData(): Promise<any> {
    return {
      prompts: await this.getAll('prompts'),
      agents: await this.getAll('agents'),
      results: await this.getAll('results'),
      settings: await this.getAll('settings'),
      nodeTemplates: await this.getAll('nodeTemplates'),
      chainFlows: await this.getAll('chainFlows'),
      communityVaultItems: await this.getAll('communityVaultItems'), // Added vault items
      exportedAt: new Date().toISOString(),
      version: this.version
    };
  }

  async importData(data: any): Promise<void> {
    // Validate data structure
    if (!data.prompts || !data.agents) {
      throw new Error('Invalid import data structure');
    }

    // Create backup before import
    await this.createBackup(`Pre-import backup ${new Date().toISOString()}`);

    // Clear existing data
    await this.clear('prompts');
    await this.clear('agents');
    await this.clear('results');
    // await this.clear('settings'); // Settings are usually not cleared on general data import
    await this.clear('nodeTemplates');
    await this.clear('chainFlows');
    await this.clear('communityVaultItems'); // Clear vault items

    // Import data
    for (const prompt of data.prompts) {
      await this.add('prompts', prompt);
    }
    
    for (const agent of data.agents) {
      await this.add('agents', agent);
    }
    
    for (const result of data.results || []) {
      await this.add('results', result);
    }

    for (const template of data.nodeTemplates || []) {
      await this.add('nodeTemplates', template);
    }

    for (const flow of data.chainFlows || []) {
      await this.add('chainFlows', flow);
    }

    for (const item of data.communityVaultItems || []) { // Added vault items
      await this.add('communityVaultItems', item);
    }
  }

  // Database statistics
  async getStats(): Promise<{
    prompts: number;
    agents: number;
    results: number;
    nodeTemplates: number;
    chainFlows: number;
    communityVaultItems: number; // Added vault items
    backups: number;
    totalSize: number;
  }> {
    const [prompts, agents, results, nodeTemplates, chainFlows, communityVaultItems, backups] = await Promise.all([
      this.count('prompts'),
      this.count('agents'),
      this.count('results'),
      this.count('nodeTemplates'),
      this.count('chainFlows'),
      this.count('communityVaultItems'), // Added vault items
      this.count('backups')
    ]);

    // Estimate total size (rough calculation)
    const allData = await this.exportData();
    const totalSize = JSON.stringify(allData).length;

    return {
      prompts,
      agents,
      results,
      nodeTemplates,
      chainFlows,
      communityVaultItems, // Added vault items
      backups,
      totalSize
    };
  }

  // Cleanup old data
  async cleanup(daysToKeep: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    // Clean up old results
    const allResults = await this.getAll('results');
    const oldResults = allResults.filter(result => 
      new Date(result.timestamp) < cutoffDate
    );

    for (const result of oldResults) {
      await this.delete('results', result.id);
    }

    // Clean up old backups (keep last 5)
    const allBackups = await this.getAll('backups');
    const sortedBackups = allBackups.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    const backupsToDelete = sortedBackups.slice(5);
    for (const backup of backupsToDelete) {
      await this.delete('backups', backup.id);
    }
  }

  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

export const databaseService = new DatabaseService();
