
import { aiService, AI_PROVIDERS, AIRequest, AIResponse } from '../ai-service';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Mock fetch
global.fetch = vi.fn();

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    length: 0,
    key: (index: number) => null,
  };
})();

// Store original window properties
const originalLocalStorage = window.localStorage;
const originalImportMetaEnv = import.meta.env;

describe('AIService', () => {
  let mockFetch: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.clear();

    Object.defineProperty(window, 'localStorage', { value: mockLocalStorage, writable: true });

    vi.stubGlobal('import.meta.env', {
      VITE_OPENAI_API_KEY: '',
      VITE_ANTHROPIC_API_KEY: '',
      VITE_GOOGLE_API_KEY: '',
      VITE_MISTRAL_API_KEY: '',
      VITE_COHERE_API_KEY: '', // Added for Cohere
      VITE_DEFAULT_AI_PROVIDER: '',
      VITE_DEFAULT_MODEL: '',
      VITE_DEFAULT_TEMPERATURE: '',
      VITE_DEFAULT_MAX_TOKENS: '',
      VITE_API_TIMEOUT: '30000', VITE_MAX_RETRIES: '3',
    });

    aiService.refreshConfiguration();
    mockFetch = global.fetch as ReturnType<typeof vi.fn>;
  });

  afterEach(() => {
    Object.defineProperty(window, 'localStorage', { value: originalLocalStorage });
    vi.stubGlobal('import.meta.env', originalImportMetaEnv);
  });

  describe('Configuration Loading', () => {
    it('should load API key from localStorage if available (OpenAI)', () => {
      mockLocalStorage.setItem('ai-settings', JSON.stringify({ openaiApiKey: 'ls_openai_key' }));
      aiService.refreshConfiguration();
      expect(aiService.isConfigured('openai')).toBe(true);
    });

    it('should load API key from import.meta.env if not in localStorage (OpenAI)', () => {
      vi.stubGlobal('import.meta.env', { ...import.meta.env, VITE_OPENAI_API_KEY: 'env_openai_key' });
      aiService.refreshConfiguration();
      expect(aiService.isConfigured('openai')).toBe(true);
    });

    it('should use hardcoded defaults if settings are missing from both localStorage and env', () => {
      aiService.refreshConfiguration();
      const defaults = aiService.getDefaultSettings();
      expect(defaults.provider).toBe('openai');
      expect(defaults.model).toBe('gpt-4o');
      expect(defaults.temperature).toBe(0.7);
      expect(defaults.maxTokens).toBe(2048);
    });
  });

  describe('isConfigured', () => {
    it('should return true if API key is set for a provider, false otherwise', () => {
      const cleanEnv = {
        VITE_OPENAI_API_KEY: '', VITE_ANTHROPIC_API_KEY: '', VITE_GOOGLE_API_KEY: '',
        VITE_MISTRAL_API_KEY: '', VITE_COHERE_API_KEY: '',
        VITE_DEFAULT_AI_PROVIDER: 'openai', VITE_DEFAULT_MODEL: 'gpt-4o',
        VITE_DEFAULT_TEMPERATURE: '0.7', VITE_DEFAULT_MAX_TOKENS: '2048', // Matching service default
        VITE_API_TIMEOUT: '30000', VITE_MAX_RETRIES: '3',
      };

      // Test 1: Only OpenAI key in localStorage, env keys are blank
      // Ensure a very clean environment for this test by explicitly setting all relevant env vars
      // to an object that definitely has API keys as empty or undefined.
      const testSpecificEnv = {
        VITE_DEFAULT_AI_PROVIDER: 'openai', VITE_DEFAULT_MODEL: 'gpt-4o',
        VITE_DEFAULT_TEMPERATURE: '0.7', VITE_DEFAULT_MAX_TOKENS: '2048',
        VITE_API_TIMEOUT: '30000', VITE_MAX_RETRIES: '3',
        VITE_OPENAI_API_KEY: '', VITE_ANTHROPIC_API_KEY: '',
        VITE_GOOGLE_API_KEY: '', VITE_MISTRAL_API_KEY: '', VITE_COHERE_API_KEY: ''
      };
      vi.stubGlobal('import.meta.env', testSpecificEnv);

      mockLocalStorage.clear();
      mockLocalStorage.setItem('ai-settings', JSON.stringify({ openaiApiKey: 'ls_openai_key' }));
      aiService.refreshConfiguration();

      expect(aiService.isConfigured('openai')).toBe(true);
      expect(aiService.isConfigured('anthropic')).toBe(false); // This is the one that was failing
      expect(aiService.isConfigured('google')).toBe(false);
      expect(aiService.isConfigured('mistral')).toBe(false);
      expect(aiService.isConfigured('cohere')).toBe(false);

      // Test 2: No localStorage, only Anthropic key in env
      mockLocalStorage.clear();
      vi.stubGlobal('import.meta.env', { ...testSpecificEnv, VITE_ANTHROPIC_API_KEY: 'env_anthropic_key' });
      aiService.refreshConfiguration();

      expect(aiService.isConfigured('openai')).toBe(false);
      expect(aiService.isConfigured('anthropic')).toBe(true);
      expect(aiService.isConfigured('google')).toBe(false);

      // Test 3: Reset to all blank from both sources
      mockLocalStorage.clear();
      vi.stubGlobal('import.meta.env', { ...testSpecificEnv }); // All API keys are ''
      aiService.refreshConfiguration();
      expect(aiService.isConfigured('openai')).toBe(false);
      expect(aiService.isConfigured('anthropic')).toBe(false);
    });
  });

  const createMockRequest = (provider: string, model: string, content: string): AIRequest => ({
    provider, model, messages: [{ role: 'user', content }], temperature: 0.1, maxTokens: 10
  });

  describe('generateResponse with OpenAI', () => {
    const provider = 'openai';
    const apiKey = 'test_openai_key';
    const model = 'gpt-4o';

    beforeEach(() => {
      mockLocalStorage.setItem('ai-settings', JSON.stringify({ openaiApiKey: apiKey }));
      aiService.refreshConfiguration();
    });

    it('should call OpenAI API with correct parameters and return formatted response', async () => {
      const mockApiResponse = {
        choices: [{ message: { content: 'OpenAI test response' } }],
        usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 },
      };
      mockFetch.mockResolvedValue({ ok: true, json: async () => mockApiResponse });

      const request = createMockRequest(provider, model, 'Hello OpenAI');
      const response = await aiService.generateResponse(request);

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${apiKey}` },
          body: JSON.stringify({
            model: request.model, messages: request.messages,
            temperature: request.temperature, max_tokens: request.maxTokens, stream: false
          }),
        })
      );
      expect(response.content).toBe('OpenAI test response');
      expect(response.usage).toEqual({ promptTokens: 10, completionTokens: 20, totalTokens: 30 });
      expect(response.provider).toBe(provider);
    });

    it('should handle OpenAI API error', async () => {
      mockFetch.mockResolvedValue({
        ok: false, status: 400, statusText: 'Bad Request',
        json: async () => ({ error: { message: 'OpenAI specific error' } })
      });
      const request = createMockRequest(provider, model, 'trigger error');
      await expect(aiService.generateResponse(request)).rejects.toThrow('OpenAI API error: OpenAI specific error');
    });
  });
});
