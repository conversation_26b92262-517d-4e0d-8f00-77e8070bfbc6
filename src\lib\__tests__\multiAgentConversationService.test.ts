
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { multiAgentConversationService, MultiAgentSimulationRequest, ConversationTurn } from '../multiAgentConversationService';
import { aiService, AIRequest, AIResponse } from '../ai-service';
import { Agent } from '@/store/promptStore';

// Mock aiService
vi.mock('../ai-service', () => ({
  aiService: {
    generateResponse: vi.fn(),
    getDefaultSettings: vi.fn(() => ({ 
        provider: 'openai', model: 'gpt-4', temperature: 0.7, maxTokens: 1024
    }))
  }
}));

const mockAgents: Agent[] = [
  { id: 'agent1', name: '<PERSON>', role: 'Developer', systemPrompt: 'You are <PERSON>, a dev.', personality: 'analytical', expertise: ['coding'], enabled: true, modelConfig: { provider: 'openai', model: 'gpt-4', temperature: 0.7, maxTokens: 100 } },
  { id: 'agent2', name: '<PERSON>', role: 'Designer', systemPrompt: 'You are <PERSON>, a designer.', personality: 'creative', expertise: ['ux'], enabled: true, modelConfig: { provider: 'openai', model: 'gpt-3.5-turbo', temperature: 0.8, maxTokens: 150 } },
];

const mockDisabledAgent: Agent = { id: 'agent3', name: 'Charlie', role: 'QA', systemPrompt: 'You are Charlie, QA.', personality: 'meticulous', expertise: ['testing'], enabled: false, modelConfig: { provider: 'openai', model: 'gpt-4', temperature: 0.7, maxTokens: 100 } };

describe('MultiAgentConversationService', () => {
  let service: typeof multiAgentConversationService;

  beforeEach(() => {
    service = multiAgentConversationService;
    vi.clearAllMocks();
  });

  describe('runSimulation', () => {
    const baseRequest: MultiAgentSimulationRequest = {
      participatingAgents: mockAgents,
      initialScenario: 'Discuss the new feature X.',
      maxTurns: 2,
      interactionPolicy: 'round-robin',
    };

    it('should throw an error if no active agents are provided', async () => {
      const requestWithNoActiveAgents = { ...baseRequest, participatingAgents: [mockDisabledAgent] };
      await expect(service.runSimulation(requestWithNoActiveAgents))
        .rejects.toThrow("No active agents selected for the simulation.");
    });

    it('should run simulation for the specified number of turns (maxTurns * num_agents)', async () => {
      (aiService.generateResponse as ReturnType<typeof vi.fn>).mockImplementation(async (req: AIRequest) => ({
        content: `Response from ${req.messages.find(m=>m.role==='system')?.content?.split(',')[0]}`,
        usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 },
        model: req.model, provider: req.provider, timestamp: new Date()
      }));

      const result = await service.runSimulation(baseRequest);

      expect(result.conversationHistory.length).toBe(1 + baseRequest.maxTurns * mockAgents.length);
      expect(result.metadata.totalTurns).toBe(baseRequest.maxTurns * mockAgents.length);
      expect(aiService.generateResponse).toHaveBeenCalledTimes(baseRequest.maxTurns * mockAgents.length);
    });

    it('should select agents in round-robin order', async () => {
      const responses: string[] = [];
      (aiService.generateResponse as ReturnType<typeof vi.fn>).mockImplementation(async (req: AIRequest) => {
        const agentName = req.messages.find(m=>m.role==='system')?.content?.match(/You are (\w+)/)?.[1] || 'UnknownAgent';
        responses.push(agentName);
        return {
          content: `Response from ${agentName}`,
          usage: { totalTokens: 1 }, model: 'test', provider: 'test', timestamp: new Date()
        };
      });

      await service.runSimulation(baseRequest);

      expect(responses[0]).toBe('Alice');
      expect(responses[1]).toBe('Bob');
      expect(responses[2]).toBe('Alice');
      expect(responses[3]).toBe('Bob');
    });

    it('should correctly build conversation history', async () => {
      let callCount = 0;
      (aiService.generateResponse as ReturnType<typeof vi.fn>).mockImplementation(async (req: AIRequest) => {
        callCount++;
        const agentName = req.messages.find(m=>m.role==='system')?.content?.match(/You are (\w+)/)?.[1];
        return {
          content: `Agent ${agentName} says hello ${callCount}`,
          usage: { totalTokens: 10 }, model: 'test', provider: 'test', timestamp: new Date()
        };
      });

      const result = await service.runSimulation({ ...baseRequest, maxTurns: 1 });
      expect(result.conversationHistory.length).toBe(3);

      expect(result.conversationHistory[0].agentId).toBe('system');
      expect(result.conversationHistory[0].message).toBe(baseRequest.initialScenario);

      expect(result.conversationHistory[1].agentId).toBe(mockAgents[0].id);
      expect(result.conversationHistory[1].agentName).toBe(mockAgents[0].name);
      expect(result.conversationHistory[1].message).toBe('Agent Alice says hello 1');
      expect(result.conversationHistory[1].tokenCount).toBe(10);

      expect(result.conversationHistory[2].agentId).toBe(mockAgents[1].id);
      expect(result.conversationHistory[2].agentName).toBe(mockAgents[1].name);
      expect(result.conversationHistory[2].message).toBe('Agent Bob says hello 2');
      expect(result.conversationHistory[2].tokenCount).toBe(10);
    });

    it('should accumulate total token usage correctly', async () => {
       (aiService.generateResponse as ReturnType<typeof vi.fn>)
        .mockResolvedValueOnce({ content: 'r1', usage: { totalTokens: 10 } } as AIResponse)
        .mockResolvedValueOnce({ content: 'r2', usage: { totalTokens: 15 } } as AIResponse)
        .mockResolvedValueOnce({ content: 'r3', usage: { totalTokens: 20 } } as AIResponse)
        .mockResolvedValueOnce({ content: 'r4', usage: { totalTokens: 25 } } as AIResponse);

      const result = await service.runSimulation(baseRequest);
      expect(result.metadata.totalTokens).toBe(10 + 15 + 20 + 25);
    });

    it('should handle AI service errors for an agent and continue simulation', async () => {
      (aiService.generateResponse as ReturnType<typeof vi.fn>)
        .mockResolvedValueOnce({ content: 'Alice turn 1', usage: { totalTokens: 10 } } as AIResponse)
        .mockRejectedValueOnce(new Error("AI Error for Bob"))
        .mockResolvedValueOnce({ content: 'Alice turn 2', usage: { totalTokens: 12 } } as AIResponse)
        .mockResolvedValueOnce({ content: 'Bob turn 2 (after error)', usage: { totalTokens: 13 } } as AIResponse);

      const result = await service.runSimulation(baseRequest);
      expect(result.conversationHistory.length).toBe(5);
      expect(result.conversationHistory[2].agentName).toBe('Bob');
      expect(result.conversationHistory[2].message).toContain('[Error: Could not generate response - AI Error for Bob]');
      expect(result.conversationHistory[3].message).toBe('Alice turn 2');
      expect(result.conversationHistory[4].message).toBe('Bob turn 2 (after error)');
      expect(result.metadata.totalTokens).toBe(10 + 12 + 13);
    });
  });

  describe('buildContextualPrompt', () => {
    const agent = mockAgents[0];
    const scenario = 'Discuss feature Y.';

    it('should include agent details, scenario, and indicate it is the first turn if history is just scenario', () => {
      const history: ConversationTurn[] = [{ turnNumber:0, agentId: 'system', agentName: 'Scenario', message: scenario, timestamp: new Date() }];
      const prompt = service['buildContextualPrompt'](agent, scenario, history);

      expect(prompt).toContain(`You are ${agent.name}, a ${agent.role}`);
      expect(prompt).toContain(`Initial Scenario: "${scenario}"`);
      expect(prompt).toContain("This is the beginning of the conversation.");
    });

    it('should include recent history (up to maxHistoryTurns)', () => {
      const history: ConversationTurn[] = [
        { turnNumber:0, agentId: 'system', agentName: 'Scenario', message: scenario, timestamp: new Date() },
        { turnNumber:1, agentId: 'agent1', agentName: 'Alice', message: 'Alice message 1', timestamp: new Date() },
        { turnNumber:2, agentId: 'agent2', agentName: 'Bob', message: 'Bob message 1', timestamp: new Date() },
        { turnNumber:3, agentId: 'agent1', agentName: 'Alice', message: 'Alice message 2', timestamp: new Date() },
        { turnNumber:4, agentId: 'agent2', agentName: 'Bob', message: 'Bob message 2', timestamp: new Date() },
        { turnNumber:5, agentId: 'agent1', agentName: 'Alice', message: 'Alice message 3', timestamp: new Date() },
        { turnNumber:6, agentId: 'agent2', agentName: 'Bob', message: 'Bob message 3 (current last message)', timestamp: new Date() },
      ];

      const prompt = service['buildContextualPrompt'](agent, scenario, history);

      expect(prompt).toContain('Current Conversation (last few turns):');
      expect(prompt).not.toContain('Alice message 1'); // Should be excluded with maxHistoryTurns = 5
      expect(prompt).toContain('Bob message 1');     // This should be the earliest message included
      expect(prompt).toContain('Alice message 2');
      expect(prompt).toContain('Bob message 2');
      expect(prompt).toContain('Alice message 3');
      expect(prompt).toContain('Bob message 3 (current last message)');
      expect(prompt).toContain(`It's now your turn, ${agent.name}.`);
      expect(prompt.match(/Turn \d+/g)?.length).toBeLessThanOrEqual(5);
    });

     it('should not repeat scenario in history section of the prompt', () => {
      const history: ConversationTurn[] = [
        { turnNumber:0, agentId: 'system', agentName: 'Scenario', message: scenario, timestamp: new Date() },
        { turnNumber:1, agentId: 'agent2', agentName: 'Bob', message: 'Bob initial response', timestamp: new Date() },
      ];
      const prompt = service['buildContextualPrompt'](agent, scenario, history);

      expect(prompt).toContain(`Initial Scenario: "${scenario}"`);
      const historySection = prompt.substring(prompt.indexOf("Current Conversation"));
      expect(historySection).not.toContain(`Scenario: "${scenario}"`);
      expect(historySection).toContain('Bob (Turn 1): "Bob initial response"');
    });
  });
});
