@echo off
:: Quick Start - Prompt Studio
:: Double-click this file to quickly start the development server

echo.
echo 💠 Prompt Studio - Quick Start
echo ==============================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js is installed
echo.

:: Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    echo This may take a few minutes...
    npm install --silent
    echo ✅ Dependencies installed
    echo.
)

:: Check for .env file
if not exist ".env" (
    echo 🔧 Creating environment file...
    echo # Prompt Studio Environment Configuration > .env
    echo # Add your AI API keys below >> .env
    echo. >> .env
    echo # OpenAI API Key >> .env
    echo VITE_OPENAI_API_KEY=your_openai_api_key_here >> .env
    echo. >> .env
    echo # Anthropic API Key >> .env
    echo VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here >> .env
    echo.
    echo ⚠️  IMPORTANT: Please edit .env file and add your API keys
    echo.
    echo Would you like to open the .env file now? (y/n)
    set /p OPEN_ENV=
    if /i "%OPEN_ENV%"=="y" (
        start notepad .env
        echo.
        echo Please save the .env file after adding your API keys
        echo Then press any key to continue...
        pause >nul
    )
    echo.
)

:: Start the development server
echo 🚀 Starting Prompt Studio...
echo.
echo The application will be available at: http://localhost:5173
echo Your browser should open automatically
echo.
echo Press Ctrl+C to stop the server when you're done
echo.

:: Use start to run in background and open browser
start "" "http://localhost:5173"
npm run dev
