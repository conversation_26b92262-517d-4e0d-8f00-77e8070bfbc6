import { PromptVariant } from '@/store/promptStore'; // Assuming this is the correct path after store changes
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Plus, Trash2, GitBranch } from 'lucide-react';
import { useState, useEffect } from 'react';

interface VariantEditorProps {
  variant: PromptVariant | null; // Active variant to edit
  allVariants: PromptVariant[]; // Needed for resolving parent name
  onUpdateVariant: (id: string, field: keyof PromptVariant, value: any) => void;
  // For simplicity, complex state like new variable input will be internal to this component for now
  // or we assume onUpdateVariant can handle nested field updates like 'variables.newKey'
}

export const VariantEditor = ({ variant, allVariants, onUpdateVariant }: VariantEditorProps) => {
  const [localVariant, setLocalVariant] = useState<PromptVariant | null>(null);
  const [variableInput, setVariableInput] = useState({ key: '', value: '' });
  const [newSubVariant, setNewSubVariant] = useState('');

  useEffect(() => {
    // Use a deep copy to avoid direct mutation of prop
    setLocalVariant(variant ? JSON.parse(JSON.stringify(variant)) : null);
  }, [variant]);

  if (!localVariant) {
    return (
      <div className="lg:col-span-3 flex items-center justify-center h-full">
        <p className="text-slate-400">Select a variant to edit or create a new one.</p>
      </div>
    );
  }

  const handleFieldChange = (field: keyof PromptVariant, value: any) => {
    if (localVariant) {
      // For direct properties of localVariant
      const updatedVariant = { ...localVariant, [field]: value, updatedAt: new Date() };
      setLocalVariant(updatedVariant);
      onUpdateVariant(localVariant.id, field, value); // Propagate specific field update
    }
  };

  const handleNestedFieldChange = (field: keyof PromptVariant, nestedField: string, value: any) => {
     if (localVariant && typeof (localVariant as any)[field] === 'object') {
      const updatedVariant = {
        ...localVariant,
        [field]: {
          ...(localVariant as any)[field],
          [nestedField]: value,
        },
        updatedAt: new Date(),
      };
      setLocalVariant(updatedVariant);
      onUpdateVariant(localVariant.id, field, updatedVariant[field]);
    }
  };

  const handleAddVariable = () => {
    if (!variableInput.key.trim() || !localVariant) return;
    const newVariables = {
      ...(localVariant.variables || {}),
      [variableInput.key.trim()]: variableInput.value
    };
    handleFieldChange('variables', newVariables);
    setVariableInput({ key: '', value: '' });
  };

  const handleRemoveVariable = (keyToRemove: string) => {
    if (!localVariant) return;
    const newVariables = { ...localVariant.variables };
    delete newVariables[keyToRemove];
    handleFieldChange('variables', newVariables);
  };

  const handleAddSubVariantEntry = () => {
    if (!localVariant) return;
    const updatedSubVariants = [...(localVariant.experimentalSubVariants || []), ''];
    handleFieldChange('experimentalSubVariants', updatedSubVariants);
  };

  const handleUpdateSubVariantEntry = (index: number, value: string) => {
    if (!localVariant || !localVariant.experimentalSubVariants) return;
    const updatedSubVariants = localVariant.experimentalSubVariants.map((sv, i) => i === index ? value : sv);
    handleFieldChange('experimentalSubVariants', updatedSubVariants);
  };

  const handleRemoveSubVariantEntry = (index: number) => {
    if (!localVariant || !localVariant.experimentalSubVariants) return;
    const updatedSubVariants = localVariant.experimentalSubVariants.filter((_, i) => i !== index);
    handleFieldChange('experimentalSubVariants', updatedSubVariants);
  };

  const renderPromptWithVariables = (promptText: string, variables: Record<string, string>) => {
    let rendered = promptText;
    Object.entries(variables).forEach(([key, value]) => {
      rendered = rendered.replace(new RegExp(`{${key}}`, 'g'), value ? `[${value}]` : `{${key}}`);
    });
    return rendered;
  };

  const parentName = localVariant.parentId ? (allVariants.find(p => p.id === localVariant.parentId)?.name || localVariant.parentId.substring(0,6)) : null;

  return (
    <Card className="bg-slate-800/50 border-slate-700 p-6">
      <div className="flex items-center gap-3 mb-6">
        <GitBranch className="w-5 h-5 text-blue-400" />
        <h3 className="text-xl font-semibold text-slate-200 truncate" title={localVariant.name}>{localVariant.name}</h3>
        {parentName && (
          <Badge variant="outline" className="border-yellow-500 text-yellow-300 text-xs whitespace-nowrap" title={`Parent ID: ${localVariant.parentId}`}>
            Fork of: {parentName}
          </Badge>
        )}
        <Badge variant="secondary" className="text-xs whitespace-nowrap">v{localVariant.version || 1}</Badge>
        {localVariant.lineageId && (
            <Badge variant="outline" className="text-xs border-purple-500 text-purple-300" title={`Lineage ID: ${localVariant.lineageId}`}>
                L: {localVariant.lineageId.substring(0,6)}...
            </Badge>
        )}
      </div>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-900/50">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="documentation">Documentation</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4 mt-4">
          <div>
            <Label className="text-slate-200">Variant Name</Label>
            <Input
              value={localVariant.name}
              onChange={(e) => handleFieldChange('name', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
            />
          </div>

          <div>
            <Label className="text-slate-200">System Prompt</Label>
            <Textarea
              value={localVariant.systemPrompt || ''}
              onChange={(e) => handleFieldChange('systemPrompt', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Define system behavior..."
            />
          </div>

          <div>
            <Label className="text-slate-200">Main Prompt</Label>
            <Textarea
              value={localVariant.prompt}
              onChange={(e) => handleFieldChange('prompt', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1 min-h-[120px]"
              placeholder="Use {variable_name} for dynamic content..."
            />
          </div>

          <div>
            <Label className="text-slate-200">Variables</Label>
            <div className="space-y-2 mt-1">
              {Object.entries(localVariant.variables || {}).map(([key, value]) => (
                <div key={key} className="flex items-center gap-2">
                  <Input value={key} readOnly className="bg-slate-950 border-slate-700 text-slate-400 w-1/3"/>
                  <Input
                    value={value}
                    onChange={(e) => {
                        const newVars = {...localVariant.variables, [key]: e.target.value};
                        handleFieldChange('variables', newVars);
                    }}
                    className="bg-slate-900/50 border-slate-600 text-white w-2/3"
                  />
                  <Button variant="ghost" size="icon" onClick={() => handleRemoveVariable(key)} className="text-red-400 hover:text-red-300">
                    <Trash2 className="w-4 h-4"/>
                  </Button>
                </div>
              ))}
              <div className="flex items-center gap-2">
                <Input
                    placeholder="New variable name"
                    value={variableInput.key}
                    onChange={(e) => setVariableInput(prev => ({...prev, key: e.target.value}))}
                    className="bg-slate-900/50 border-slate-600 text-white"
                />
                <Input
                    placeholder="Variable value"
                    value={variableInput.value}
                    onChange={(e) => setVariableInput(prev => ({...prev, value: e.target.value}))}
                    className="bg-slate-900/50 border-slate-600 text-white"
                />
                <Button onClick={handleAddVariable} variant="outline" className="border-slate-600 text-slate-300">Add</Button>
              </div>
            </div>
          </div>

          <div>
            <Label className="text-slate-200">Preview</Label>
            <div className="bg-slate-900/50 p-3 rounded border border-slate-600 mt-1 min-h-[60px]">
              <p className="text-sm text-slate-300 whitespace-pre-wrap">
                {renderPromptWithVariables(localVariant.prompt, localVariant.variables || {})}
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="documentation" className="space-y-4 mt-4">
          <div>
            <Label className="text-slate-200">Purpose & Utility</Label>
            <Textarea
              value={localVariant.purpose || ''}
              onChange={(e) => handleFieldChange('purpose', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Explain what this variant does and why it's useful..."
            />
          </div>
          <div>
            <Label className="text-slate-200">When to Use</Label>
            <Textarea
              value={localVariant.whenToUse || ''}
              onChange={(e) => handleFieldChange('whenToUse', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Describe ideal use cases and scenarios..."
            />
          </div>
          <div>
            <Label className="text-slate-200">When NOT to Use</Label>
            <Textarea
              value={localVariant.whenNotToUse || ''}
              onChange={(e) => handleFieldChange('whenNotToUse', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Warn about inappropriate use cases..."
            />
          </div>
          <div>
            <Label className="text-slate-200">Suggestions & Notes</Label>
            <Textarea
              value={localVariant.suggestions || ''}
              onChange={(e) => handleFieldChange('suggestions', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Additional tips, optimization notes, or usage recommendations..."
            />
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4 mt-4">
          <div>
            <Label className="text-slate-200">Experimental Sub-Variants</Label>
            <div className="space-y-2 mt-2">
              {(localVariant.experimentalSubVariants || []).map((subVariant, index) => (
                <div key={index} className="flex gap-2">
                  <Textarea
                    value={subVariant}
                    onChange={(e) => handleUpdateSubVariantEntry(index, e.target.value)}
                    className="bg-slate-900/50 border-slate-600 text-white flex-1"
                    placeholder="Alternative version of this variant..."
                  />
                  <Button variant="ghost" size="sm" onClick={() => handleRemoveSubVariantEntry(index)} className="text-red-400 hover:text-red-300">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
              <Button variant="outline" size="sm" onClick={handleAddSubVariantEntry} className="border-slate-600 text-slate-300">
                <Plus className="w-4 h-4 mr-2" />
                Add Sub-Variant
              </Button>
            </div>
          </div>
          <Separator className="bg-slate-600" />
          <div>
            <Label className="text-slate-200">Variant Build Kit</Label>
            <div className="space-y-3 mt-2">
              <div>
                <Label className="text-slate-400 text-sm">Prompt Frame</Label>
                <Input
                  value={localVariant.buildKit?.frame || ''}
                  onChange={(e) => handleNestedFieldChange('buildKit', 'frame', e.target.value)}
                  className="bg-slate-900/50 border-slate-600 text-white mt-1"
                  placeholder="[ Component 1 + Component 2 + Component 3 ]"
                />
              </div>
              <div>
                <Label className="text-slate-400 text-sm">Example Structure</Label>
                <Input
                  value={localVariant.buildKit?.example || ''}
                  onChange={(e) => handleNestedFieldChange('buildKit', 'example', e.target.value)}
                  className="bg-slate-900/50 border-slate-600 text-white mt-1"
                  placeholder="Action | target | constraint"
                />
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
};
