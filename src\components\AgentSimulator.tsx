
import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Bot, Trash2, Play, Users, Settings, Zap, Target } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { usePromptStore } from '@/hooks/usePromptStore';
import { Agent } from '@/store/promptStore';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { AgentForm } from './AgentForm';
import { Edit2 } from 'lucide-react';

interface AgentSimulatorProps {
  onRunSimulation: (agents: Agent[], scenario: string) => void;
  onRunMultiAgentSimulation: (agents: Agent[], scenario: string, maxTurns: number) => void;
}

export const AgentSimulator = ({ onRunSimulation, onRunMultiAgentSimulation }: AgentSimulatorProps) => {
  const { agents, enabledAgents, addAgent, updateAgent, deleteAgent, toggleAgent, prompts, runIntegratedTest } = usePromptStore();
  
  const [simulationMode, setSimulationMode] = useState<'individual' | 'multiAgent'>('individual');
  const [maxTurns, setMaxTurns] = useState<number>(10);
  const [scenario, setScenario] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [selectedPrompts, setSelectedPrompts] = useState<string[]>([]);

  const handleSaveAgent = (agentData: Omit<Agent, 'id' | 'enabled'> | Partial<Agent>) => {
    try {
      if (editingAgent) {
        updateAgent(editingAgent.id, agentData as Partial<Agent>);
        toast({ title: "Agent Updated", description: `${agentData.name || editingAgent.name} has been updated.` });
      } else {
        const newAgentPayload = {
          name: agentData.name || 'Unnamed Agent',
          role: agentData.role || 'Default Role',
          systemPrompt: agentData.systemPrompt || 'Default System Prompt',
          personality: agentData.personality || '',
          expertise: agentData.expertise || [],
          avatar: agentData.avatar || '🤖',
        };
        addAgent(newAgentPayload as Omit<Agent, 'id' | 'enabled'>);
        toast({ title: "Agent Created", description: `${newAgentPayload.name} has been added.` });
      }
      setIsFormOpen(false);
      setEditingAgent(null);
    } catch (error: any) {
      toast({ title: "Error Saving Agent", description: error.message || "Failed to save agent.", variant: "destructive"});
    }
  };

  const openNewAgentForm = () => {
    setEditingAgent(null);
    setIsFormOpen(true);
  };

  const openEditAgentForm = (agentToEdit: Agent) => {
    setEditingAgent(agentToEdit);
    setIsFormOpen(true);
  };

  const handleRunSimulation = () => {
    const activeAgents = agents.filter(agent => agent.enabled);
    if (activeAgents.length === 0) {
      toast({
        title: "Error",
        description: "Please enable at least one agent for the simulation.",
        variant: "destructive"
      });
      return;
    }

    if (!scenario.trim()) {
      toast({
        title: "Error",
        description: "Please provide a simulation scenario.",
        variant: "destructive"
      });
      return;
    }

    if (simulationMode === 'individual') {
      if (activeAgents.length > 1) {
         toast({
          title: "Info",
          description: "Individual mode selected. Each enabled agent will respond to the scenario independently.",
        });
      }
      onRunSimulation(activeAgents, scenario);
    } else {
      if (activeAgents.length < 2) {
        toast({
          title: "Error",
          description: "Multi-agent simulation requires at least two enabled agents.",
          variant: "destructive"
        });
        return;
      }
      if (maxTurns <= 0) {
        toast({
          title: "Error",
          description: "Max turns for multi-agent simulation must be greater than 0.",
          variant: "destructive"
        });
        return;
      }
      onRunMultiAgentSimulation(activeAgents, scenario, maxTurns);
    }
  };

  const handleRunIntegratedTest = async () => {
    if (selectedPrompts.length === 0 || enabledAgents.length === 0) {
      toast({
        title: "Error",
        description: "Please select prompts and enable agents",
        variant: "destructive"
      });
      return;
    }

    const testToastId = toast({
      title: "Integrated Test In Progress...",
      description: `Testing ${selectedPrompts.length} prompt(s) against ${enabledAgents.length} agent(s).`,
    });

    try {
      await runIntegratedTest(selectedPrompts, enabledAgents.map(a => a.id), scenario);
      toast({
        title: "Integrated Test Complete",
        description: "Results have been added to the dashboard.",
        variant: "default",
      });
    } catch (error: any) {
      console.error("Integrated test failed:", error);
      toast({
        title: "Integrated Test Failed",
        description: error.message || "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
        if(testToastId && testToastId.dismiss) testToastId.dismiss();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-200 flex items-center gap-3">
          <Bot className="w-8 h-8 text-purple-400" />
          Agent Studio
        </h2>
        <Button
          onClick={openNewAgentForm}
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Agent
        </Button>
      </div>

      <Tabs defaultValue="agents" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800/50 border border-slate-600">
          <TabsTrigger 
            value="agents" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <Users className="w-4 h-4 mr-2" />
            Agents ({agents.length})
          </TabsTrigger>
          <TabsTrigger 
            value="simulation" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-blue-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <Zap className="w-4 h-4 mr-2" />
            Simulation
          </TabsTrigger>
          <TabsTrigger 
            value="integration" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-green-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <Target className="w-4 h-4 mr-2" />
            Integration
          </TabsTrigger>
        </TabsList>

        <TabsContent value="agents" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <div className="space-y-4">
              <Label htmlFor="scenario" className="text-slate-200 flex items-center gap-2">
                <Zap className="w-4 h-4 text-yellow-400" />
                Global Scenario (applies to all tests)
              </Label>
              <Textarea
                id="scenario"
                placeholder="Describe the scenario you want the agents to respond to..."
                value={scenario}
                onChange={(e) => setScenario(e.target.value)}
                className="bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 min-h-[100px]"
              />
            </div>
          </Card>

          <div className="grid gap-4">
            {agents.map((agent) => (
              <Card key={agent.id} className="bg-slate-800/50 border-slate-700 p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={agent.enabled}
                      onCheckedChange={() => toggleAgent(agent.id)}
                      className="border-slate-500"
                    />
                    <div className="text-2xl">{agent.avatar || '🤖'}</div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-200">{agent.name}</h3>
                      <Badge variant="outline" className="border-purple-500 text-purple-300">
                        {agent.role}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openEditAgentForm(agent)}
                      className="text-blue-400 hover:text-blue-300 hover:bg-blue-900/20 p-1 h-auto"
                      title="Edit Agent"
                    >
                      <Edit2 className="w-3.5 h-3.5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteAgent(agent.id)}
                      className="text-red-400 hover:text-red-300 hover:bg-red-900/20 p-1 h-auto"
                      title="Delete Agent"
                    >
                      <Trash2 className="w-3.5 h-3.5" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-3 text-sm">
                  <p className="text-slate-400">
                    <span className="font-medium">System Prompt:</span> {agent.systemPrompt.slice(0, 150)}...
                  </p>
                  {agent.personality && (
                    <p className="text-slate-400">
                      <span className="font-medium">Personality:</span> {agent.personality}
                    </p>
                  )}
                  {agent.expertise && agent.expertise.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      <span className="text-slate-400 text-xs">Expertise:</span>
                      {agent.expertise.map((skill, index) => (
                        <Badge key={index} variant="secondary" className="bg-slate-700 text-slate-300 text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="simulation" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-200 mb-4 flex items-center gap-2">
              <Zap className="w-5 h-5 text-blue-400" />
              Simulation Setup
            </h3>
            <div className="space-y-6">
              <div>
                <Label className="text-slate-200 mb-2 block">Simulation Mode</Label>
                <RadioGroup
                  defaultValue="individual"
                  value={simulationMode}
                  onValueChange={(value: 'individual' | 'multiAgent') => setSimulationMode(value)}
                  className="flex gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="individual" id="r-individual" className="text-blue-400 border-slate-500"/>
                    <Label htmlFor="r-individual" className="text-slate-300">Individual Agents</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="multiAgent" id="r-multiAgent" className="text-purple-400 border-slate-500" />
                    <Label htmlFor="r-multiAgent" className="text-slate-300">Multi-Agent Conversation</Label>
                  </div>
                </RadioGroup>
              </div>

              {simulationMode === 'multiAgent' && (
                <div>
                  <Label htmlFor="max-turns" className="text-slate-200">Max Conversation Turns (per agent)</Label>
                  <Input
                    id="max-turns"
                    type="number"
                    value={maxTurns}
                    onChange={(e) => setMaxTurns(Math.max(1, parseInt(e.target.value, 10) || 1))}
                    className="bg-slate-900/50 border-slate-600 text-white mt-1 w-32"
                    min="1"
                  />
                   <p className="text-xs text-slate-400 mt-1">Total interactions will be roughly (Active Agents * Max Turns).</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center pt-4 border-t border-slate-700">
                <div>
                  <div className="text-2xl font-bold text-purple-400">{agents.length}</div>
                  <div className="text-sm text-slate-400">Total Agents</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400">{enabledAgents.length}</div>
                  <div className="text-sm text-slate-400">Active Agents</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-400">
                    {agents.reduce((acc, a) => acc + (a.expertise?.length || 0), 0)}
                  </div>
                  <div className="text-sm text-slate-400">Skills Total</div>
                </div>
              </div>
              
              <Button 
                onClick={handleRunSimulation}
                disabled={enabledAgents.length === 0}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                size="lg"
              >
                <Play className="w-4 h-4 mr-2" />
                Run Agent Simulation
              </Button>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="integration" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-200 mb-4 flex items-center gap-2">
              <Target className="w-5 h-5 text-green-400" />
              Integrated Testing
            </h3>
            <div className="space-y-4">
              <div>
                <Label className="text-slate-200">Select Prompts to Test</Label>
                <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                  {prompts.map((prompt) => (
                    <div key={prompt.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={prompt.id}
                        checked={selectedPrompts.includes(prompt.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedPrompts(prev => [...prev, prompt.id]);
                          } else {
                            setSelectedPrompts(prev => prev.filter(id => id !== prompt.id));
                          }
                        }}
                      />
                      <label htmlFor={prompt.id} className="text-sm text-slate-300 cursor-pointer">
                        {prompt.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-xl font-bold text-blue-400">{selectedPrompts.length}</div>
                  <div className="text-sm text-slate-400">Selected Prompts</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-purple-400">{enabledAgents.length}</div>
                  <div className="text-sm text-slate-400">Active Agents</div>
                </div>
              </div>

              <Button 
                onClick={handleRunIntegratedTest}
                disabled={selectedPrompts.length === 0 || enabledAgents.length === 0}
                className="w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700"
                size="lg"
              >
                <Target className="w-4 h-4 mr-2" />
                Run Integrated Test
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      <AgentForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={handleSaveAgent}
        editingAgent={editingAgent}
      />
    </div>
  );
};
