export interface PerformanceMetric {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
  tags?: string[];
}

export interface LoadingState {
  isLoading: boolean;
  progress?: number;
  message?: string;
  stage?: string;
}

export interface PerformanceStats {
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  totalCalls: number;
  successRate: number;
  recentMetrics: PerformanceMetric[];
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private completedMetrics: PerformanceMetric[] = [];
  private maxHistorySize = 1000;

  /**
   * Start measuring performance for an operation
   */
  startMeasurement(name: string, metadata?: Record<string, any>, tags?: string[]): string {
    const id = `${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const metric: PerformanceMetric = {
      id,
      name,
      startTime: performance.now(),
      metadata,
      tags
    };

    this.metrics.set(id, metric);
    return id;
  }

  /**
   * End measurement and calculate duration
   */
  endMeasurement(id: string, additionalMetadata?: Record<string, any>): PerformanceMetric | null {
    const metric = this.metrics.get(id);
    if (!metric) {
      console.warn(`Performance metric with id ${id} not found`);
      return null;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    
    if (additionalMetadata) {
      metric.metadata = { ...metric.metadata, ...additionalMetadata };
    }

    this.metrics.delete(id);
    this.completedMetrics.unshift(metric);

    // Keep history size manageable
    if (this.completedMetrics.length > this.maxHistorySize) {
      this.completedMetrics = this.completedMetrics.slice(0, this.maxHistorySize);
    }

    // Log slow operations in development
    if (process.env.NODE_ENV === 'development' && metric.duration > 1000) {
      console.warn(`Slow operation detected: ${metric.name} took ${metric.duration.toFixed(2)}ms`);
    }

    return metric;
  }

  /**
   * Measure a function execution
   */
  async measureFunction<T>(
    name: string,
    fn: () => Promise<T> | T,
    metadata?: Record<string, any>,
    tags?: string[]
  ): Promise<T> {
    const id = this.startMeasurement(name, metadata, tags);
    
    try {
      const result = await fn();
      this.endMeasurement(id, { success: true });
      return result;
    } catch (error) {
      this.endMeasurement(id, { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Get performance statistics for a specific operation
   */
  getStats(operationName: string): PerformanceStats {
    const operationMetrics = this.completedMetrics.filter(m => m.name === operationName);
    
    if (operationMetrics.length === 0) {
      return {
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        totalCalls: 0,
        successRate: 0,
        recentMetrics: []
      };
    }

    const durations = operationMetrics.map(m => m.duration!);
    const successfulCalls = operationMetrics.filter(m => m.metadata?.success !== false).length;

    return {
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      totalCalls: operationMetrics.length,
      successRate: (successfulCalls / operationMetrics.length) * 100,
      recentMetrics: operationMetrics.slice(0, 10)
    };
  }

  /**
   * Get all performance statistics
   */
  getAllStats(): Record<string, PerformanceStats> {
    const operationNames = [...new Set(this.completedMetrics.map(m => m.name))];
    const stats: Record<string, PerformanceStats> = {};

    operationNames.forEach(name => {
      stats[name] = this.getStats(name);
    });

    return stats;
  }

  /**
   * Get recent metrics
   */
  getRecentMetrics(limit: number = 20): PerformanceMetric[] {
    return this.completedMetrics.slice(0, limit);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics.clear();
    this.completedMetrics = [];
  }

  /**
   * Get slow operations (above threshold)
   */
  getSlowOperations(thresholdMs: number = 1000): PerformanceMetric[] {
    return this.completedMetrics.filter(m => m.duration! > thresholdMs);
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): {
    timestamp: string;
    metrics: PerformanceMetric[];
    summary: Record<string, PerformanceStats>;
  } {
    return {
      timestamp: new Date().toISOString(),
      metrics: this.completedMetrics,
      summary: this.getAllStats()
    };
  }
}

class LoadingManager {
  private loadingStates: Map<string, LoadingState> = new Map();
  private listeners: Map<string, ((state: LoadingState) => void)[]> = new Map();

  /**
   * Set loading state for a specific operation
   */
  setLoading(
    operationId: string, 
    isLoading: boolean, 
    options?: {
      progress?: number;
      message?: string;
      stage?: string;
    }
  ): void {
    const state: LoadingState = {
      isLoading,
      progress: options?.progress,
      message: options?.message,
      stage: options?.stage
    };

    this.loadingStates.set(operationId, state);
    this.notifyListeners(operationId, state);
  }

  /**
   * Update loading progress
   */
  updateProgress(operationId: string, progress: number, message?: string): void {
    const currentState = this.loadingStates.get(operationId);
    if (currentState) {
      this.setLoading(operationId, true, {
        ...currentState,
        progress,
        message: message || currentState.message
      });
    }
  }

  /**
   * Update loading stage
   */
  updateStage(operationId: string, stage: string, message?: string): void {
    const currentState = this.loadingStates.get(operationId);
    if (currentState) {
      this.setLoading(operationId, true, {
        ...currentState,
        stage,
        message: message || currentState.message
      });
    }
  }

  /**
   * Get loading state for an operation
   */
  getLoadingState(operationId: string): LoadingState | undefined {
    return this.loadingStates.get(operationId);
  }

  /**
   * Check if any operation is loading
   */
  isAnyLoading(): boolean {
    return Array.from(this.loadingStates.values()).some(state => state.isLoading);
  }

  /**
   * Get all loading operations
   */
  getLoadingOperations(): Record<string, LoadingState> {
    const loading: Record<string, LoadingState> = {};
    this.loadingStates.forEach((state, id) => {
      if (state.isLoading) {
        loading[id] = state;
      }
    });
    return loading;
  }

  /**
   * Subscribe to loading state changes
   */
  subscribe(operationId: string, callback: (state: LoadingState) => void): () => void {
    if (!this.listeners.has(operationId)) {
      this.listeners.set(operationId, []);
    }
    
    this.listeners.get(operationId)!.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(operationId);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * Clear loading state for an operation
   */
  clearLoading(operationId: string): void {
    this.loadingStates.delete(operationId);
    this.notifyListeners(operationId, { isLoading: false });
  }

  /**
   * Clear all loading states
   */
  clearAllLoading(): void {
    this.loadingStates.clear();
    this.listeners.clear();
  }

  private notifyListeners(operationId: string, state: LoadingState): void {
    const callbacks = this.listeners.get(operationId);
    if (callbacks) {
      callbacks.forEach(callback => callback(state));
    }
  }
}

// Global instances
export const performanceMonitor = new PerformanceMonitor();
export const loadingManager = new LoadingManager();

/**
 * Decorator for measuring function performance
 */
export function measurePerformance(name?: string, tags?: string[]) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operationName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      return performanceMonitor.measureFunction(
        operationName,
        () => originalMethod.apply(this, args),
        { className: target.constructor.name, methodName: propertyKey },
        tags
      );
    };

    return descriptor;
  };
}

/**
 * Hook for React components to use performance monitoring
 */
export function usePerformance() {
  return {
    startMeasurement: performanceMonitor.startMeasurement.bind(performanceMonitor),
    endMeasurement: performanceMonitor.endMeasurement.bind(performanceMonitor),
    measureFunction: performanceMonitor.measureFunction.bind(performanceMonitor),
    getStats: performanceMonitor.getStats.bind(performanceMonitor),
    getAllStats: performanceMonitor.getAllStats.bind(performanceMonitor),
    getRecentMetrics: performanceMonitor.getRecentMetrics.bind(performanceMonitor),
    getSlowOperations: performanceMonitor.getSlowOperations.bind(performanceMonitor)
  };
}

/**
 * Hook for React components to use loading management
 */
export function useLoading(operationId?: string) {
  const id = operationId || 'default';

  return {
    setLoading: (isLoading: boolean, options?: any) => 
      loadingManager.setLoading(id, isLoading, options),
    updateProgress: (progress: number, message?: string) => 
      loadingManager.updateProgress(id, progress, message),
    updateStage: (stage: string, message?: string) => 
      loadingManager.updateStage(id, stage, message),
    getLoadingState: () => loadingManager.getLoadingState(id),
    clearLoading: () => loadingManager.clearLoading(id),
    subscribe: (callback: (state: LoadingState) => void) => 
      loadingManager.subscribe(id, callback),
    isAnyLoading: loadingManager.isAnyLoading.bind(loadingManager),
    getLoadingOperations: loadingManager.getLoadingOperations.bind(loadingManager)
  };
}

/**
 * Utility to measure and track async operations with loading states
 */
export async function withLoadingAndPerformance<T>(
  operationId: string,
  operationName: string,
  operation: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
  options?: {
    tags?: string[];
    metadata?: Record<string, any>;
  }
): Promise<T> {
  const updateProgress = (progress: number, message?: string) => {
    loadingManager.updateProgress(operationId, progress, message);
  };

  loadingManager.setLoading(operationId, true, { progress: 0 });

  try {
    const result = await performanceMonitor.measureFunction(
      operationName,
      () => operation(updateProgress),
      options?.metadata,
      options?.tags
    );

    loadingManager.setLoading(operationId, false);
    return result;
  } catch (error) {
    loadingManager.setLoading(operationId, false);
    throw error;
  }
}
