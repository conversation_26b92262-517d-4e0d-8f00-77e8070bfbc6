import { aiService, AIRequest, AIResponse } from './ai-service';
import { agentSimulationService, AgentSimulationRequest, SimulationResult as AgentSimulationResult, AgentResponse as AgentSimResponse } from './agent-simulation';
import { Agent } from '@/store/promptStore';

export interface ChainNode {
  id: string;
  type: 'prompt' |  'agent' | 'condition' | 'output' | 'input';
  position: { x: number; y: number };
  data: {
    title: string;
    description?: string;
    content?: string;
    config?: Record<string, any>;
  };
  inputs: Array<{
    id: string;
    label: string;
    type: string;
    required: boolean;
  }>;
  outputs: Array<{
    id: string;
    label: string;
    type: string;
  }>;
}

export interface ChainConnection {
  id: string;
  sourceNodeId: string;
  sourceOutputId: string;
  targetNodeId: string;
  targetInputId: string;
  data?: {
    transform?: string;
    condition?: string;
  };
}

export interface ChainFlow {
  id: string;
  name: string;
  description: string;
  nodes: ChainNode[];
  connections: ChainConnection[];
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    version: string;
    tags: string[];
  };
}

export interface ExecutionContext {
  variables: Record<string, any>;
  results: Record<string, any>;
  currentNode?: string;
  executionPath: string[];
}

export interface ExecutionResult {
  success: boolean;
  results: Record<string, any>;
  executionPath: string[];
  duration: number;
  errors?: Array<{
    nodeId: string;
    message: string;
    details?: any;
  }>;
}

export interface NodeExecutionResult {
  response?: string;
  confidence?: number;
  insights?: any[];
  metadata?: Record<string, any>;
  [key: string]: any;
}

// Helper function to process variables (adapted from PromptTestingService)
function processVariables(text: string, variables?: Record<string, any>): string {
  if (!variables || Object.keys(variables).length === 0) return text;

  let processedText = text;
  for (const [key, value] of Object.entries(variables)) {
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
    const patterns = [
      new RegExp(`\\{\\{${key}\\}\\}`, 'g'),
      new RegExp(`\\{${key}\\}`, 'g')
    ];

    for (const pattern of patterns) {
      processedText = processedText.replace(pattern, stringValue);
    }
  }
  return processedText;
}

export class ChainLinkerService {
  /**
   * Process variables in text
   */
  processVariables(text: string, variables?: Record<string, any>): string {
    return processVariables(text, variables);
  }

  /**
   * Create a new chain flow
   */
  createFlow(name: string, description: string): ChainFlow {
    return {
      id: `flow_${Date.now()}`,
      name,
      description,
      nodes: [],
      connections: [],
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        version: '1.0',
        tags: []
      }
    };
  }

  /**
   * Add a node to the flow
   */
  addNode(flow: ChainFlow, nodeType: ChainNode['type'], position: { x: number; y: number }): ChainNode {
    const nodeTemplates = this.getNodeTemplates();
    const template = nodeTemplates[nodeType];
    
    const node: ChainNode = {
      id: `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: nodeType,
      position,
      data: {
        title: template.title,
        description: template.description,
        content: template.defaultContent || '',
        config: { ...template.defaultConfig }
      },
      inputs: [...template.inputs],
      outputs: [...template.outputs]
    };

    flow.nodes.push(node);
    flow.metadata.updatedAt = new Date();
    
    return node;
  }

  /**
   * Remove a node from the flow
   */
  removeNode(flow: ChainFlow, nodeId: string): void {
    // Remove the node
    flow.nodes = flow.nodes.filter(node => node.id !== nodeId);
    
    // Remove all connections involving this node
    flow.connections = flow.connections.filter(
      conn => conn.sourceNodeId !== nodeId && conn.targetNodeId !== nodeId
    );
    
    flow.metadata.updatedAt = new Date();
  }

  /**
   * Add a connection between nodes
   */
  addConnection(
    flow: ChainFlow,
    sourceNodeId: string,
    sourceOutputId: string,
    targetNodeId: string,
    targetInputId: string
  ): ChainConnection {
    // Validate connection
    const sourceNode = flow.nodes.find(n => n.id === sourceNodeId);
    const targetNode = flow.nodes.find(n => n.id === targetNodeId);
    
    if (!sourceNode || !targetNode) {
      throw new Error('Source or target node not found');
    }

    const sourceOutput = sourceNode.outputs.find(o => o.id === sourceOutputId);
    const targetInput = targetNode.inputs.find(i => i.id === targetInputId);
    
    if (!sourceOutput || !targetInput) {
      throw new Error('Source output or target input not found');
    }

    // Check for cycles
    if (this.wouldCreateCycle(flow, sourceNodeId, targetNodeId)) {
      throw new Error('Connection would create a cycle');
    }

    const connection: ChainConnection = {
      id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sourceNodeId,
      sourceOutputId,
      targetNodeId,
      targetInputId
    };

    flow.connections.push(connection);
    flow.metadata.updatedAt = new Date();
    
    return connection;
  }

  /**
   * Remove a connection
   */
  removeConnection(flow: ChainFlow, connectionId: string): void {
    flow.connections = flow.connections.filter(conn => conn.id !== connectionId);
    flow.metadata.updatedAt = new Date();
  }

  /**
   * Execute a chain flow
   */
  async executeFlow(flow: ChainFlow, initialVariables: Record<string, any> = {}): Promise<ExecutionResult> {
    const startTime = Date.now();
    const context: ExecutionContext = {
      variables: { ...initialVariables },
      results: {},
      executionPath: []
    };

    const errors: Array<{ nodeId: string; message: string }> = [];

    try {
      // Find entry points (nodes with no incoming connections)
      const entryNodes = this.findEntryNodes(flow);
      
      if (entryNodes.length === 0) {
        throw new Error('No entry points found in the flow');
      }

      // Execute nodes in topological order
      const executionOrder = this.getExecutionOrder(flow);
      
      for (const nodeId of executionOrder) {
        try {
          await this.executeNode(flow, nodeId, context);
          context.executionPath.push(nodeId);
        } catch (error) {
          errors.push({
            nodeId,
            message: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return {
        success: errors.length === 0,
        results: context.results,
        executionPath: context.executionPath,
        duration: Date.now() - startTime,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      return {
        success: false,
        results: context.results,
        executionPath: context.executionPath,
        duration: Date.now() - startTime,
        errors: [{
          nodeId: 'flow',
          message: error instanceof Error ? error.message : 'Flow execution failed'
        }]
      };
    }
  }

  /**
   * Get node templates for different types
   */
  private getNodeTemplates(): Record<string, any> {
    return {
      input: {
        title: 'Input',
        description: 'Provides input data to the flow',
        inputs: [],
        outputs: [
          { id: 'value', label: 'Value', type: 'any' }
        ],
        defaultConfig: {
          inputType: 'text',
          defaultValue: ''
        }
      },
      prompt: {
        title: 'Prompt Node',
        description: 'Executes a prompt with an AI model',
        inputs: [
          { id: 'input', label: 'Input', type: 'string', required: true },
          { id: 'variables', label: 'Variables', type: 'object', required: false }
        ],
        outputs: [
          { id: 'response', label: 'Response', type: 'string' },
          { id: 'metadata', label: 'Metadata', type: 'object' }
        ],
        defaultContent: 'Enter your prompt here: {{input}}', // Default content now includes an example variable
        defaultConfig: {
          provider: 'openai',
          model: 'gpt-4',
          temperature: 0.7,
          maxTokens: 1024,
          systemPrompt: 'You are a helpful assistant.'
        }
      },
      agent: {
        title: 'Agent Node',
        description: 'Processes input through an AI agent',
        inputs: [
          { id: 'scenario', label: 'Scenario', type: 'string', required: true },
          { id: 'context', label: 'Context', type: 'object', required: false },
          { id: 'variables', label: 'Variables for Scenario', type: 'object', required: false }
        ],
        outputs: [
          { id: 'response', label: 'Response', type: 'string' },
          { id: 'confidence', label: 'Confidence', type: 'number' },
          { id: 'insights', label: 'Insights', type: 'array' },
          { id: 'metadata', label: 'Agent Metadata', type: 'object'}
        ],
        defaultConfig: {
          agentId: 'default-agent',
          agentName: 'Default Agent',
          agentRole: 'AI Assistant',
          agentSystemPrompt: 'You are a helpful AI assistant. Analyze the scenario and provide your expert insights.',
          agentPersonality: 'Neutral and informative',
          agentExpertise: ['general analysis'],
          temperature: 0.7,
          maxTokens: 1024,
          provider: 'openai',
          model: 'gpt-4',
        }
      },
      condition: {
        title: 'Condition',
        description: 'Routes flow based on conditions',
        inputs: [
          { id: 'input', label: 'Input', type: 'any', required: true }
        ],
        outputs: [
          { id: 'true', label: 'True', type: 'any' },
          { id: 'false', label: 'False', type: 'any' }
        ],
        defaultContent: 'input.length > 0',
        defaultConfig: {
          conditionType: 'javascript'
        }
      },
      output: {
        title: 'Output',
        description: 'Collects and formats final output',
        inputs: [
          { id: 'data', label: 'Data', type: 'any', required: true }
        ],
        outputs: [],
        defaultConfig: {
          format: 'json',
          includeMetadata: true
        }
      }
    };
  }

  /**
   * Check if adding a connection would create a cycle
   */
  private wouldCreateCycle(flow: ChainFlow, sourceNodeId: string, targetNodeId: string): boolean {
    // Simple cycle detection using DFS
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      // Check all outgoing connections
      const outgoingConnections = flow.connections.filter(conn => conn.sourceNodeId === nodeId);
      for (const conn of outgoingConnections) {
        if (hasCycle(conn.targetNodeId)) return true;
      }

      // Add the hypothetical new connection
      if (nodeId === sourceNodeId && hasCycle(targetNodeId)) return true;

      recursionStack.delete(nodeId);
      return false;
    };

    return hasCycle(sourceNodeId);
  }

  /**
   * Find nodes with no incoming connections
   */
  private findEntryNodes(flow: ChainFlow): string[] {
    const nodesWithIncoming = new Set(flow.connections.map(conn => conn.targetNodeId));
    return flow.nodes
      .filter(node => !nodesWithIncoming.has(node.id))
      .map(node => node.id);
  }

  /**
   * Get execution order using topological sort
   */
  private getExecutionOrder(flow: ChainFlow): string[] {
    const inDegree = new Map<string, number>();
    const adjList = new Map<string, string[]>();

    // Initialize
    flow.nodes.forEach(node => {
      inDegree.set(node.id, 0);
      adjList.set(node.id, []);
    });

    // Build adjacency list and calculate in-degrees
    flow.connections.forEach(conn => {
      adjList.get(conn.sourceNodeId)?.push(conn.targetNodeId);
      inDegree.set(conn.targetNodeId, (inDegree.get(conn.targetNodeId) || 0) + 1);
    });

    // Topological sort
    const queue: string[] = [];
    const result: string[] = [];

    // Add nodes with no incoming edges
    inDegree.forEach((degree, nodeId) => {
      if (degree === 0) queue.push(nodeId);
    });

    while (queue.length > 0) {
      const nodeId = queue.shift()!;
      result.push(nodeId);

      // Process neighbors
      adjList.get(nodeId)?.forEach(neighborId => {
        const newDegree = (inDegree.get(neighborId) || 0) - 1;
        inDegree.set(neighborId, newDegree);
        
        if (newDegree === 0) {
          queue.push(neighborId);
        }
      });
    }

    return result;
  }

  /**
   * Execute a single node
   */
  private async executeNode(flow: ChainFlow, nodeId: string, context: ExecutionContext): Promise<void> {
    const node = flow.nodes.find(n => n.id === nodeId);
    if (!node) throw new Error(`Node ${nodeId} not found`);

    context.currentNode = nodeId;

    // Collect inputs from connected nodes
    const inputs: Record<string, any> = {};
    const incomingConnections = flow.connections.filter(conn => conn.targetNodeId === nodeId);
    
    for (const conn of incomingConnections) {
      const sourceResult = context.results[conn.sourceNodeId];
      if (sourceResult && sourceResult[conn.sourceOutputId] !== undefined) {
        inputs[conn.targetInputId] = sourceResult[conn.sourceOutputId];
      }
    }

    // Execute node based on type
    let result: any = {};

    switch (node.type) {
      case 'input':
        result = { value: node.data.config?.defaultValue || '' };
        break;
        
      case 'prompt':
        result = await this.executePromptNode(node, inputs);
        break;
        
      case 'agent':
        result = await this.executeAgentNode(flow, node, inputs);
        break;
        
      case 'condition':
        result = this.executeConditionNode(node, inputs);
        break;
        
      case 'output':
        result = this.executeOutputNode(node, inputs);
        break;
        
      default:
        throw new Error(`Unknown node type: ${node.type}`);
    }

    context.results[nodeId] = result;
  }

  private async executePromptNode(node: ChainNode, inputs: Record<string, any>): Promise<any> {
    const nodeConfig = node.data.config || {};
    const nodeContent = node.data.content || '';
    // Consolidate variables from direct input and a potential 'variables' input port
    const passedVariables = inputs.variables || {};
    const allVariables = { ...passedVariables, ...inputs }; // Ensure inputs themselves can be used as variables e.g. {{input}}

    const systemPrompt = processVariables(nodeConfig.systemPrompt || '', allVariables);
    // The main 'input' to a prompt node often comes from an 'input' port.
    // If 'inputs.input' exists, use it as the primary content to process. Otherwise, use node.data.content.
    const promptTemplate = inputs.input !== undefined ? String(inputs.input) : nodeContent;
    const userPrompt = processVariables(promptTemplate, allVariables);

    const messages: AIRequest['messages'] = [];
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    messages.push({ role: 'user', content: userPrompt });

    const aiRequest: Partial<AIRequest> = {
      provider: nodeConfig.provider,
      model: nodeConfig.model,
      messages,
      temperature: nodeConfig.temperature,
      maxTokens: nodeConfig.maxTokens,
    };

    const startTime = Date.now();
    try {
      const aiResponse = await aiService.generateResponse(aiRequest);
      const duration = Date.now() - startTime;

      return {
        response: aiResponse.content,
        metadata: {
          model: aiResponse.model,
          provider: aiResponse.provider,
          tokens: aiResponse.usage?.totalTokens,
          promptTokens: aiResponse.usage?.promptTokens,
          completionTokens: aiResponse.usage?.completionTokens,
          duration: duration,
          timestamp: aiResponse.timestamp.toISOString()
        }
      };
    } catch (error: any) {
      // Do not record error in context.errors here, let executeFlow handle it.
      console.error(`AI service error for prompt node ${node.id} (${node.data.title}):`, error.message);
      throw new Error(`AI call failed for node "${node.data.title}": ${error.message}`);
    }
  }

  private async executeAgentNode(flow: ChainFlow, node: ChainNode, inputs: any): Promise<NodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Create agent from node configuration
      const agent: Agent = {
        id: node.data.config?.agentId || `agent_${node.id}`,
        name: node.data.config?.agentName || `Agent ${node.id}`,
        role: node.data.config?.agentRole || 'Assistant',
        systemPrompt: processVariables(node.data.config?.agentSystemPrompt || 'You are a helpful assistant.', inputs.variables || {}),
        personality: node.data.config?.agentPersonality,
        expertise: node.data.config?.agentExpertise || [],
        enabled: true,
        modelConfig: {
          provider: node.data.config?.provider || 'openai',
          model: node.data.config?.model || 'gpt-4',
          temperature: node.data.config?.temperature || 0.7,
          maxTokens: node.data.config?.maxTokens || 1024
        }
      };

      const request = {
        agents: [agent],
        scenario: processVariables(inputs.scenario || 'Please respond to this request.', inputs.variables || {}),
        temperature: node.data.config?.temperature || 0.7,
        maxTokens: node.data.config?.maxTokens || 1024
      };

      const result = await agentSimulationService.runSimulation(request);
      const agentResponse = result.agentResponses[0];

      return {
        response: agentResponse.response,
        confidence: agentResponse.confidence,
        insights: agentResponse.keyInsights,
        metadata: {
          agentId: agent.id,
          agentName: agentResponse.agentName,
          model: agentResponse.aiResponse?.model || agent.modelConfig?.model,
          provider: agentResponse.aiResponse?.provider || agent.modelConfig?.provider,
          totalTokens: result.metadata.totalTokens,
          duration: Date.now() - startTime,
          timestamp: new Date()
        }
      };
    } catch (error) {
      return {
        response: `[Error: Agent execution failed - ${error instanceof Error ? error.message : 'Unknown error'}]`,
        confidence: 0,
        insights: [],
        metadata: {
          agentId: node.data.config?.agentId || `agent_${node.id}`,
          agentName: node.data.config?.agentName || `Agent ${node.id}`,
          error: error instanceof Error ? error.message : 'Unknown error',
          duration: Date.now() - startTime,
          timestamp: new Date()
        }
      };
    }
  }

  private executeConditionNode(node: ChainNode, inputs: Record<string, any>): any {
    // Safe condition evaluation without eval
    const condition = node.data.content || 'true';
    const input = inputs.input;

    try {
      // Safe evaluation for common conditions
      let result = false;

      if (condition === 'true') {
        result = true;
      } else if (condition === 'false') {
        result = false;
      } else if (condition.includes('input.length > 0')) {
        result = input && input.length > 0;
      } else if (condition.includes('input.length')) {
        const match = condition.match(/input\.length\s*([><=!]+)\s*(\d+)/);
        if (match) {
          const operator = match[1];
          const value = parseInt(match[2]);
          const inputLength = input ? input.length : 0;

          switch (operator) {
            case '>': result = inputLength > value; break;
            case '<': result = inputLength < value; break;
            case '>=': result = inputLength >= value; break;
            case '<=': result = inputLength <= value; break;
            case '==': result = inputLength === value; break;
            case '!=': result = inputLength !== value; break;
            default: result = false;
          }
        }
      } else if (condition.includes('input')) {
        // Basic input existence check
        result = !!input;
      } else {
        // Default to false for unknown conditions
        result = false;
      }

      return {
        true: result ? input : undefined,
        false: result ? undefined : input
      };
    } catch (error) {
      return {
        true: undefined,
        false: input
      };
    }
  }

  private executeOutputNode(node: ChainNode, inputs: Record<string, any>): any {
    const format = node.data.config?.format || 'json';
    const includeMetadata = node.data.config?.includeMetadata || false;
    
    let output = inputs.data;
    
    if (includeMetadata) {
      output = {
        data: output,
        metadata: {
          timestamp: new Date().toISOString(),
          format,
          nodeId: node.id
        }
      };
    }
    
    return { output };
  }

  /**
   * Validate a flow
   */
  validateFlow(flow: ChainFlow): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for orphaned nodes
    const connectedNodes = new Set([
      ...flow.connections.map(c => c.sourceNodeId),
      ...flow.connections.map(c => c.targetNodeId)
    ]);

    const orphanedNodes = flow.nodes.filter(node => 
      node.type !== 'input' && node.type !== 'output' && !connectedNodes.has(node.id)
    );

    if (orphanedNodes.length > 0) {
      errors.push(`Orphaned nodes found: ${orphanedNodes.map(n => n.data.title).join(', ')}`);
    }

    // Check for required inputs
    flow.nodes.forEach(node => {
      const requiredInputs = node.inputs.filter(input => input.required);
      const connectedInputs = flow.connections
        .filter(conn => conn.targetNodeId === node.id)
        .map(conn => conn.targetInputId);

      const missingInputs = requiredInputs.filter(input => 
        !connectedInputs.includes(input.id)
      );

      if (missingInputs.length > 0) {
        errors.push(`Node "${node.data.title}" missing required inputs: ${missingInputs.map(i => i.label).join(', ')}`);
      }
    });

    // Check for cycles
    if (this.hasCycles(flow)) {
      errors.push('Flow contains cycles');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private hasCycles(flow: ChainFlow): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const outgoingConnections = flow.connections.filter(conn => conn.sourceNodeId === nodeId);
      for (const conn of outgoingConnections) {
        if (hasCycle(conn.targetNodeId)) return true;
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of flow.nodes) {
      if (!visited.has(node.id) && hasCycle(node.id)) {
        return true;
      }
    }

    return false;
  }
}

export const chainLinkerService = new ChainLinkerService();
