import { Agent } from '@/store/promptStore';
import { aiService, AIRequest, AIResponse } from './ai-service';

export interface ConversationTurn {
  turnNumber: number;
  agentId: string; // "system" for initial scenario or moderator messages
  agentName: string;
  message: string;
  timestamp: Date;
  tokenCount?: number; // Tokens for this specific turn's generation
}

export interface MultiAgentSimulationRequest {
  participatingAgents: Agent[];
  initialScenario: string;
  maxTurns: number;
  interactionPolicy: 'round-robin'; // Start with round-robin
  // Future: 'moderated', 'dynamic' (agent decides next speaker), etc.
}

export interface MultiAgentSimulationResult {
  id: string;
  timestamp: Date;
  scenario: string;
  conversationHistory: ConversationTurn[];
  summary: string; // Can be a simple concatenation or an LLM-generated summary
  participatingAgents: Agent[]; // Store a snapshot of agents involved
  metadata: {
    totalTurns: number;
    duration: number; // milliseconds
    totalTokens: number;
    // Future: sentiment analysis, conflict detection, etc.
  };
}

class MultiAgentConversationService {
  constructor() {
    // Initialize any necessary state
  }

  public async runSimulation(request: MultiAgentSimulationRequest): Promise<MultiAgentSimulationResult> {
    const startTime = Date.now();
    const conversationHistory: ConversationTurn[] = [];
    let totalTokensUsed = 0;
    let currentTurnNumber = 0;

    // Initial turn: the scenario itself
    conversationHistory.push({
      turnNumber: currentTurnNumber++,
      agentId: 'system',
      agentName: 'Scenario',
      message: request.initialScenario,
      timestamp: new Date(),
    });

    const activeAgents = request.participatingAgents.filter(a => a.enabled);
    if (activeAgents.length === 0) {
      throw new Error("No active agents selected for the simulation.");
    }

    for (let i = 0; i < request.maxTurns * activeAgents.length; i++) {
      const currentAgentIndex = i % activeAgents.length;
      const currentAgent = activeAgents[currentAgentIndex];

      const contextualPrompt = this.buildContextualPrompt(
        currentAgent,
        request.initialScenario,
        conversationHistory
      );

      const aiRequest: AIRequest = {
        provider: currentAgent.modelConfig?.provider || aiService.getDefaultSettings().provider,
        model: currentAgent.modelConfig?.model || aiService.getDefaultSettings().model,
        messages: [
          { role: 'system', content: currentAgent.systemPrompt },
          { role: 'user', content: contextualPrompt },
        ],
        temperature: currentAgent.modelConfig?.temperature ?? aiService.getDefaultSettings().temperature,
        maxTokens: currentAgent.modelConfig?.maxTokens ?? aiService.getDefaultSettings().maxTokens,
      };

      try {
        const aiResponse = await aiService.generateResponse(aiRequest);
        const turnTokens = aiResponse.usage?.totalTokens || 0;
        totalTokensUsed += turnTokens;

        conversationHistory.push({
          turnNumber: currentTurnNumber++,
          agentId: currentAgent.id,
          agentName: currentAgent.name,
          message: aiResponse.content,
          timestamp: new Date(),
          tokenCount: turnTokens,
        });

        // Basic termination condition (e.g., if an agent says "goodbye" or "task complete")
        // This can be made more sophisticated later.
        if (aiResponse.content.toLowerCase().includes("simulation complete") || aiResponse.content.toLowerCase().includes("goodbye")) {
          // console.log(`Agent ${currentAgent.name} indicated simulation completion.`);
          // break;
        }

      } catch (error: any) {
        console.error(`Error during agent ${currentAgent.name}'s turn:`, error);
        conversationHistory.push({
          turnNumber: currentTurnNumber++,
          agentId: currentAgent.id,
          agentName: currentAgent.name,
          message: `[Error: Could not generate response - ${error.message}]`,
          timestamp: new Date(),
        });
        // Optionally, decide if the simulation should stop on agent error
      }
    }

    const duration = Date.now() - startTime;
    // For now, summary is just the last message or a concatenation.
    // Later, this could be an LLM call to summarize the conversationHistory.
    const summary = conversationHistory.length > 1
      ? `Conversation completed after ${conversationHistory.length -1} agent turns. Last message: "${conversationHistory[conversationHistory.length-1].message}"`
      : "No conversation generated.";


    return {
      id: `multi_agent_sim_${Date.now()}`,
      timestamp: new Date(),
      scenario: request.initialScenario,
      conversationHistory,
      summary,
      participatingAgents: request.participatingAgents.map(a => ({...a})), // Store a copy
      metadata: {
        totalTurns: conversationHistory.length -1, // Exclude initial scenario turn
        duration,
        totalTokens: totalTokensUsed,
      },
    };
  }

  private buildContextualPrompt(
    agent: Agent,
    initialScenario: string,
    history: ConversationTurn[]
  ): string {
    // Simple implementation: Use the last N turns.
    // More sophisticated: Summarize, select relevant turns, etc.
    const maxHistoryTurns = 5; // Number of past turns to include
    const recentHistory = history.slice(-maxHistoryTurns);

    let prompt = `You are ${agent.name}, a ${agent.role}. Your personality is: ${agent.personality}.\n`;
    prompt += `Your expertise includes: ${agent.expertise?.join(', ') || 'general knowledge'}.\n\n`;
    prompt += `Initial Scenario: "${initialScenario}"\n\n`;
    prompt += "Current Conversation (last few turns):\n";

    if (recentHistory.length === 1 && recentHistory[0].agentId === 'system') { // Only scenario so far
        prompt += `This is the beginning of the conversation. Please provide your initial response to the scenario based on your role and expertise.\n`;
    } else {
        recentHistory.forEach(turn => {
            if (turn.agentId === 'system') return; // Don't repeat scenario in history section
            prompt += `${turn.agentName} (Turn ${turn.turnNumber}): "${turn.message}"\n`;
        });
        prompt += `\nIt's now your turn, ${agent.name}. Provide your response based on the ongoing conversation, your role, and expertise. Address the last message or the overall state of the discussion as appropriate.`;
    }

    return prompt;
  }
}

export const multiAgentConversationService = new MultiAgentConversationService();
