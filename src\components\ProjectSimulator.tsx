
import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Code, FileText, GitBranch, Clock, CheckCircle, AlertCircle, Play, Plus, Users, Zap } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { usePromptStore } from '@/hooks/usePromptStore';
import { projectSimulationService, ProjectStep } from '@/lib/project-simulation';

interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  projectType: string;
  complexity: 'low' | 'medium' | 'high';
  estimatedTime: number;
}

export const ProjectSimulator = () => {
  const { agents, enabledAgents, prompts, addResult } = usePromptStore();

  const [projectTemplates] = useState<ProjectTemplate[]>([
    {
      id: '1',
      name: 'E-commerce Website',
      description: 'Build a full-stack e-commerce platform with user authentication, product catalog, and payment processing',
      projectType: 'Web Application',
      complexity: 'high',
      estimatedTime: 480
    },
    {
      id: '2',
      name: 'Mobile App MVP',
      description: 'Develop a minimum viable product mobile application with core features',
      projectType: 'Mobile Application',
      complexity: 'medium',
      estimatedTime: 320
    },
    {
      id: '3',
      name: 'API Service',
      description: 'Create a RESTful API service with authentication and data management',
      projectType: 'Backend Service',
      complexity: 'low',
      estimatedTime: 160
    }
  ]);

  const [customProject, setCustomProject] = useState({
    name: '',
    description: '',
    projectType: 'Web Application',
    complexity: 'medium' as 'low' | 'medium' | 'high',
    scenario: ''
  });

  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [selectedPrompts, setSelectedPrompts] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [simulationResults, setSimulationResults] = useState<any>(null);

  const handleRunTemplateSimulation = async (templateId: string) => {
    const template = projectTemplates.find(t => t.id === templateId);
    if (!template) return;

    await runProjectSimulation({
      name: template.name,
      description: template.description,
      projectType: template.projectType,
      complexity: template.complexity
    });
  };

  const handleRunCustomSimulation = async () => {
    if (!customProject.name || !customProject.description) {
      toast({
        title: "Error",
        description: "Please provide project name and description",
        variant: "destructive"
      });
      return;
    }

    await runProjectSimulation(customProject);
  };

  const runProjectSimulation = async (project: {
    name: string;
    description: string;
    projectType: string;
    complexity: 'low' | 'medium' | 'high';
    scenario?: string;
  }) => {
    setIsRunning(true);
    setSimulationResults(null);

    try {
      const selectedAgentObjects = agents.filter(a => selectedAgents.includes(a.id));
      const selectedPromptObjects = prompts.filter(p => selectedPrompts.includes(p.id));

      toast({
        title: "Simulation Started",
        description: `Running project simulation for ${project.name}...`,
      });

      const result = await projectSimulationService.runProjectSimulation({
        name: project.name,
        description: project.description,
        projectType: project.projectType,
        complexity: project.complexity,
        agents: selectedAgentObjects,
        prompts: selectedPromptObjects,
        scenario: project.scenario
      });

      setSimulationResults(result);

      // Add to results store
      const storeResult = {
        timestamp: result.timestamp,
        type: 'project_simulation' as const,
        data: {
          project: {
            name: result.name,
            description: result.description,
            projectType: result.projectType,
            complexity: result.complexity
          },
          steps: result.steps,
          agentContributions: result.agentContributions,
          promptUsage: result.promptUsage,
          insights: result.insights,
          metrics: {
            successRate: result.successRate,
            overallQuality: result.overallQuality,
            estimatedTime: result.totalEstimatedTime,
            actualTime: result.actualTime
          }
        },
        scores: {
          fidelity: result.overallQuality,
          adherence: result.successRate,
          consistency: Math.min(100, result.overallQuality + 10),
          creativity: Math.max(60, result.overallQuality - 10),
          accuracy: result.successRate
        }
      };

      addResult(storeResult);

      toast({
        title: "Simulation Complete",
        description: `Project simulation completed with ${result.successRate.toFixed(1)}% success rate`,
      });
    } catch (error) {
      console.error('Project simulation failed:', error);
      toast({
        title: "Simulation Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsRunning(false);
    }
  };

  const handleAgentToggle = (agentId: string) => {
    setSelectedAgents(prev =>
      prev.includes(agentId)
        ? prev.filter(id => id !== agentId)
        : [...prev, agentId]
    );
  };

  const handlePromptToggle = (promptId: string) => {
    setSelectedPrompts(prev =>
      prev.includes(promptId)
        ? prev.filter(id => id !== promptId)
        : [...prev, promptId]
    );
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'bg-green-600';
      case 'medium': return 'bg-yellow-600';
      case 'high': return 'bg-red-600';
      default: return 'bg-slate-600';
    }
  };

  const getStepIcon = (step: any) => {
    switch (step.status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-400" />;
      case 'in_progress': return <Clock className="w-4 h-4 text-blue-400 animate-pulse" />;
      default: return <Clock className="w-4 h-4 text-slate-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-200 flex items-center gap-3">
          <Code className="w-8 h-8 text-purple-400" />
          Project Simulator
        </h2>
        <Badge variant="outline" className="border-slate-500 text-slate-300">
          AI-Powered Project Simulation
        </Badge>
      </div>

      <Tabs defaultValue="templates" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="custom">Custom Project</TabsTrigger>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid gap-4">
            {projectTemplates.map((template) => (
              <Card key={template.id} className="bg-slate-800/50 border-slate-700 p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Code className="w-6 h-6 text-blue-400" />
                    <div>
                      <h3 className="text-lg font-semibold text-slate-200">{template.name}</h3>
                      <p className="text-sm text-slate-400">{template.description}</p>
                      <div className="flex gap-2 mt-2">
                        <Badge variant="outline" className="border-slate-500 text-slate-300">
                          {template.projectType}
                        </Badge>
                        <Badge className={`${getComplexityColor(template.complexity)} text-white`}>
                          {template.complexity} complexity
                        </Badge>
                        <Badge variant="outline" className="border-slate-500 text-slate-300">
                          ~{Math.round(template.estimatedTime / 8)} weeks
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleRunTemplateSimulation(template.id)}
                    disabled={isRunning}
                    className="bg-purple-600 hover:bg-purple-700"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    {isRunning ? 'Running...' : 'Simulate'}
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-200 mb-4">Create Custom Project</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="project-name" className="text-slate-200">Project Name *</Label>
                <Input
                  id="project-name"
                  value={customProject.name}
                  onChange={(e) => setCustomProject(prev => ({ ...prev, name: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white mt-1"
                  placeholder="Enter project name..."
                />
              </div>

              <div>
                <Label htmlFor="project-description" className="text-slate-200">Project Description *</Label>
                <Textarea
                  id="project-description"
                  value={customProject.description}
                  onChange={(e) => setCustomProject(prev => ({ ...prev, description: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white mt-1 min-h-[100px]"
                  placeholder="Describe your project in detail..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-slate-200">Project Type</Label>
                  <Select value={customProject.projectType} onValueChange={(value) =>
                    setCustomProject(prev => ({ ...prev, projectType: value }))
                  }>
                    <SelectTrigger className="bg-slate-900/50 border-slate-600 text-white mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Web Application">Web Application</SelectItem>
                      <SelectItem value="Mobile Application">Mobile Application</SelectItem>
                      <SelectItem value="Backend Service">Backend Service</SelectItem>
                      <SelectItem value="Desktop Application">Desktop Application</SelectItem>
                      <SelectItem value="Data Pipeline">Data Pipeline</SelectItem>
                      <SelectItem value="Machine Learning">Machine Learning</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-slate-200">Complexity</Label>
                  <Select value={customProject.complexity} onValueChange={(value: 'low' | 'medium' | 'high') =>
                    setCustomProject(prev => ({ ...prev, complexity: value }))
                  }>
                    <SelectTrigger className="bg-slate-900/50 border-slate-600 text-white mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="project-scenario" className="text-slate-200">Additional Scenario (Optional)</Label>
                <Textarea
                  id="project-scenario"
                  value={customProject.scenario}
                  onChange={(e) => setCustomProject(prev => ({ ...prev, scenario: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white mt-1"
                  placeholder="Any specific constraints, requirements, or context..."
                />
              </div>

              <Button
                onClick={handleRunCustomSimulation}
                disabled={isRunning || !customProject.name || !customProject.description}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                size="lg"
              >
                <Play className="w-4 h-4 mr-2" />
                {isRunning ? 'Running Simulation...' : 'Run Custom Simulation'}
              </Button>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="configuration" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Agent Selection */}
            <Card className="bg-slate-800/50 border-slate-700 p-6">
              <div className="flex items-center gap-2 mb-4">
                <Users className="w-5 h-5 text-blue-400" />
                <h3 className="text-lg font-semibold text-slate-200">Select Agents</h3>
              </div>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {agents.map((agent) => (
                  <div key={agent.id} className="flex items-center space-x-3">
                    <Checkbox
                      id={`agent-${agent.id}`}
                      checked={selectedAgents.includes(agent.id)}
                      onCheckedChange={() => handleAgentToggle(agent.id)}
                      className="border-slate-500"
                    />
                    <div className="flex-1">
                      <Label htmlFor={`agent-${agent.id}`} className="text-slate-200 cursor-pointer flex items-center gap-2">
                        <span className="text-lg">{agent.avatar || '🤖'}</span>
                        {agent.name}
                      </Label>
                      <p className="text-xs text-slate-400">{agent.role}</p>
                    </div>
                  </div>
                ))}
                {agents.length === 0 && (
                  <p className="text-slate-400 text-sm">No agents available. Create agents in the Agent Studio.</p>
                )}
              </div>
            </Card>

            {/* Prompt Selection */}
            <Card className="bg-slate-800/50 border-slate-700 p-6">
              <div className="flex items-center gap-2 mb-4">
                <Zap className="w-5 h-5 text-purple-400" />
                <h3 className="text-lg font-semibold text-slate-200">Select Prompts</h3>
              </div>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {prompts.map((prompt) => (
                  <div key={prompt.id} className="flex items-center space-x-3">
                    <Checkbox
                      id={`prompt-${prompt.id}`}
                      checked={selectedPrompts.includes(prompt.id)}
                      onCheckedChange={() => handlePromptToggle(prompt.id)}
                      className="border-slate-500"
                    />
                    <div className="flex-1">
                      <Label htmlFor={`prompt-${prompt.id}`} className="text-slate-200 cursor-pointer">
                        {prompt.name}
                      </Label>
                      <p className="text-xs text-slate-400">{prompt.purpose || 'No purpose defined'}</p>
                    </div>
                  </div>
                ))}
                {prompts.length === 0 && (
                  <p className="text-slate-400 text-sm">No prompts available. Create prompts in the Prompt Variations.</p>
                )}
              </div>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {simulationResults ? (
            <div className="space-y-6">
              {/* Project Overview */}
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <h3 className="text-lg font-semibold text-slate-200 mb-4">Simulation Results: {simulationResults.name}</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{simulationResults.successRate.toFixed(1)}%</div>
                    <div className="text-sm text-slate-400">Success Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">{simulationResults.overallQuality.toFixed(1)}%</div>
                    <div className="text-sm text-slate-400">Quality Score</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">{Math.round(simulationResults.actualTime)}h</div>
                    <div className="text-sm text-slate-400">Actual Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-400">{simulationResults.steps.length}</div>
                    <div className="text-sm text-slate-400">Total Steps</div>
                  </div>
                </div>
              </Card>

              {/* Project Steps */}
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <h3 className="text-lg font-semibold text-slate-200 mb-4">Project Steps</h3>
                <div className="space-y-3">
                  {simulationResults.steps.map((step: any, index: number) => (
                    <div key={step.id} className="flex items-start gap-3 p-3 rounded bg-slate-900/30">
                      {getStepIcon(step)}
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className={`font-medium ${
                            step.status === 'completed' ? 'text-green-300' :
                            step.status === 'failed' ? 'text-red-300' : 'text-slate-300'
                          }`}>
                            {step.name}
                          </h4>
                          <Badge variant={step.status === 'completed' ? 'default' : 'destructive'}>
                            {step.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-slate-400 mt-1">{step.description}</p>
                        {step.result && (
                          <p className="text-sm text-slate-300 mt-2 bg-slate-800 p-2 rounded">
                            {step.result}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Insights */}
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <h3 className="text-lg font-semibold text-slate-200 mb-4">Project Insights</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-green-400 mb-2">Strengths</h4>
                    <ul className="space-y-1">
                      {simulationResults.insights.strengths.map((strength: string, index: number) => (
                        <li key={index} className="text-sm text-slate-300">• {strength}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-yellow-400 mb-2">Recommendations</h4>
                    <ul className="space-y-1">
                      {simulationResults.insights.recommendations.map((rec: string, index: number) => (
                        <li key={index} className="text-sm text-slate-300">• {rec}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </Card>
            </div>
          ) : (
            <Card className="bg-slate-800/50 border-slate-700 p-6">
              <div className="text-center py-8">
                <Code className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-slate-200 mb-2">No Simulation Results</h3>
                <p className="text-slate-400">Run a project simulation to see detailed results here.</p>
              </div>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
