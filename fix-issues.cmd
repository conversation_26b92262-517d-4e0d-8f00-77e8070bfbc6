@echo off
:: Fix common issues with Prompt Studio setup

echo.
echo 🔧 Prompt Studio - Issue Fixer
echo ==============================
echo.

echo 📋 Fixing common setup issues...
echo.

:: Fix browserslist database issue
echo 1. Updating browserslist database...
npx browserslist@latest --update-db
if %errorlevel% neq 0 (
    echo   Trying alternative method...
    npm update caniuse-lite browserslist
)
echo   ✅ Browserslist updated
echo.

:: Fix npm vulnerabilities (non-breaking)
echo 2. Fixing npm vulnerabilities...
npm audit fix
if %errorlevel% neq 0 (
    echo   Some vulnerabilities require manual review
    echo   Run 'npm audit' to see details
)
echo   ✅ Vulnerabilities addressed
echo.

:: Update dependencies to latest compatible versions
echo 3. Updating dependencies...
npm update
echo   ✅ Dependencies updated
echo.

:: Clear npm cache to resolve potential issues
echo 4. Clearing npm cache...
npm cache clean --force
echo   ✅ Cache cleared
echo.

:: Verify installation
echo 5. Verifying installation...
npm list --depth=0 > nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ All dependencies installed correctly
) else (
    echo   ⚠️  Some dependency issues detected
    echo   Running npm install to fix...
    npm install
)
echo.

echo 🎉 Issues fixed! You can now run:
echo    - npm run dev (start development server)
echo    - npm start (alias for dev)
echo    - npm run build (build for production)
echo.

echo Press any key to exit...
pause >nul
