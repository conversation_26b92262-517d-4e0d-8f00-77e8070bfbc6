
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { databaseService } from '../database';

// Mock IndexedDB
const mockIDBDatabase = {
  createObjectStore: vi.fn().mockReturnValue({
    createIndex: vi.fn()
  }),
  transaction: vi.fn(),
  close: vi.fn(),
  objectStoreNames: {
    contains: vi.fn().mockReturnValue(false)
  }
};

const mockIDBTransaction = {
  objectStore: vi.fn().mockReturnValue({
    add: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
    put: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
    get: vi.fn().mockReturnValue({ onsuccess: null, onerror: null, result: null }),
    getAll: vi.fn().mockReturnValue({ onsuccess: null, onerror: null, result: [] }),
    delete: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
    clear: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
    count: vi.fn().mockReturnValue({ onsuccess: null, onerror: null, result: 0 }),
    index: vi.fn().mockReturnValue({
      getAll: vi.fn().mockReturnValue({ onsuccess: null, onerror: null, result: [] })
    })
  })
};

const mockIDBRequest = {
  onsuccess: null,
  onerror: null,
  onupgradeneeded: null,
  result: mockIDBDatabase
};

// Mock global IndexedDB
global.indexedDB = {
  open: vi.fn().mockReturnValue(mockIDBRequest)
} as any;

describe('DatabaseService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);
  });

  afterEach(() => {
    databaseService.close();
  });

  describe('initialize', () => {
    it('should initialize database successfully', async () => {
      const initPromise = databaseService.initialize();
      
      // Simulate successful database opening
      setTimeout(() => {
        if (mockIDBRequest.onsuccess) {
          mockIDBRequest.onsuccess();
        }
      }, 0);

      await expect(initPromise).resolves.toBeUndefined();
      expect(global.indexedDB.open).toHaveBeenCalledWith('PromptStudioDB', 1);
    });

    it('should handle database opening errors', async () => {
      const initPromise = databaseService.initialize();
      
      // Simulate database opening error
      setTimeout(() => {
        if (mockIDBRequest.onerror) {
          mockIDBRequest.onerror();
        }
      }, 0);

      await expect(initPromise).rejects.toThrow('Failed to open database');
    });

    it('should create object stores on upgrade', async () => {
      const initPromise = databaseService.initialize();
      
      // Simulate database upgrade needed
      setTimeout(() => {
        if (mockIDBRequest.onupgradeneeded) {
          mockIDBRequest.onupgradeneeded({ target: mockIDBRequest } as any);
        }
        if (mockIDBRequest.onsuccess) {
          mockIDBRequest.onsuccess();
        }
      }, 0);

      await initPromise;
      
      expect(mockIDBDatabase.createObjectStore).toHaveBeenCalledWith('prompts', { keyPath: 'id' });
      expect(mockIDBDatabase.createObjectStore).toHaveBeenCalledWith('agents', { keyPath: 'id' });
      expect(mockIDBDatabase.createObjectStore).toHaveBeenCalledWith('results', { keyPath: 'id', autoIncrement: true });
    });
  });

  describe('CRUD operations', () => {
    beforeEach(async () => {
      const initPromise = databaseService.initialize();
      setTimeout(() => {
        if (mockIDBRequest.onsuccess) {
          mockIDBRequest.onsuccess();
        }
      }, 0);
      await initPromise;
    });

    it('should add data to store', async () => {
      const testData = { 
        id: '1', 
        name: 'Test Prompt', 
        prompt: 'Test content',
        variables: {},
        purpose: 'Testing',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      const addPromise = databaseService.add('prompts', testData);

      const addRequest = mockIDBTransaction.objectStore().add();
      setTimeout(() => {
        if (addRequest.onsuccess) {
          addRequest.onsuccess();
        }
      }, 0);

      await expect(addPromise).resolves.toBeUndefined();
      expect(mockIDBTransaction.objectStore).toHaveBeenCalledWith('prompts');
    });

    it('should update data in store', async () => {
      const testData = { 
        id: '1', 
        name: 'Updated Prompt', 
        prompt: 'Updated content',
        variables: {},
        purpose: 'Testing',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      const updatePromise = databaseService.update('prompts', testData);

      const putRequest = mockIDBTransaction.objectStore().put();
      setTimeout(() => {
        if (putRequest.onsuccess) {
          putRequest.onsuccess();
        }
      }, 0);

      await expect(updatePromise).resolves.toBeUndefined();
    });

    it('should get data from store', async () => {
      const testData = { id: '1', name: 'Test Prompt' };
      const getPromise = databaseService.get('prompts', '1');

      // Simulate successful get operation
      const getRequest = mockIDBTransaction.objectStore().get();
      getRequest.result = testData;
      setTimeout(() => {
        if (getRequest.onsuccess) {
          getRequest.onsuccess();
        }
      }, 0);

      const result = await getPromise;
      expect(result).toEqual(testData);
    });

    it('should get all data from store', async () => {
      const testData = [
        { id: '1', name: 'Prompt 1' },
        { id: '2', name: 'Prompt 2' }
      ];
      const getAllPromise = databaseService.getAll('prompts');

      // Simulate successful getAll operation
      const getAllRequest = mockIDBTransaction.objectStore().getAll();
      getAllRequest.result = testData;
      setTimeout(() => {
        if (getAllRequest.onsuccess) {
          getAllRequest.onsuccess();
        }
      }, 0);

      const result = await getAllPromise;
      expect(result).toEqual(testData);
    });

    it('should delete data from store', async () => {
      const deletePromise = databaseService.delete('prompts', '1');

      // Simulate successful delete operation
      const deleteRequest = mockIDBTransaction.objectStore().delete();
      setTimeout(() => {
        if (deleteRequest.onsuccess) {
          deleteRequest.onsuccess();
        }
      }, 0);

      await expect(deletePromise).resolves.toBeUndefined();
    });

    it('should clear store', async () => {
      const clearPromise = databaseService.clear('prompts');

      // Simulate successful clear operation
      const clearRequest = mockIDBTransaction.objectStore().clear();
      setTimeout(() => {
        if (clearRequest.onsuccess) {
          clearRequest.onsuccess();
        }
      }, 0);

      await expect(clearPromise).resolves.toBeUndefined();
    });

    it('should count items in store', async () => {
      const countPromise = databaseService.count('prompts');

      // Simulate successful count operation
      const countRequest = mockIDBTransaction.objectStore().count();
      countRequest.result = 5;
      setTimeout(() => {
        if (countRequest.onsuccess) {
          countRequest.onsuccess();
        }
      }, 0);

      const result = await countPromise;
      expect(result).toBe(5);
    });

    it('should query by index', async () => {
      const testData = [{ id: '1', name: 'Test', tags: ['tag1'] }];
      const queryPromise = databaseService.query('prompts', 'tags', 'tag1');

      const indexRequest = mockIDBTransaction.objectStore().index().getAll();
      indexRequest.result = testData;
      setTimeout(() => {
        if (indexRequest.onsuccess) {
          indexRequest.onsuccess();
        }
      }, 0);

      const result = await queryPromise;
      expect(result).toEqual(testData);
    });
  });

  describe('backup operations', () => {
    beforeEach(async () => {
      const initPromise = databaseService.initialize();
      setTimeout(() => {
        if (mockIDBRequest.onsuccess) {
          mockIDBRequest.onsuccess();
        }
      }, 0);
      await initPromise;
    });

    it('should create backup', async () => {
      const mockData = {
        prompts: [{ id: '1', name: 'Test', prompt: 'Test', variables: {}, purpose: 'Test', createdAt: new Date(), updatedAt: new Date() }],
        agents: [{ id: '1', name: 'Agent', role: 'Test', systemPrompt: 'Test', personality: 'Test', expertise: [], enabled: true }],
        results: [],
        settings: [],
        nodeTemplates: [],
        chainFlows: [], // Added
        communityVaultItems: [] // Added
      };

      const originalGetAll = databaseService.getAll;
      databaseService.getAll = vi.fn().mockImplementation((storeName) => {
        return Promise.resolve(mockData[storeName as keyof typeof mockData] || []);
      });

      const addPromise = Promise.resolve();
      databaseService.add = vi.fn().mockResolvedValue(addPromise);

      const backupId = await databaseService.createBackup('Test Backup');
      
      expect(backupId).toMatch(/^backup_\d+$/);
      expect(databaseService.add).toHaveBeenCalledWith('backups', expect.objectContaining({
        id: expect.stringMatching(/^backup_\d+$/), // Expect id to be present and match pattern
        name: 'Test Backup',
        data: mockData,
        size: expect.any(Number) // Expect size to be present
      }));

      databaseService.getAll = originalGetAll;
    });
  });

  describe('settings operations', () => {
    beforeEach(async () => {
      const initPromise = databaseService.initialize();
      setTimeout(() => {
        if (mockIDBRequest.onsuccess) {
          mockIDBRequest.onsuccess();
        }
      }, 0);
      await initPromise;
    });

    it('should get setting by key', async () => {
      const testSetting = { id: 'setting_test', key: 'test', value: 'value' };
      
      databaseService.query = vi.fn().mockResolvedValue([testSetting]);

      const result = await databaseService.getSetting('test');
      expect(result).toEqual(testSetting);
      expect(databaseService.query).toHaveBeenCalledWith('settings', 'key', 'test');
    });

    it('should set setting value', async () => {
      databaseService.add = vi.fn().mockResolvedValue(undefined);
      databaseService.getSetting = vi.fn().mockResolvedValue(undefined);

      await databaseService.setSetting('test', 'value');
      
      expect(databaseService.add).toHaveBeenCalledWith('settings', expect.objectContaining({
        key: 'test',
        value: 'value'
      }));
    });
  });

  describe('error handling', () => {
    it('should handle database not initialized error', async () => {
      // Ensure the db instance is null for this test
      const service = databaseService as any;
      service.close(); // Explicitly close/nullify db for this test
      expect(service.db).toBeNull(); // Verify it's null

      try {
        await databaseService.add('prompts', {
          id: '1',
          name: 'Test',
          prompt: 'Test',
          variables: {},
          purpose: 'Test',
          createdAt: new Date(),
          updatedAt: new Date()
        });
        // Should not reach here
        throw new Error('Promise was not rejected');
      } catch (e:any) {
        expect(e).toBeInstanceOf(Error);
        expect(e.message).toBe('Database not initialized');
      }
    });

    it('should handle operation errors', async () => {
      // Ensure DB is initialized for this test
      const initPromise = databaseService.initialize();
      setTimeout(() => { if (mockIDBRequest.onsuccess) mockIDBRequest.onsuccess(); }, 0);
      await initPromise;
      expect((databaseService as any).db).not.toBeNull(); // Verify db is initialized

      const testData = { 
        id: '1', name: 'Test', prompt: 'Test',
        variables: {}, purpose: 'Test',
        createdAt: new Date(), updatedAt: new Date()
      };

      // Mock the 'add' request to simulate an error
      const mockAddRequest = { onsuccess: null, onerror: null };
      (mockIDBTransaction.objectStore() as any).add = vi.fn().mockReturnValue(mockAddRequest);

      try {
        const addPromise = databaseService.add('prompts', testData); // Call the method

        // Simulate the onerror callback being triggered by IndexedDB
        if (mockAddRequest.onerror) {
          (mockAddRequest.onerror as Function)(new Event('mock_idb_error'));
        } else {
          // This case should ideally not happen if the promise is structured correctly
          // and onerror is always set by the service method.
          throw new Error("mockAddRequest.onerror was not set by DatabaseService.add");
        }

        await addPromise; // Await the promise that should now be rejected
        throw new Error('Promise was not rejected by operation error'); // Force failure if not rejected
      } catch (e: any) {
        expect(e).toBeInstanceOf(Error);
        expect(e.message).toBe('Failed to add to prompts');
      }

      // Restore original mock for add if other tests in this describe block need it
      (mockIDBTransaction.objectStore() as any).add = vi.fn().mockReturnValue({ onsuccess: null, onerror: null });
    });
  });
});
