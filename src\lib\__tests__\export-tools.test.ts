import { describe, it, expect } from 'vitest';
import J<PERSON><PERSON><PERSON> from 'jszip';
import {
  exportToPromptXArchive,
  type StandaloneExportData,
  PROMPT_STUDIO_APP_NAME,
  PROMPT_STUDIO_APP_VERSION
} from '../export-tools';
import type { PromptVariant, Agent, TestResult, TestRunExecutionMetadata } from '../../store/promptStore';

describe('ExportToolsService - exportToPromptXArchive', () => {
  it('should create a valid ZIP archive with manifest for minimal data', async () => {
    const data: StandaloneExportData = {
      prompts: [],
      metadata: { exportDate: new Date().toISOString() }
    };
    const blob = await exportToPromptXArchive(data);
    expect(blob).toBeInstanceOf(Blob);
    expect(blob.type).toBe('application/zip');

    const zip = await JSZip.loadAsync(blob);
    const manifestFile = zip.file('manifest.json');
    expect(manifestFile).not.toBeNull();

    if (manifestFile) {
      const manifestContent = await manifestFile.async('string');
      const manifest = JSON.parse(manifestContent);
      expect(manifest.formatVersion).toBe('1.0.0');
      expect(manifest.appName).toBe(PROMPT_STUDIO_APP_NAME);
      expect(manifest.packMetadata.title).toBe('Untitled Prompt Pack');
      expect(manifest.itemCounts.prompts).toBe(0);
    }
  });

  it('should include prompts with new metadata fields in the archive', async () => {
    const prompt1: PromptVariant = {
      id: 'p1', name: 'Test Prompt 1', prompt: 'Hello {{name}}', variables: { name: 'World' },
      createdAt: new Date(), updatedAt: new Date(), version: 1, lineageId: 'p1',
      // New fields
      promptType: 'greeting',
      definedOutputFormat: 'text',
      tags: ['test', 'basic'],
      usageNotes: 'Simple greeting prompt.',
      targetModels: ['gpt-4o']
    };
    const data: StandaloneExportData = {
      prompts: [prompt1],
      metadata: { exportDate: new Date().toISOString() }
    };

    const blob = await exportToPromptXArchive(data);
    const zip = await JSZip.loadAsync(blob);

    const promptFile = zip.file(`prompts/${prompt1.id}.json`);
    expect(promptFile).not.toBeNull();
    if (promptFile) {
      const promptContent = await promptFile.async('string');
      const parsedPrompt = JSON.parse(promptContent);
      expect(parsedPrompt.id).toBe(prompt1.id);
      expect(parsedPrompt.name).toBe(prompt1.name);
      expect(parsedPrompt.promptType).toBe('greeting');
      expect(parsedPrompt.definedOutputFormat).toBe('text');
      expect(parsedPrompt.tags).toEqual(['test', 'basic']);
      expect(parsedPrompt.usageNotes).toBe('Simple greeting prompt.');
      expect(parsedPrompt.targetModels).toEqual(['gpt-4o']);
      // Ensure dates are stringified
      expect(typeof parsedPrompt.createdAt).toBe('string');
    }
  });

  it('should include packMetadata in manifest.json if provided', async () => {
    const data: StandaloneExportData = {
      prompts: [],
      metadata: {
        exportDate: new Date().toISOString(),
        packTitle: "My Custom Pack",
        packVersion: "1.2.3",
        packDescription: "A special collection.",
        authors: ["Author One"],
        license: "MIT",
        packTags: ["custom", "special"]
      }
    };
    const blob = await exportToPromptXArchive(data);
    const zip = await JSZip.loadAsync(blob);
    const manifestFile = zip.file('manifest.json');
    expect(manifestFile).not.toBeNull();

    if (manifestFile) {
      const manifestContent = await manifestFile.async('string');
      const manifest = JSON.parse(manifestContent);
      expect(manifest.packMetadata.title).toBe("My Custom Pack");
      expect(manifest.packMetadata.packVersion).toBe("1.2.3");
      expect(manifest.packMetadata.description).toBe("A special collection.");
      expect(manifest.packMetadata.authors).toEqual(["Author One"]);
      expect(manifest.packMetadata.license).toBe("MIT");
      expect(manifest.packMetadata.tags).toEqual(["custom", "special"]);
    }
  });

  it('should include agents and results if provided', async () => {
    const agent1: Agent = {
      id: 'a1', name: 'Test Agent', role: 'Tester', systemPrompt: 'You are a test agent.',
      personality: 'Testy', enabled: true
    };
    const result1: TestResult = {
      id: 'r1', timestamp: new Date(), type: 'prompt_test', data: { info: 'test data' },
      runMetadata: { testGoal: 'Verify export' }
    };
    const data: StandaloneExportData = {
      prompts: [],
      agents: [agent1],
      results: [result1],
      metadata: { exportDate: new Date().toISOString() }
    };

    const blob = await exportToPromptXArchive(data);
    const zip = await JSZip.loadAsync(blob);

    expect(zip.file(`agents/${agent1.id}.json`)).not.toBeNull();
    expect(zip.file(`results/${result1.id}.json`)).not.toBeNull();

    const resultFile = zip.file(`results/${result1.id}.json`);
    if (resultFile) {
        const resultJson = JSON.parse(await resultFile.async("string"));
        expect(resultJson.runMetadata?.testGoal).toBe("Verify export");
        expect(typeof resultJson.timestamp).toBe("string");
    }
  });

  it('should ensure metadata integrity in a prompt roundtrip (.promptx export and re-parse)', async () => {
    const originalPrompt: PromptVariant = {
      id: 'roundtrip1',
      name: 'Roundtrip Test Prompt',
      prompt: 'Test {{variable}}',
      systemPrompt: 'System for roundtrip',
      variables: { variable: 'value' },
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 2,
      lineageId: 'lineage_abc',
      parentId: 'parent_def',
      purpose: 'Testing metadata roundtrip',
      promptType: 'test_suite_utility',
      definedOutputFormat: 'json_object_string',
      tags: ['roundtrip', 'metadata_check', '#critical'],
      usageNotes: 'This prompt must maintain all its metadata through export/import.',
      targetModels: ['gpt-4o', 'claude-3-sonnet-32k'],
      desiredTone: 'precise_and_technical',
      contextualDepth: 'medium',
      experimentalSubVariants: ['Sub variant text'],
      buildKit: { frame: 'Test Frame', example: 'Test Example'},
      compatibleAgents: ['agent_x']
    };

    const data: StandaloneExportData = {
      prompts: [originalPrompt],
      metadata: { exportDate: new Date().toISOString() }
    };

    const blob = await exportToPromptXArchive(data);
    const zip = await JSZip.loadAsync(blob);
    const promptFile = zip.file(`prompts/${originalPrompt.id}.json`);
    expect(promptFile).not.toBeNull();

    if (promptFile) {
      const promptContent = await promptFile.async('string');
      const parsedPrompt: PromptVariant = JSON.parse(promptContent);

      // Assert all fields, especially new metadata and dates (as strings)
      expect(parsedPrompt.id).toBe(originalPrompt.id);
      expect(parsedPrompt.name).toBe(originalPrompt.name);
      expect(parsedPrompt.prompt).toBe(originalPrompt.prompt);
      expect(parsedPrompt.systemPrompt).toBe(originalPrompt.systemPrompt);
      expect(parsedPrompt.variables).toEqual(originalPrompt.variables);
      expect(parsedPrompt.version).toBe(originalPrompt.version);
      expect(parsedPrompt.lineageId).toBe(originalPrompt.lineageId);
      expect(parsedPrompt.parentId).toBe(originalPrompt.parentId);
      expect(parsedPrompt.purpose).toBe(originalPrompt.purpose);

      // New fields from I1.2 / I3.2
      expect(parsedPrompt.promptType).toBe(originalPrompt.promptType);
      expect(parsedPrompt.definedOutputFormat).toBe(originalPrompt.definedOutputFormat);
      expect(parsedPrompt.tags).toEqual(originalPrompt.tags);
      expect(parsedPrompt.usageNotes).toBe(originalPrompt.usageNotes);
      expect(parsedPrompt.targetModels).toEqual(originalPrompt.targetModels);
      expect(parsedPrompt.desiredTone).toBe(originalPrompt.desiredTone);
      expect(parsedPrompt.contextualDepth).toBe(originalPrompt.contextualDepth);

      // Other existing fields
      expect(parsedPrompt.experimentalSubVariants).toEqual(originalPrompt.experimentalSubVariants);
      expect(parsedPrompt.buildKit).toEqual(originalPrompt.buildKit);
      expect(parsedPrompt.compatibleAgents).toEqual(originalPrompt.compatibleAgents);

      // Dates are stringified
      expect(parsedPrompt.createdAt).toBe(originalPrompt.createdAt?.toISOString());
      expect(parsedPrompt.updatedAt).toBe(originalPrompt.updatedAt?.toISOString());
    }
  });
});
