import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Replaces placeholders in a string with provided variable values.
 * Placeholders can be in the format {{key}} or {key}.
 * If a variable value is an object, it will be JSON.stringified.
 * @param text The text containing placeholders.
 * @param variables A record of variable keys and their values.
 * @returns The text with placeholders replaced.
 */
export function interpolateVariables(text: string, variables?: Record<string, any>): string {
  if (!variables || Object.keys(variables).length === 0) {
    return text;
  }

  let processedText = text;
  for (const [key, value] of Object.entries(variables)) {
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);

    // Ensure the key is properly escaped for use in a RegExp
    const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    const patterns = [
      new RegExp(`\\{\\{${escapedKey}\\}\\}`, 'g'), // Double curly braces
      new RegExp(`\\{${escapedKey}\\}`, 'g')    // Single curly braces
    ];

    for (const pattern of patterns) {
      processedText = processedText.replace(pattern, stringValue);
    }
  }
  return processedText;
}
